<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>落地灯@2x</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-471.000000, -224.000000)">
            <g id="落地灯" transform="translate(471.000000, 224.000000)">
                <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="200" height="200"></rect>
                <g id="编组-6" transform="translate(17.582053, 24.335783)" fill="#FFFFFF" stroke="#000000">
                    <circle id="椭圆形" cx="91.4179472" cy="65.664217" r="59.5"></circle>
                    <rect id="矩形" transform="translate(69.495796, 73.270076) rotate(-20.000000) translate(-69.495796, -73.270076) " x="-2.50420442" y="69.770076" width="144" height="7"></rect>
                    <rect id="矩形备份-14" transform="translate(77.454253, 79.175595) rotate(-50.000000) translate(-77.454253, -79.175595) " x="-22.0457474" y="75.6755948" width="199" height="7"></rect>
                    <rect id="矩形备份-15" transform="translate(98.418921, 82.183068) rotate(-120.000000) translate(-98.418921, -82.183068) " x="13.4189207" y="78.6830678" width="170" height="7"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>