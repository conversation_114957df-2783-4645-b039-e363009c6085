<?xml version="1.0" encoding="UTF-8"?>
<svg width="180px" height="60px" viewBox="0 0 180 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>转角窗帘@2x</title>
        <g transform="translate(0.000000, 0.000000)" stroke="#000000"  stroke-width="1" fill="none" fill-rule="evenodd">
            <rect id="矩形" fill="#FFFFFF" x="0.5" y="5.5" width="179" height="3"></rect>
            <rect id="矩形" fill="#FFFFFF" x="1.5" y="5.5" width="3" height="54.5"></rect>
            <rect id="矩形" fill="#FFFFFF" x="13.5" y="8.5" width="3" height="11"></rect>
            <rect id="矩形备份-54" fill="#FFFFFF" x="163.5" y="8.5" width="3" height="11"></rect>
            <path d="M11.75,13.25 C13.0833333,4.41666667 14.5,0 16,0 C18.25,0 16.5,13.25 19.75,13.25 C23,13.25 21.25,0 24,0 C26.75,0 25,13.25 27.5,13.25 C30,13.25 28.25,0 31.75,0 C35.25,0 31.25,13.25 35.25,13.25 C39.25,13.25 36.25,0 40.25,0 C44.25,0 39.5,13.25 44.25,13.25 C49,13.25 45.25,0 49.75,0 C54.25,0 49.25,13.25 53.75,13.25 C58.25,13.25 54.5,0 58.25,0 C62,0 57.75,13.25 62,13.25 C66.25,13.25 62.75,0 66.75,0 C70.75,0 68,13.25 71,13.25" id="路径-4"></path>
            <path d="M108.75,13.25 C110.083333,4.41666667 111.5,0 113,0 C115.25,0 113.5,13.25 116.75,13.25 C120,13.25 118.25,0 121,0 C123.75,0 122,13.25 124.5,13.25 C127,13.25 125.25,0 128.75,0 C132.25,0 128.25,13.25 132.25,13.25 C136.25,13.25 133.25,0 137.25,0 C141.25,0 136.5,13.25 141.25,13.25 C146,13.25 142.25,0 146.75,0 C151.25,0 146.25,13.25 150.75,13.25 C155.25,13.25 151.5,0 155.25,0 C159,0 154.75,13.25 159,13.25 C163.25,13.25 159.75,0 163.75,0 C167.75,0 165,13.25 168,13.25" id="路径-4备份" transform="translate(138.375000, 6.625000) scale(-1, 1) translate(-138.375000, -6.625000) "></path>
        </g>
</svg>