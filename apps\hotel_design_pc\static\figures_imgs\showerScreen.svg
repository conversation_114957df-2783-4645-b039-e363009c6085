<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>淋浴屏风@2x</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="画板" transform="translate(-1571.000000, -624.000000)">
            <g id="淋浴屏风" transform="translate(1571.000000, 624.000000)">
                <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="200" height="200"></rect>
                <g id="编组-19" transform="translate(8.642722, 87.000000)" fill="#FFFFFF" stroke="#000000">
                    <circle id="椭圆形" cx="44.8572779" cy="2.5" r="2"></circle>
                    <circle id="椭圆形备份-11" cx="91.8572779" cy="19.5" r="2"></circle>
                    <circle id="椭圆形备份-12" cx="137.857278" cy="2.5" r="2"></circle>
                    <rect id="矩形" transform="translate(21.357278, 11.000000) rotate(-20.000000) translate(-21.357278, -11.000000) " x="-0.142722056" y="9.5" width="43" height="3"></rect>
                    <rect id="矩形备份-82" transform="translate(114.786390, 11.000000) rotate(-20.000000) translate(-114.786390, -11.000000) " x="93.2863897" y="9.5" width="43" height="3"></rect>
                    <rect id="矩形备份-81" transform="translate(68.071834, 11.000000) scale(-1, 1) rotate(-20.000000) translate(-68.071834, -11.000000) " x="46.5718338" y="9.5" width="43" height="3"></rect>
                    <rect id="矩形备份-83" transform="translate(161.500946, 11.000000) scale(-1, 1) rotate(-20.000000) translate(-161.500946, -11.000000) " x="140.000946" y="9.5" width="43" height="3"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>