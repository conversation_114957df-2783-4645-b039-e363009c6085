﻿{
    "客餐厅": [
        {
            "scheme_room_id": "BKLayoutTest-106",
            "room_id": "106",
            "scheme_id": "BKLayoutTest",
            "room": null,
            "swj_room": {
                "uid": 106,
                "name": "客餐厅",
                "area": 39.08573649996478,
                "wall_list": [
                    {
                        "uid": 37,
                        "start_x": -4423,
                        "start_y": -3575.5,
                        "end_x": -1523,
                        "end_y": -3575.5,
                        "thick": 201,
                        "boundary": [
                            {
                                "start": {
                                    "x": -1523,
                                    "y": -3676
                                },
                                "end": {
                                    "x": -1523,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -1523,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -4423,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -4423,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -4423,
                                    "y": -3676
                                }
                            },
                            {
                                "start": {
                                    "x": -4423,
                                    "y": -3676
                                },
                                "end": {
                                    "x": -1523,
                                    "y": -3676
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 35,
                        "start_x": -4322,
                        "start_y": -3226,
                        "end_x": -4322,
                        "end_y": -3475,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4423,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -4221,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -4221,
                                    "y": -3226
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": -3226
                                },
                                "end": {
                                    "x": -4423,
                                    "y": -3226
                                }
                            },
                            {
                                "start": {
                                    "x": -4423,
                                    "y": -3226
                                },
                                "end": {
                                    "x": -4423,
                                    "y": -3475
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 43,
                        "start_x": -4272,
                        "start_y": 126,
                        "end_x": -4272,
                        "end_y": -3226,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4323,
                                    "y": -3226
                                },
                                "end": {
                                    "x": -4221,
                                    "y": -3226
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": -3226
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 126
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 126
                                },
                                "end": {
                                    "x": -4323,
                                    "y": 126
                                }
                            },
                            {
                                "start": {
                                    "x": -4323,
                                    "y": 126
                                },
                                "end": {
                                    "x": -4323,
                                    "y": -3226
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 50,
                        "start_x": -6121,
                        "start_y": 75.5,
                        "end_x": -4323,
                        "end_y": 75.5,
                        "thick": 101,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4323,
                                    "y": 25
                                },
                                "end": {
                                    "x": -4323,
                                    "y": 126
                                }
                            },
                            {
                                "start": {
                                    "x": -4323,
                                    "y": 126
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 126
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 126
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 25
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 25
                                },
                                "end": {
                                    "x": -4323,
                                    "y": 25
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 15,
                        "start_x": -6222,
                        "start_y": 4427,
                        "end_x": -6222,
                        "end_y": -2974,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -6323,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -6121,
                                    "y": -2974
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 4427
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 4427
                                },
                                "end": {
                                    "x": -6323,
                                    "y": 4427
                                }
                            },
                            {
                                "start": {
                                    "x": -6323,
                                    "y": 4427
                                },
                                "end": {
                                    "x": -6323,
                                    "y": -2974
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 29,
                        "start_x": -6121,
                        "start_y": 2425,
                        "end_x": -5721,
                        "end_y": 2425,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -5721,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -5721,
                                    "y": 2526
                                }
                            },
                            {
                                "start": {
                                    "x": -5721,
                                    "y": 2526
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 2526
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 2526
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 2324
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -5721,
                                    "y": 2324
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 22,
                        "start_x": -6121,
                        "start_y": 4325,
                        "end_x": -4221,
                        "end_y": 4325,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 4224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 4426
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 4426
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 4426
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 4426
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 4224
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 4224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 4224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 36,
                        "start_x": -4321.5,
                        "start_y": 4245,
                        "end_x": -4321.5,
                        "end_y": 1224,
                        "thick": 201,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4422,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 4245
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 4245
                                },
                                "end": {
                                    "x": -4422,
                                    "y": 4245
                                }
                            },
                            {
                                "start": {
                                    "x": -4422,
                                    "y": 4245
                                },
                                "end": {
                                    "x": -4422,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 24,
                        "start_x": -4322,
                        "start_y": 2324,
                        "end_x": -4322,
                        "end_y": 1224,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4423,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 2324
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -4423,
                                    "y": 2324
                                }
                            },
                            {
                                "start": {
                                    "x": -4423,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -4423,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 44,
                        "start_x": -4221,
                        "start_y": 1275,
                        "end_x": -1821,
                        "end_y": 1275,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 18,
                        "start_x": -1922,
                        "start_y": 4724,
                        "end_x": -1922,
                        "end_y": 1326,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -2023,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 4724
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 4724
                                },
                                "end": {
                                    "x": -2023,
                                    "y": 4724
                                }
                            },
                            {
                                "start": {
                                    "x": -2023,
                                    "y": 4724
                                },
                                "end": {
                                    "x": -2023,
                                    "y": 1326
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 47,
                        "start_x": -1821,
                        "start_y": 2375,
                        "end_x": -222,
                        "end_y": 2375,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": -222,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -222,
                                    "y": 2426
                                }
                            },
                            {
                                "start": {
                                    "x": -222,
                                    "y": 2426
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 2426
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 2426
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 2324
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -222,
                                    "y": 2324
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 38,
                        "start_x": -121.5,
                        "start_y": 4224,
                        "end_x": -121.5,
                        "end_y": 1326,
                        "thick": 201,
                        "boundary": [
                            {
                                "start": {
                                    "x": -222,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -21,
                                    "y": 4224
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 4224
                                },
                                "end": {
                                    "x": -222,
                                    "y": 4224
                                }
                            },
                            {
                                "start": {
                                    "x": -222,
                                    "y": 4224
                                },
                                "end": {
                                    "x": -222,
                                    "y": 1326
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 27,
                        "start_x": -923,
                        "start_y": 1325,
                        "end_x": -21,
                        "end_y": 1325,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1426
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1426
                                },
                                "end": {
                                    "x": -923,
                                    "y": 1426
                                }
                            },
                            {
                                "start": {
                                    "x": -923,
                                    "y": 1426
                                },
                                "end": {
                                    "x": -923,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -923,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 41,
                        "start_x": -21,
                        "start_y": 1275,
                        "end_x": 5077,
                        "end_y": 1275,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": 5077,
                                    "y": 1224
                                },
                                "end": {
                                    "x": 5077,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": 5077,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1224
                                },
                                "end": {
                                    "x": 5077,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 46,
                        "start_x": 3828,
                        "start_y": 1224,
                        "end_x": 3828,
                        "end_y": -774,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": 3777,
                                    "y": -774
                                },
                                "end": {
                                    "x": 3879,
                                    "y": -774
                                }
                            },
                            {
                                "start": {
                                    "x": 3879,
                                    "y": -774
                                },
                                "end": {
                                    "x": 3879,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": 3879,
                                    "y": 1224
                                },
                                "end": {
                                    "x": 3777,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": 3777,
                                    "y": 1224
                                },
                                "end": {
                                    "x": 3777,
                                    "y": -774
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 48,
                        "start_x": 2477,
                        "start_y": 175,
                        "end_x": 3777,
                        "end_y": 175,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": 3777,
                                    "y": 124
                                },
                                "end": {
                                    "x": 3777,
                                    "y": 226
                                }
                            },
                            {
                                "start": {
                                    "x": 3777,
                                    "y": 226
                                },
                                "end": {
                                    "x": 2477,
                                    "y": 226
                                }
                            },
                            {
                                "start": {
                                    "x": 2477,
                                    "y": 226
                                },
                                "end": {
                                    "x": 2477,
                                    "y": 124
                                }
                            },
                            {
                                "start": {
                                    "x": 2477,
                                    "y": 124
                                },
                                "end": {
                                    "x": 3777,
                                    "y": 124
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 17,
                        "start_x": 2578,
                        "start_y": 124,
                        "end_x": 2578,
                        "end_y": -4574,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": 2477,
                                    "y": -4574
                                },
                                "end": {
                                    "x": 2679,
                                    "y": -4574
                                }
                            },
                            {
                                "start": {
                                    "x": 2679,
                                    "y": -4574
                                },
                                "end": {
                                    "x": 2679,
                                    "y": 124
                                }
                            },
                            {
                                "start": {
                                    "x": 2679,
                                    "y": 124
                                },
                                "end": {
                                    "x": 2477,
                                    "y": 124
                                }
                            },
                            {
                                "start": {
                                    "x": 2477,
                                    "y": 124
                                },
                                "end": {
                                    "x": 2477,
                                    "y": -4574
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 5,
                        "start_x": -1321,
                        "start_y": -3224.5,
                        "end_x": -2273.5,
                        "end_y": -3224.5,
                        "thick": 501,
                        "boundary": [
                            {
                                "start": {
                                    "x": -2273.5,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -2273.5,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -2273.5,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -1321,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -1321,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -1321,
                                    "y": -2974
                                }
                            },
                            {
                                "start": {
                                    "x": -1321,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -2273.5,
                                    "y": -2974
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 11,
                        "start_x": -1321,
                        "start_y": -3100,
                        "end_x": 2477,
                        "end_y": -3100,
                        "thick": 252,
                        "boundary": [
                            {
                                "start": {
                                    "x": 2477,
                                    "y": -3226
                                },
                                "end": {
                                    "x": 2477,
                                    "y": -2974
                                }
                            },
                            {
                                "start": {
                                    "x": 2477,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -1321,
                                    "y": -2974
                                }
                            },
                            {
                                "start": {
                                    "x": -1321,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -1321,
                                    "y": -3226
                                }
                            },
                            {
                                "start": {
                                    "x": -1321,
                                    "y": -3226
                                },
                                "end": {
                                    "x": 2477,
                                    "y": -3226
                                }
                            }
                        ],
                        "type": "1"
                    }
                ],
                "inner_wall_list": [
                    {
                        "uid": 37,
                        "start_x": -4423,
                        "start_y": -3575.5,
                        "end_x": -1523,
                        "end_y": -3575.5,
                        "thick": 201,
                        "boundary": [
                            {
                                "start": {
                                    "x": -1523,
                                    "y": -3676
                                },
                                "end": {
                                    "x": -1523,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -1523,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -4423,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -4423,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -4423,
                                    "y": -3676
                                }
                            },
                            {
                                "start": {
                                    "x": -4423,
                                    "y": -3676
                                },
                                "end": {
                                    "x": -1523,
                                    "y": -3676
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 35,
                        "start_x": -4322,
                        "start_y": -3226,
                        "end_x": -4322,
                        "end_y": -3475,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4423,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -4221,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -4221,
                                    "y": -3226
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": -3226
                                },
                                "end": {
                                    "x": -4423,
                                    "y": -3226
                                }
                            },
                            {
                                "start": {
                                    "x": -4423,
                                    "y": -3226
                                },
                                "end": {
                                    "x": -4423,
                                    "y": -3475
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 43,
                        "start_x": -4272,
                        "start_y": 126,
                        "end_x": -4272,
                        "end_y": -3226,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4323,
                                    "y": -3226
                                },
                                "end": {
                                    "x": -4221,
                                    "y": -3226
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": -3226
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 126
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 126
                                },
                                "end": {
                                    "x": -4323,
                                    "y": 126
                                }
                            },
                            {
                                "start": {
                                    "x": -4323,
                                    "y": 126
                                },
                                "end": {
                                    "x": -4323,
                                    "y": -3226
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 50,
                        "start_x": -6121,
                        "start_y": 75.5,
                        "end_x": -4323,
                        "end_y": 75.5,
                        "thick": 101,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4323,
                                    "y": 25
                                },
                                "end": {
                                    "x": -4323,
                                    "y": 126
                                }
                            },
                            {
                                "start": {
                                    "x": -4323,
                                    "y": 126
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 126
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 126
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 25
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 25
                                },
                                "end": {
                                    "x": -4323,
                                    "y": 25
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 15,
                        "start_x": -6222,
                        "start_y": 4427,
                        "end_x": -6222,
                        "end_y": -2974,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -6323,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -6121,
                                    "y": -2974
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 4427
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 4427
                                },
                                "end": {
                                    "x": -6323,
                                    "y": 4427
                                }
                            },
                            {
                                "start": {
                                    "x": -6323,
                                    "y": 4427
                                },
                                "end": {
                                    "x": -6323,
                                    "y": -2974
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 29,
                        "start_x": -6121,
                        "start_y": 2425,
                        "end_x": -5721,
                        "end_y": 2425,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -5721,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -5721,
                                    "y": 2526
                                }
                            },
                            {
                                "start": {
                                    "x": -5721,
                                    "y": 2526
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 2526
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 2526
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 2324
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -5721,
                                    "y": 2324
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 22,
                        "start_x": -6121,
                        "start_y": 4325,
                        "end_x": -4221,
                        "end_y": 4325,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 4224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 4426
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 4426
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 4426
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 4426
                                },
                                "end": {
                                    "x": -6121,
                                    "y": 4224
                                }
                            },
                            {
                                "start": {
                                    "x": -6121,
                                    "y": 4224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 4224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 36,
                        "start_x": -4321.5,
                        "start_y": 4245,
                        "end_x": -4321.5,
                        "end_y": 1224,
                        "thick": 201,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4422,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 4245
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 4245
                                },
                                "end": {
                                    "x": -4422,
                                    "y": 4245
                                }
                            },
                            {
                                "start": {
                                    "x": -4422,
                                    "y": 4245
                                },
                                "end": {
                                    "x": -4422,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 24,
                        "start_x": -4322,
                        "start_y": 2324,
                        "end_x": -4322,
                        "end_y": 1224,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -4423,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 2324
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -4423,
                                    "y": 2324
                                }
                            },
                            {
                                "start": {
                                    "x": -4423,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -4423,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 44,
                        "start_x": -4221,
                        "start_y": 1275,
                        "end_x": -1821,
                        "end_y": 1275,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -4221,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -4221,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 18,
                        "start_x": -1922,
                        "start_y": 4724,
                        "end_x": -1922,
                        "end_y": 1326,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -2023,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 4724
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 4724
                                },
                                "end": {
                                    "x": -2023,
                                    "y": 4724
                                }
                            },
                            {
                                "start": {
                                    "x": -2023,
                                    "y": 4724
                                },
                                "end": {
                                    "x": -2023,
                                    "y": 1326
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 47,
                        "start_x": -1821,
                        "start_y": 2375,
                        "end_x": -222,
                        "end_y": 2375,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": -222,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -222,
                                    "y": 2426
                                }
                            },
                            {
                                "start": {
                                    "x": -222,
                                    "y": 2426
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 2426
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 2426
                                },
                                "end": {
                                    "x": -1821,
                                    "y": 2324
                                }
                            },
                            {
                                "start": {
                                    "x": -1821,
                                    "y": 2324
                                },
                                "end": {
                                    "x": -222,
                                    "y": 2324
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 38,
                        "start_x": -121.5,
                        "start_y": 4224,
                        "end_x": -121.5,
                        "end_y": 1326,
                        "thick": 201,
                        "boundary": [
                            {
                                "start": {
                                    "x": -222,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -21,
                                    "y": 4224
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 4224
                                },
                                "end": {
                                    "x": -222,
                                    "y": 4224
                                }
                            },
                            {
                                "start": {
                                    "x": -222,
                                    "y": 4224
                                },
                                "end": {
                                    "x": -222,
                                    "y": 1326
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 27,
                        "start_x": -923,
                        "start_y": 1325,
                        "end_x": -21,
                        "end_y": 1325,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1426
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1426
                                },
                                "end": {
                                    "x": -923,
                                    "y": 1426
                                }
                            },
                            {
                                "start": {
                                    "x": -923,
                                    "y": 1426
                                },
                                "end": {
                                    "x": -923,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -923,
                                    "y": 1224
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 41,
                        "start_x": -21,
                        "start_y": 1275,
                        "end_x": 5077,
                        "end_y": 1275,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": 5077,
                                    "y": 1224
                                },
                                "end": {
                                    "x": 5077,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": 5077,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1326
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1326
                                },
                                "end": {
                                    "x": -21,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": -21,
                                    "y": 1224
                                },
                                "end": {
                                    "x": 5077,
                                    "y": 1224
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 46,
                        "start_x": 3828,
                        "start_y": 1224,
                        "end_x": 3828,
                        "end_y": -774,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": 3777,
                                    "y": -774
                                },
                                "end": {
                                    "x": 3879,
                                    "y": -774
                                }
                            },
                            {
                                "start": {
                                    "x": 3879,
                                    "y": -774
                                },
                                "end": {
                                    "x": 3879,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": 3879,
                                    "y": 1224
                                },
                                "end": {
                                    "x": 3777,
                                    "y": 1224
                                }
                            },
                            {
                                "start": {
                                    "x": 3777,
                                    "y": 1224
                                },
                                "end": {
                                    "x": 3777,
                                    "y": -774
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 48,
                        "start_x": 2477,
                        "start_y": 175,
                        "end_x": 3777,
                        "end_y": 175,
                        "thick": 102,
                        "boundary": [
                            {
                                "start": {
                                    "x": 3777,
                                    "y": 124
                                },
                                "end": {
                                    "x": 3777,
                                    "y": 226
                                }
                            },
                            {
                                "start": {
                                    "x": 3777,
                                    "y": 226
                                },
                                "end": {
                                    "x": 2477,
                                    "y": 226
                                }
                            },
                            {
                                "start": {
                                    "x": 2477,
                                    "y": 226
                                },
                                "end": {
                                    "x": 2477,
                                    "y": 124
                                }
                            },
                            {
                                "start": {
                                    "x": 2477,
                                    "y": 124
                                },
                                "end": {
                                    "x": 3777,
                                    "y": 124
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 17,
                        "start_x": 2578,
                        "start_y": 124,
                        "end_x": 2578,
                        "end_y": -4574,
                        "thick": 202,
                        "boundary": [
                            {
                                "start": {
                                    "x": 2477,
                                    "y": -4574
                                },
                                "end": {
                                    "x": 2679,
                                    "y": -4574
                                }
                            },
                            {
                                "start": {
                                    "x": 2679,
                                    "y": -4574
                                },
                                "end": {
                                    "x": 2679,
                                    "y": 124
                                }
                            },
                            {
                                "start": {
                                    "x": 2679,
                                    "y": 124
                                },
                                "end": {
                                    "x": 2477,
                                    "y": 124
                                }
                            },
                            {
                                "start": {
                                    "x": 2477,
                                    "y": 124
                                },
                                "end": {
                                    "x": 2477,
                                    "y": -4574
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 5,
                        "start_x": -1321,
                        "start_y": -3224.5,
                        "end_x": -2273.5,
                        "end_y": -3224.5,
                        "thick": 501,
                        "boundary": [
                            {
                                "start": {
                                    "x": -2273.5,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -2273.5,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -2273.5,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -1321,
                                    "y": -3475
                                }
                            },
                            {
                                "start": {
                                    "x": -1321,
                                    "y": -3475
                                },
                                "end": {
                                    "x": -1321,
                                    "y": -2974
                                }
                            },
                            {
                                "start": {
                                    "x": -1321,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -2273.5,
                                    "y": -2974
                                }
                            }
                        ],
                        "type": "1"
                    },
                    {
                        "uid": 11,
                        "start_x": -1321,
                        "start_y": -3100,
                        "end_x": 2477,
                        "end_y": -3100,
                        "thick": 252,
                        "boundary": [
                            {
                                "start": {
                                    "x": 2477,
                                    "y": -3226
                                },
                                "end": {
                                    "x": 2477,
                                    "y": -2974
                                }
                            },
                            {
                                "start": {
                                    "x": 2477,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -1321,
                                    "y": -2974
                                }
                            },
                            {
                                "start": {
                                    "x": -1321,
                                    "y": -2974
                                },
                                "end": {
                                    "x": -1321,
                                    "y": -3226
                                }
                            },
                            {
                                "start": {
                                    "x": -1321,
                                    "y": -3226
                                },
                                "end": {
                                    "x": 2477,
                                    "y": -3226
                                }
                            }
                        ],
                        "type": "1"
                    }
                ],
                "window_list": [
                    {
                        "uid": 81,
                        "pos_x": -5372.127,
                        "pos_y": 4325.129,
                        "pos_z": 900,
                        "rotate_z": 3.141592653589793,
                        "length": 1500,
                        "width": 200,
                        "height": 1300,
                        "material_id": "142378863",
                        "wall_uid": 22,
                        "type": "CommonWindow"
                    },
                    {
                        "uid": 83,
                        "pos_x": -3072.127,
                        "pos_y": -3574.871,
                        "pos_z": 900,
                        "rotate_z": 3.141592653589793,
                        "length": 2100,
                        "width": 200,
                        "height": 1300,
                        "material_id": "142378863",
                        "wall_uid": 37,
                        "type": "CommonWindow"
                    }
                ],
                "door_list": [
                    {
                        "uid": 74,
                        "pos_x": 677.878,
                        "pos_y": -3099.83,
                        "pos_z": 0,
                        "rotate_z": 3.141592653589793,
                        "length": 2799.99,
                        "width": 250,
                        "height": 2150,
                        "material_id": "135714326",
                        "wall_uid": 11,
                        "type": "SlidingDoor",
                        "openDirection": 1
                    },
                    {
                        "uid": 77,
                        "pos_x": -6222.124,
                        "pos_y": 3550.129,
                        "pos_z": 0,
                        "rotate_z": -1.5707963267948966,
                        "length": 1050,
                        "width": 200.005,
                        "height": 2150,
                        "material_id": "135723478",
                        "wall_uid": 15,
                        "type": "SingleDoor",
                        "openDirection": 0
                    },
                    {
                        "uid": 86,
                        "pos_x": 3227.873,
                        "pos_y": 1275.129,
                        "pos_z": 0,
                        "rotate_z": 0,
                        "length": 900,
                        "width": 100,
                        "height": 2150,
                        "material_id": "135723478",
                        "wall_uid": 41,
                        "type": "SingleDoor",
                        "openDirection": 0
                    },
                    {
                        "uid": 87,
                        "pos_x": 2027.873,
                        "pos_y": 1275.129,
                        "pos_z": 0,
                        "rotate_z": 0,
                        "length": 900,
                        "width": 100,
                        "height": 2150,
                        "material_id": "135723478",
                        "wall_uid": 41,
                        "type": "SingleDoor",
                        "openDirection": 1
                    },
                    {
                        "uid": 88,
                        "pos_x": -4272.127,
                        "pos_y": -1474.87,
                        "pos_z": 0,
                        "rotate_z": 1.5707963267948966,
                        "length": 1600,
                        "width": 100,
                        "height": 2150,
                        "material_id": "135714326",
                        "wall_uid": 43,
                        "type": "SlidingDoor",
                        "openDirection": 0
                    },
                    {
                        "uid": 89,
                        "pos_x": -3122.127,
                        "pos_y": 1275.12,
                        "pos_z": 0,
                        "rotate_z": 3.141580968951757,
                        "length": 1600,
                        "width": 100,
                        "height": 2150,
                        "material_id": "135714326",
                        "wall_uid": 44,
                        "type": "SlidingDoor",
                        "openDirection": 1
                    },
                    {
                        "uid": 90,
                        "pos_x": 3827.882,
                        "pos_y": -299.94,
                        "pos_z": 0,
                        "rotate_z": -1.5707963267948966,
                        "length": 749.86,
                        "width": 100.017,
                        "height": 2150,
                        "material_id": "135723478",
                        "wall_uid": 46,
                        "type": "SingleDoor",
                        "openDirection": 1
                    },
                    {
                        "uid": 91,
                        "pos_x": -1347.127,
                        "pos_y": 2375.129,
                        "pos_z": 0,
                        "rotate_z": 0,
                        "length": 750,
                        "width": 100,
                        "height": 2150,
                        "material_id": "135723478",
                        "wall_uid": 47,
                        "type": "SingleDoor",
                        "openDirection": 0
                    },
                    {
                        "uid": 92,
                        "pos_x": 3227.882,
                        "pos_y": 175.129,
                        "pos_z": 0,
                        "rotate_z": 3.141592653589793,
                        "length": 900.017,
                        "width": 100,
                        "height": 2150,
                        "material_id": "135723478",
                        "wall_uid": 48,
                        "type": "SingleDoor",
                        "openDirection": 1
                    }
                ],
                "flue_list": [],
                "pipe_list": [],
                "pillar_list": [],
                "boundary": [
                    {
                        "start": {
                            "x": -2273.5,
                            "y": -3475
                        },
                        "end": {
                            "x": -4221,
                            "y": -3475
                        }
                    },
                    {
                        "start": {
                            "x": -4221,
                            "y": -3475
                        },
                        "end": {
                            "x": -4221,
                            "y": 126
                        }
                    },
                    {
                        "start": {
                            "x": -4221,
                            "y": 126
                        },
                        "end": {
                            "x": -6121,
                            "y": 126
                        }
                    },
                    {
                        "start": {
                            "x": -6121,
                            "y": 126
                        },
                        "end": {
                            "x": -6121,
                            "y": 2324
                        }
                    },
                    {
                        "start": {
                            "x": -6121,
                            "y": 2324
                        },
                        "end": {
                            "x": -5721,
                            "y": 2324
                        }
                    },
                    {
                        "start": {
                            "x": -5721,
                            "y": 2324
                        },
                        "end": {
                            "x": -5721,
                            "y": 2526
                        }
                    },
                    {
                        "start": {
                            "x": -5721,
                            "y": 2526
                        },
                        "end": {
                            "x": -6121,
                            "y": 2526
                        }
                    },
                    {
                        "start": {
                            "x": -6121,
                            "y": 2526
                        },
                        "end": {
                            "x": -6121,
                            "y": 4224
                        }
                    },
                    {
                        "start": {
                            "x": -6121,
                            "y": 4224
                        },
                        "end": {
                            "x": -4422,
                            "y": 4224
                        }
                    },
                    {
                        "start": {
                            "x": -4422,
                            "y": 4224
                        },
                        "end": {
                            "x": -4422,
                            "y": 2324
                        }
                    },
                    {
                        "start": {
                            "x": -4422,
                            "y": 2324
                        },
                        "end": {
                            "x": -4423,
                            "y": 2324
                        }
                    },
                    {
                        "start": {
                            "x": -4423,
                            "y": 2324
                        },
                        "end": {
                            "x": -4423,
                            "y": 1224
                        }
                    },
                    {
                        "start": {
                            "x": -4423,
                            "y": 1224
                        },
                        "end": {
                            "x": -1821,
                            "y": 1224
                        }
                    },
                    {
                        "start": {
                            "x": -1821,
                            "y": 1224
                        },
                        "end": {
                            "x": -1821,
                            "y": 2324
                        }
                    },
                    {
                        "start": {
                            "x": -1821,
                            "y": 2324
                        },
                        "end": {
                            "x": -222,
                            "y": 2324
                        }
                    },
                    {
                        "start": {
                            "x": -222,
                            "y": 2324
                        },
                        "end": {
                            "x": -222,
                            "y": 1426
                        }
                    },
                    {
                        "start": {
                            "x": -222,
                            "y": 1426
                        },
                        "end": {
                            "x": -923,
                            "y": 1426
                        }
                    },
                    {
                        "start": {
                            "x": -923,
                            "y": 1426
                        },
                        "end": {
                            "x": -923,
                            "y": 1224
                        }
                    },
                    {
                        "start": {
                            "x": -923,
                            "y": 1224
                        },
                        "end": {
                            "x": 3777,
                            "y": 1224
                        }
                    },
                    {
                        "start": {
                            "x": 3777,
                            "y": 1224
                        },
                        "end": {
                            "x": 3777,
                            "y": 226
                        }
                    },
                    {
                        "start": {
                            "x": 3777,
                            "y": 226
                        },
                        "end": {
                            "x": 2477,
                            "y": 226
                        }
                    },
                    {
                        "start": {
                            "x": 2477,
                            "y": 226
                        },
                        "end": {
                            "x": 2477,
                            "y": -2974
                        }
                    },
                    {
                        "start": {
                            "x": 2477,
                            "y": -2974
                        },
                        "end": {
                            "x": -2273.5,
                            "y": -2974
                        }
                    },
                    {
                        "start": {
                            "x": -2273.5,
                            "y": -2974
                        },
                        "end": {
                            "x": -2273.5,
                            "y": -3475
                        }
                    }
                ],
                "furniture_list": [
                    {
                        "uid": 9627,
                        "room_ind": 106,
                        "pos_x": -4448,
                        "pos_y": 2650,
                        "pos_z": 100,
                        "length": 3000,
                        "width": 50,
                        "height": 2500,
                        "rotate_z": 4.71,
                        "name": "现代背景墙",
                        "material_id": "218177539"
                    },
                    {
                        "uid": 9629,
                        "room_ind": 106,
                        "pos_x": -4492.07,
                        "pos_y": 1867.192,
                        "pos_z": 1480,
                        "length": 831.69,
                        "width": 36.14,
                        "height": 833.9,
                        "rotate_z": 4.71,
                        "name": "现代奶油风挂画",
                        "material_id": "218204382",
                        "public_category": "挂画"
                    },
                    {
                        "uid": 9639,
                        "room_ind": 106,
                        "pos_x": -5272,
                        "pos_y": 4030,
                        "pos_z": 8,
                        "length": 1700,
                        "width": 200,
                        "height": 2800,
                        "rotate_z": 0,
                        "name": "现代双开帘",
                        "material_id": "167755216",
                        "public_category": "双开帘"
                    },
                    {
                        "uid": 9641,
                        "room_ind": 106,
                        "pos_x": 426.101,
                        "pos_y": -1336.618,
                        "pos_z": 2072.089,
                        "length": 719.46,
                        "width": 700.75,
                        "height": 719.64,
                        "rotate_z": 3.141592653589793,
                        "name": "吊灯",
                        "material_id": "240534594",
                        "public_category": "吊灯"
                    },
                    {
                        "uid": 9643,
                        "room_ind": 106,
                        "pos_x": 821.505,
                        "pos_y": -2556.859,
                        "pos_z": 8,
                        "length": 563.52,
                        "width": 556.36,
                        "height": 341.3,
                        "rotate_z": 3.141592653589793,
                        "name": "现代布艺脚踏",
                        "material_id": "145522479",
                        "public_category": "脚踏"
                    },
                    {
                        "uid": 9647,
                        "room_ind": 106,
                        "pos_x": -2871,
                        "pos_y": -1313.5,
                        "pos_z": 8,
                        "length": 2193.46,
                        "width": 1000,
                        "height": 750.28,
                        "rotate_z": 1.57,
                        "name": "ZD175-餐台",
                        "material_id": "240539934",
                        "public_category": "餐桌"
                    },
                    {
                        "uid": 9649,
                        "room_ind": 106,
                        "pos_x": -2210.926,
                        "pos_y": -1751.73,
                        "pos_z": 8,
                        "length": 600,
                        "width": 533.58,
                        "height": 792.36,
                        "rotate_z": -1.57,
                        "name": "PT026-餐椅",
                        "material_id": "150827820",
                        "public_category": "餐椅"
                    },
                    {
                        "uid": 9651,
                        "room_ind": 106,
                        "pos_x": -2210.926,
                        "pos_y": -875.27,
                        "pos_z": 8,
                        "length": 600,
                        "width": 533.58,
                        "height": 792.36,
                        "rotate_z": -1.57,
                        "name": "PT026-餐椅",
                        "material_id": "150827820",
                        "public_category": "餐椅"
                    },
                    {
                        "uid": 9653,
                        "room_ind": 106,
                        "pos_x": -3531.074,
                        "pos_y": -1751.73,
                        "pos_z": 8,
                        "length":600,
                        "width": 533.58,
                        "height": 792.36,
                        "rotate_z": 1.57,
                        "name": "PT026-餐椅",
                        "material_id": "150827820",
                        "public_category": "餐椅"
                    },
                    {
                        "uid": 9655,
                        "room_ind": 106,
                        "pos_x": -3531.074,
                        "pos_y": -875.27,
                        "pos_z": 8,
                        "length":600,
                        "width": 533.58,
                        "height": 792.36,
                        "rotate_z": 1.57,
                        "name": "PT026-餐椅",
                        "material_id": "150827820",
                        "public_category": "餐椅"
                    },
                    {
                        "uid": 9657,
                        "room_ind": 106,
                        "pos_x": -5056.57,
                        "pos_y": 3349.112,
                        "pos_z": 8,
                        "length": 600,
                        "width": 600,
                        "height": 435.7,
                        "rotate_z": 0,
                        "name": "现代大理石边几",
                        "material_id": "128549202",
                        "public_category": "边几"
                    },
                    {
                        "uid": 9661,
                        "room_ind": 106,
                        "pos_x": -4756.57,
                        "pos_y": 3649.112,
                        "pos_z": 443.7,
                        "length": 248.71,
                        "width": 256.92,
                        "height": 459.39,
                        "rotate_z": 4.71,
                        "name": "现代金属台灯",
                        "material_id": "100297529",
                        "public_category": "台灯"
                    },
                    {
                        "uid": 9669,
                        "room_ind": 106,
                        "pos_x": -2871,
                        "pos_y": -1313.5,
                        "pos_z": 758.28,
                        "length": 275.86,
                        "width": 325.04,
                        "height": 488.86,
                        "rotate_z": 1.57,
                        "name": "桌面花艺",
                        "material_id": "169589753",
                        "public_category": "花艺"
                    },
                    {
                        "uid": 9625,
                        "room_ind": 106,
                        "pos_x": 696.439,
                        "pos_y": -1376.9,
                        "pos_z": 8,
                        "length": 1000,
                        "width": 1000.01,
                        "height": 436.69,
                        "rotate_z": 1.57,
                        "name": "ZE169",
                        "material_id": "240523575",
                        "public_category": "茶几"
                    },
                    {
                        "uid": 9665,
                        "room_ind": 106,
                        "pos_x": 732.036,
                        "pos_y": -1401.703,
                        "pos_z": 444.69,
                        "length": 287.87,
                        "width": 256.72,
                        "height": 136.59,
                        "rotate_z": 1.57,
                        "name": "核桃",
                        "material_id": "142456906",
                        "public_category": "食品"
                    },
                    {
                        "uid": 9663,
                        "room_ind": 106,
                        "pos_x": 2177.564,
                        "pos_y": -1983.951,
                        "pos_z": 431.862,
                        "length": 439.82,
                        "width": 264.91,
                        "height": 300.86,
                        "rotate_z": -1.5707963267948966,
                        "name": "书籍杂志",
                        "material_id": "169552654",
                        "public_category": "书籍杂志"
                    },
                    {
                        "uid": 9667,
                        "room_ind": 106,
                        "pos_x": 2145.849,
                        "pos_y": -877.576,
                        "pos_z": 451.78,
                        "length": 439.82,
                        "width": 264.91,
                        "height": 300.86,
                        "rotate_z": -1.5707963267948966,
                        "name": "书籍杂志",
                        "material_id": "169552654",
                        "public_category": "书籍杂志"
                    },
                    {
                        "uid": 9645,
                        "room_ind": 106,
                        "pos_x": 2388.76,
                        "pos_y": -1381.334,
                        "pos_z": 643.78,
                        "length": 1463,
                        "width": 33.86,
                        "height": 886,
                        "rotate_z": -1.5707963267948966,
                        "name": "电视",
                        "material_id": "142420765",
                        "public_category": "电视"
                    },
                    {
                        "uid": 9633,
                        "room_ind": 106,
                        "pos_x": 2433.504,
                        "pos_y": -1436.978,
                        "pos_z": 100,
                        "length": 3168.239,
                        "width": 50,
                        "height": 2500,
                        "rotate_z": -1.5707963267948966,
                        "name": "现代背景墙",
                        "material_id": "218177539",
                        "public_category": "背景墙"
                    },
                    {
                        "uid": 9659,
                        "room_ind": 106,
                        "pos_x": 2192.368,
                        "pos_y": -1381.45,
                        "pos_z": 0,
                        "length": 2015.5,
                        "width": 431.38,
                        "height": 451.78,
                        "rotate_z": -1.5707963267948966,
                        "name": "GT140",
                        "material_id": "240523577",
                        "public_category": "电视柜"
                    },
                    {
                        "uid": 17398,
                        "room_ind": 106,
                        "pos_x": 723.239,
                        "pos_y": -1320.969,
                        "pos_z": 8,
                        "length": 2832.157,
                        "width": 2093.506,
                        "height": 5.108,
                        "rotate_z": 1.5707963267948966,
                        "name": "现代地毯",
                        "material_id": "153697126",
                        "public_category": "地毯"
                    },
                    {
                        "uid": 17410,
                        "room_ind": 106,
                        "pos_x": -3014.308,
                        "pos_y": -1266.859,
                        "pos_z": 1743.743,
                        "length": 1533.92,
                        "width": 204.48,
                        "height": 973.45,
                        "rotate_z": -1.5707963267948966,
                        "name": "轻奢风金属吊灯",
                        "material_id": "121782175",
                        "public_category": "吊灯"
                    },
                    {
                        "uid": 44738,
                        "room_ind": 106,
                        "pos_x": -644.219,
                        "pos_y": -1233.31,
                        "pos_z": 8,
                        "length": 2833.9,
                        "width": 957.34,
                        "height": 817.04,
                        "rotate_z": 1.5707963267948966,
                        "name": "S0353-四人位沙发",
                        "material_id": "240547782",
                        "public_category": "多人沙发"
                    },
                    {
                        "uid": 44739,
                        "room_ind": 106,
                        "pos_x": 522.872,
                        "pos_y": -2894.839,
                        "pos_z": 8,
                        "length": 3671.245,
                        "width": 150.01,
                        "height": 2800,
                        "rotate_z": 3.141592653589793,
                        "name": "现代奶油风窗帘",
                        "material_id": "218208600",
                        "public_category": "双开帘"
                    },
                    {
                        "uid": 44868,
                        "room_ind": 106,
                        "pos_x": -3240,
                        "pos_y": -3217.065,
                        "pos_z": 8,
                        "length": 2000,
                        "width": 390,
                        "height": 889.849,
                        "rotate_z": 3.141592653589793,
                        "name": "现代风边柜",
                        "material_id": "223343513",
                        "public_category": "餐边柜"
                    }
                ],
                "furniture_group_list": [],
                "cabinet_list": [
                    {
                        "uid": 36191,
                        "room_ind": 106,
                        "pos_x": -6122,
                        "pos_y": 120,
                        "pos_z": 0,
                        "length": 2203,
                        "width": 420,
                        "height": 2460,
                        "rotate_z": 90,
                        "name": "鞋柜-2000-3",
                        "material_id": "230054208",
                        "public_category": "鞋柜"
                    }
                ]
            }
        }
    ]
}