.windowContainer {
  position: fixed;
  top: 50px;
  right: 20px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 1000;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.windowHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: move;
  user-select: none;
}

.windowTitle {
  flex: 1;
  margin-right: 8px;
}

.windowControls {
  display: flex;
  gap: 4px;
}

.controlButton {
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 3px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  transition: background-color 0.2s ease;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.3);
}

.controlButton:active {
  background: rgba(255, 255, 255, 0.4);
}

.windowContent {
  width: 100%;
  height: 100%;
  background: #f8fafc;
}

.canvasContainer {
  width: 100%;
  height: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: #f0f0f0;
  overflow: hidden;
  pointer-events: auto;
  position: relative;
  z-index: 1;
}

.canvasContainer canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  pointer-events: auto;
  z-index: 2;
}

/* 最小化状态 */
.windowContainer[style*="display: none"] {
  display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .windowContainer {
    top: 20px;
    right: 10px;
    left: 10px;
    width: calc(100% - 20px) !important;
    height: 250px !important;
  }
} 