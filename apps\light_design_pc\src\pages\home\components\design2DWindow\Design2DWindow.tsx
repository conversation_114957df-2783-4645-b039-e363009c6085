import React, { useEffect, useRef, useState } from 'react';
import { Design2DApiHub } from '@layoutai/design_2d';
import styles from './Design2DWindow.module.css';

interface Design2DWindowProps {
    title?: string;
    width?: number;
    height?: number;
    onClose?: () => void;
}

/**
 * 2D设计窗口  测试外置canvas初始化
 * @param props 
 * @returns 
 */
const Design2DWindow: React.FC<Design2DWindowProps> = ({
    width = 400,
    height = 300,
    onClose
}) => {
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (containerRef.current) {
            const canvas = document.createElement('canvas');
            canvas.id = 'canvas2d-window';
            
            // 设置canvas的尺寸为容器的实际尺寸
            const containerRect = containerRef.current.getBoundingClientRect();
            canvas.width = containerRect.width || width;
            canvas.height = containerRect.height || height;
            
            // 设置canvas样式
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.display = 'block';
            canvas.style.pointerEvents = 'auto'; // 确保鼠标事件能正常工作

            // 使用外部canvas初始化
            Design2DApiHub.instance.initWithExternalCanvas(canvas, containerRef.current);
            
            // 设置初始网格状态
            Design2DApiHub.instance.setGridConfig({
                visible: true,
                size: 200,
                color: '#ffffff',
                backgroundColor: '#eef2f4',
                lineWidth: 1,
            });
        }
    }, []);

    return (
        <div 
            className={styles.windowContainer}
            style={{
                width: width,
                height: height,
                display: 'block'
            }}
        >
            <div className={styles.windowContent}>
                <div
                    ref={containerRef}
                    className={styles.canvasContainer}
                />
            </div>
        </div>
    );
};

export default Design2DWindow; 