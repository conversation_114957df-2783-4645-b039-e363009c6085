# Design 2D Package

这是一个2D设计包，支持内置canvas和外部canvas两种使用方式。

## 基本使用

### 方式一：内置canvas

```typescript
import { Design2DApiHub } from '@your-org/design_2d';

// 初始化内置canvas
Design2DApiHub.instance.init();

// 绑定容器div
const container = document.getElementById('canvas-container') as HTMLDivElement;
Design2DApiHub.instance.initWithInternalCanvas(container);
```

### 方式二：外部canvas

```typescript
import { Design2DApiHub } from '@your-org/design_2d';

// 创建自定义大小的canvas
const canvas = document.createElement('canvas');
canvas.width = 800;
canvas.height = 600;

// 获取容器
const container = document.getElementById('canvas-container') as HTMLDivElement;

// 使用外部canvas初始化
Design2DApiHub.instance.initWithExternalCanvas(canvas, container);

// 设置网格配置
Design2DApiHub.instance.setGridConfig({
    visible: true,
    size: 50,
    color: '#e0e0e0',
    lineWidth: 1,
    backgroundColor: '#ffffff'
});

// 更新画布
Design2DApiHub.instance.updateCanvas(true);
```

## API 说明

### Design2DApiHub

- `init()`: 初始化基础环境（工具类等）
- `initWithInternalCanvas(div: HTMLDivElement)`: 使用内置canvas初始化并绑定容器
- `initWithExternalCanvas(canvas: HTMLCanvasElement, div: HTMLDivElement)`: 使用外部canvas初始化
- `setGridConfig(config)`: 设置网格配置
- `updateCanvas(force?: boolean)`: 更新画布
- `updateObject2D(force?: boolean)`: 更新2D对象
- `updateGridAndCanvas(gridConfig?, force?)`: 更新网格和画布
- `dispose()`: 清理资源
- `getDebugInfo()`: 获取调试信息

### Canvas2DManager

- `initWithInternalCanvas()`: 初始化内置canvas
- `initWithExternalCanvas(canvas: HTMLCanvasElement, container: HTMLDivElement)`: 使用外部canvas初始化
- `bindContainer(container: HTMLDivElement)`: 绑定容器（仅内置canvas）
- `startRender()`: 开始渲染
- `stopRender()`: 停止渲染
- `setScale(scale: number)`: 设置缩放
- `getScale()`: 获取当前缩放
- `setOffset(offsetX: number, offsetY: number)`: 设置偏移
- `getOffset()`: 获取当前偏移
- `getTransform()`: 获取当前变换参数
- `centerOrigin()`: 居中原点

## 注意事项

1. 内置canvas会自动添加到容器中，外部canvas需要自己管理DOM
2. 外部canvas需要确保已经设置了正确的尺寸
3. 建议在组件卸载时调用dispose()方法清理资源
4. 初始化顺序：先调用init()初始化基础环境，再调用具体的canvas初始化方法
5. 外部canvas会自动添加到指定的容器中，无需手动管理DOM 