/**
 * 画布事件处理器
 * 负责处理画布的事件监听和管理
 */
export class CanvasEventHandler {
  private _canvas: HTMLCanvasElement | undefined;
  private _ctx: CanvasRenderingContext2D | undefined;

  // 变换回调函数
  private _transformCallback: ((args: { scale?: number, offsetX?: number, offsetY?: number }) => void) | undefined;

  // 事件状态
  private _isDragging = false;
  private _lastMouseX = 0;
  private _lastMouseY = 0;

  // 事件监听器引用，用于清理

  public init(ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement): void {
    this._ctx = ctx;
    this._canvas = canvas;
    this._addMouseEvents();
  }

  public setTransformCallback(callback: (args: { scale?: number, offsetX?: number, offsetY?: number }) => void): void {
    this._transformCallback = callback;
  }

  private _addMouseEvents(): void {
    if (!this._canvas) {
      return;
    }

    // 使用箭头函数绑定事件，确保this指向正确
    this._canvas.addEventListener('mousedown', this._onMouseDown);
    this._canvas.addEventListener('mousemove', this._onMouseMove);
    this._canvas.addEventListener('mouseup', this._onMouseUp);
    this._canvas.addEventListener('mouseleave', this._onMouseLeave);
    this._canvas.addEventListener('wheel', this._onWheel);
    this._canvas.addEventListener('contextmenu', this._onContextMenu);
  }

  private _onContextMenu = (e: MouseEvent): void => {
    // 阻止默认的右键菜单（图片保存等）
    e.preventDefault();
  }

  // 使用箭头函数，避免this指向问题
  private _onMouseDown = (e: MouseEvent): void => {
    this._isDragging = true;
    this._lastMouseX = e.clientX;
    this._lastMouseY = e.clientY;
  }

  private _onMouseMove = (e: MouseEvent): void => {
    if (this._isDragging) {
      const deltaX = e.clientX - this._lastMouseX;
      const deltaY = e.clientY - this._lastMouseY;

      // 通知变换回调
      if (this._transformCallback) {
        this._transformCallback({
          offsetX: deltaX,
          offsetY: deltaY
        });
      }

      this._lastMouseX = e.clientX;
      this._lastMouseY = e.clientY;
    }
  }
  // 鼠标松开事件
  private _onMouseUp = (): void => {
    this._isDragging = false;
  };

  // 鼠标离开事件
  private _onMouseLeave = (): void => {
    this._isDragging = false;
  };

  private _onWheel = (e: WheelEvent): void => {
    e.preventDefault();

    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;

    // 通知变换回调
    if (this._transformCallback) {
      this._transformCallback({
        scale: zoomFactor
      });
    }
  }

  public dispose(): void {
    if (this._canvas) {
      this._canvas.removeEventListener('mousedown', this._onMouseDown);
      this._canvas.removeEventListener('mousemove', this._onMouseMove);
      this._canvas.removeEventListener('mouseup', this._onMouseUp);
      this._canvas.removeEventListener('mouseleave', this._onMouseLeave);
      this._canvas.removeEventListener('wheel', this._onWheel);
      this._canvas.removeEventListener('contextmenu', this._onContextMenu);
      this._canvas = undefined;
    }
    // 移除所有事件监听器

    this._ctx = undefined;
    this._transformCallback = undefined;
    this._isDragging = false;
  }
} 