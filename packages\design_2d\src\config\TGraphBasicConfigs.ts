
/**
 *   一些布局图谱相关的基本（常见)配置
 */

export class TGraphBasicConfigs {

    static MainCabinetsCategories: string[] = ["衣柜", "餐边柜", "电视柜", "玄关柜",
        "浴缸", "钻石形淋浴房", "一字型淋浴房", "钻石形淋浴房", "一字形淋浴房", "弧形淋浴房", "矩形淋浴房",
        "吊柜", "地柜", "洗衣机柜", "浴室柜", "高柜", "书柜", "地柜见光板", "吊柜收口板",
        "地柜收口板", "吊柜收口板", "地柜收口板", "假门地柜"];
    /**
     * 
     */
    static MainEleCategories: string[] = ["床", "电视", "梳妆台", "沙发", "多人沙发", "直排沙发", "转角沙发", "马桶", "餐桌", "淋浴房", "梳妆台", "书桌"];


    static MainOcclusionCategories: string[] = [...TGraphBasicConfigs.MainCabinetsCategories, "床", "梳妆台", "沙发", "多人沙发", "直排沙发", "转角沙发", "马桶", "餐桌",
        "淋浴房", "梳妆台", "书桌", "床头柜", "边几", "茶几"];

    static BedRoomCategories: string[] = [...TGraphBasicConfigs.MainOcclusionCategories, ...TGraphBasicConfigs.MainEleCategories];

    /**
     *  墙面
     */
    static OnwallEleCategories: string[] = ["背景墙", "沙发背景墙", "电视背景墙", "卧室背景墙", "墙画", "挂画", "浴室镜", "梳妆镜", "空调", "花洒", "毛巾架"];

    static OnFloorCategories: string[] = ["茶几", "餐桌", "餐椅", "书椅", "休闲椅", "床", "沙发", "多人沙发", "直排沙发", "转角沙发", "毛巾架", "马桶"];

    /**
     *  窗帘类
     */
    static OnWindowsEleCategories: string[] = ["双开帘", "单开帘", "窗帘", "百叶窗帘"];

    /**
     *  其他类
     */
    static OtherEleCategories: string[] = ["绿植", "雕塑", "衣帽架", "折叠类", "插座", "地毯", "书籍杂志", "花艺", "化妆品", "首饰", "床头柜", "床尾凳", "梳妆镜", "凳子", "脚踏"];

    //用于记录所有有收口方向属性的柜体类别
    static CloseDirectionCategories: string[] = ["衣柜", "玄关柜", "餐边柜", "电视柜"];

    static DecorationCategories: string[] = ["绿植", "墙画", "墙饰", "书记杂志", "化妆品", "首饰", "办公文具"];
    static LightingCategories: string[] = ["吊灯", "吸顶灯", "筒灯", "主灯", "吸顶灯"];

    static KitchenCabinetsCategories: string[] = ["地柜", "消毒柜", "消毒地柜", "水槽地柜", "消毒地柜", "炉灶地柜",
        "转角炉灶地柜", "转角水槽地柜", "拉篮地柜",
        "调味拉篮地柜", "抽屉地柜", "工具拉篮地柜", "碗碟拉篮地柜", "米箱地柜", "单门地柜", "双门地柜", "转角地柜", "假门地柜",
        "假门柜", "冰箱", "地柜收口板", "地柜见光板", "地柜收口板", "吊柜", "烟机吊柜", "单门吊柜", "双门吊柜", "吊柜收口板", "吊柜见光板",
        "吊柜收口板"];
    static OnDrawingLevels: string[][] = [
        ["地毯", "背景墙", "一字形淋浴房", "弧形淋浴房", "钻石形淋浴房", "矩形淋浴房", "Default"],
        ["休闲椅", "餐椅", "脚踏", "落地灯", "绿植", "雕塑", "马桶", "书椅", "床头柜", "梳妆凳", "床尾凳", "凳子"],
        ["床", "餐桌", "矩形茶几", "圆形茶几", "矩形边几", "圆形边几", "茶几", "边几", "书桌", "直排沙发", "多人沙发", "单人沙发", "转角沙发", "梳妆台"],
        ["电视柜", "餐边柜", "玄关柜", "洗衣机柜", "浴室柜", "左收口板", "右收口板", "衣柜", "书柜"],
        TGraphBasicConfigs.KitchenCabinetsCategories,
        ["花洒", "毛巾架", "墙画", "墙饰", "书记杂志", "化妆品", "首饰", "办公文具", "餐具饰品", "餐桌饰品", "饰品", "茶几饰品", "电视柜饰品", "餐边柜饰品", "书桌饰品", "电视"],
        ["主灯", "床头吊灯", "吊灯", "多头吊灯", "射灯", "筒灯", "窗帘"]
    ]

    /**
     *  可放缩类别
     */
    static ScaleAbleCategories: string[] = [...TGraphBasicConfigs.MainCabinetsCategories, ...TGraphBasicConfigs.OnWindowsEleCategories,
        "地毯", "毛巾架", "吊柜收口板", "地柜收口板", "吊柜收口板", "地柜收口板", "假门地柜", "背景墙", "沙发背景墙", "电视背景墙", "卧室背景墙"];

    /**
     * 可移动图元
     */
    static MoveableCategories: string[] = ["休闲椅", "餐椅", "脚踏", "书椅", "凳子", "长条凳"];

    /**
     *  所有类别
     */
    static AllCategories: string[] = [...TGraphBasicConfigs.MainCabinetsCategories, ...TGraphBasicConfigs.MainEleCategories, ...TGraphBasicConfigs.OnwallEleCategories,
    ...TGraphBasicConfigs.OnWindowsEleCategories, ...TGraphBasicConfigs.OtherEleCategories, ...TGraphBasicConfigs.OnFloorCategories, ...TGraphBasicConfigs.DecorationCategories, ...TGraphBasicConfigs.LightingCategories];

    /**
     *  可放缩类别
     */
    static DepthScaleAbleCategories: string[] = [...TGraphBasicConfigs.MainCabinetsCategories, "地毯", "钻石形淋浴房", "地柜见光板", "假门地柜"];

    static GlobalDefaultValues = {
        floor_cabinet_depth: 560,
        hangon_cabinet_depth: 375,
        high_cabinet_depth: 750,

        floor_cabinet_min_z: 100,
        hangon_cabinet_min_z: 1680,
        high_cabinet_min_z: 100,

        floor_cabinet_height: 710,
        hangon_cabinet_height: 704,
        high_cabinet_height: 2400,

    }

    static SpaceAreaBasicParams = {
        min_hallway_depth: 900,
        min_cabinet_depth: 450,
        min_wardrobe_depth: 600,
        min_cupboard_depth: 600,
        min_tv_cabinet_length: 1200,
        min_sofa_length: 2100,
        min_sofa_depth: 1000,

    }

    /**
     *  设置图元的 离地高
     */
    static FigureMinZDict: { [key: string]: number } = {
        "电视": 800,
        "毛巾架": 1300,
        "主灯": 2100,
        "筒灯": 2500
    }

    /**
     *  设置图元的 默认高度
     */
    static FigureDefaultHeightDict: { [key: string]: number } = {
        "毛巾架": 450,
        "马桶": 750,
        "主灯": 700,
        "筒灯": 300

    }
    /**
     *   一些禁用组合
     */
    static UnvalidGroupCodes: { [key: string]: number } = {
        "床-FR_床头柜-FR_床头柜": 1,  // 1代表禁用, 这里只填1
        "床-FL_边几-FR_边几-FR_边几": 1,
        "茶几-FL_餐椅-FC_边几-FR_餐椅": 1,
        "床-FR_儿童床头柜": 1,
        "床-FL_儿童床头柜": 1,
        "高柜": 1,
        "地柜": 1
    }


}

