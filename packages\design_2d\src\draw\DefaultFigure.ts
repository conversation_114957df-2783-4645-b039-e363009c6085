import { DefaultFigureXml } from "./DefaultFigureXml";


/**
* @description 默认图元
* <AUTHOR>
* @date 2025-06-28
* @lastEditTime 2025-06-28 11:51:48
* @lastEditors xuld
*/
export class DefaultFigure {

    private static _figure2svgElement: Map<string, SVGSVGElement> = new Map();

    public static loadConfig() {
        let xml_document = new DOMParser().parseFromString(DefaultFigureXml, 'application/xml');

        let svgElements = xml_document.getElementsByTagName('svg');

        for (let svgElement of svgElements) {
            let figureName = svgElement.getAttribute('figure_name');
            if (figureName) {
                this._figure2svgElement.set(figureName, svgElement);
            }
        }
    }

    public static getSVGElement(figureName: string): SVGSVGElement | undefined {
        return this._figure2svgElement.get(figureName);
    }
}
