import { ZRect, ZRectShape } from "z_polygon";

import { I_RectDrawingFunc } from "./TPainter";


const board_thickness = 20;
const border_thickness = 2;
const cloth_thickness = 20;
export var DefaultFigureDrawingFuncs: { [key: string]: I_RectDrawingFunc[] } = {
    "背景墙": [
        {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [parent_rect.clone()];
                return ans_rect;
            }
        }
    ],
    "一字衣柜": [
        {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [parent_rect.clone()];
                // back rect
                let thickness = board_thickness;
                let back_rect = new ZRect(parent_rect._w, thickness);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.updateRect();
                ans_rect.push(back_rect);


                // let side_rect = new ZRect(parent_rect._h - thickness,thickness);
                // side_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                // side_rect.back_center = parent_rect.rightEdge.unprojectEdge2d({x:parent_rect._h/2+thickness/2,y:0});
                // side_rect.updateRect();
                // ans_rect.push(side_rect);


                let line_rect0 = new ZRect(parent_rect._w, border_thickness / 4);
                line_rect0.nor = parent_rect.nor;
                line_rect0.rect_center = parent_rect.backEdge.unprojectEdge2d({ x: (parent_rect._w) / 2, y: -parent_rect.h / 2 });
                ans_rect.push(line_rect0);

                let cloth_rects: ZRect[] = [];

                let gap_thickness = cloth_thickness * 1.2;
                let lw_num = Math.floor(line_rect0.w / (cloth_thickness + gap_thickness));

                let unvalid_ids = [0, 1, 6, 11, 12]
                for (let i = 0; i < lw_num; i++) {
                    if (i < 2 || i > lw_num - 2) continue;
                    if (unvalid_ids.includes(i % 13)) continue;
                    let x_pos = (cloth_thickness + gap_thickness) * i + cloth_thickness / 2;

                    let pos = line_rect0.unproject({ x: -line_rect0.w / 2 + x_pos, y: 0 });

                    let t_rect = new ZRect(cloth_thickness, Math.min(320, parent_rect.h * 0.8));
                    t_rect.nor = line_rect0.nor;
                    t_rect.rect_center = pos;

                    cloth_rects.push(t_rect);

                }

                ans_rect.push(...cloth_rects);
                return ans_rect;
            }
        }
    ],
    "L型衣柜": [
        {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1.,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];
                // back rect
                let back_rect = new ZRect(parent_rect._w, parent_rect.cornerDepth);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.updateRect();
                ans_rect.push(back_rect);

                let side_rect = new ZRect(parent_rect._h, parent_rect.cornerWidth);
                side_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                side_rect.back_center = parent_rect.rightEdge.center;
                side_rect.updateRect();
                ans_rect.push(side_rect);

                return ans_rect;
            }
        },
        {
            fill: "#fff",
            opacity: 1.,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];
                // back rect
                let back_rect = new ZRect(parent_rect._w - border_thickness * 2, parent_rect.cornerDepth - border_thickness);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.updateRect();
                ans_rect.push(back_rect);

                let side_rect = new ZRect(parent_rect._h - border_thickness * 2, parent_rect.cornerWidth - border_thickness);
                side_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                side_rect.back_center = parent_rect.rightEdge.center;
                side_rect.updateRect();
                ans_rect.push(side_rect);

                return ans_rect;
            }
        },
        {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];
                // back rect
                let thickness = board_thickness;
                let back_rect = new ZRect(parent_rect._w, thickness);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.updateRect();
                ans_rect.push(back_rect);


                let side_rect = new ZRect(parent_rect._h - thickness, thickness);
                side_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                side_rect.back_center = parent_rect.rightEdge.unprojectEdge2d({ x: parent_rect._h / 2 + thickness / 2, y: 0 });
                side_rect.updateRect();
                ans_rect.push(side_rect);


                let line_rect0 = new ZRect(parent_rect._w - parent_rect.cornerWidth / 2, border_thickness / 4);
                line_rect0.nor = parent_rect.nor;
                line_rect0.rect_center = parent_rect.backEdge.unprojectEdge2d({ x: (parent_rect._w - parent_rect.cornerWidth / 2) / 2, y: -parent_rect.cornerDepth / 2 });
                ans_rect.push(line_rect0);


                let line_rect1 = new ZRect(parent_rect._h - parent_rect.cornerDepth / 2, border_thickness / 4);
                line_rect1.nor = parent_rect.rightEdge.nor.clone().negate();
                line_rect1.rect_center = parent_rect.rightEdge.unprojectEdge2d({ x: (parent_rect._h - parent_rect.cornerDepth / 2) / 2 + parent_rect.cornerDepth / 2, y: -parent_rect.cornerWidth / 2 });
                ans_rect.push(line_rect1);

                let cloth_rects: ZRect[] = [];

                let gap_thickness = cloth_thickness * 1.2;
                let lw_num = Math.floor(line_rect0.w / (cloth_thickness + gap_thickness));

                let unvalid_ids = [0, 1, 6, 11, 12]
                for (let i = 0; i < lw_num; i++) {
                    if (i < 2 || i > lw_num - 2) continue;
                    if (unvalid_ids.includes(i % 13)) continue;
                    let x_pos = (cloth_thickness + gap_thickness) * i + cloth_thickness / 2;

                    let pos = line_rect0.unproject({ x: -line_rect0.w / 2 + x_pos, y: 0 });

                    let t_rect = new ZRect(cloth_thickness, Math.min(320, parent_rect.cornerDepth * 0.8));
                    t_rect.nor = line_rect0.nor;
                    t_rect.rect_center = pos;

                    cloth_rects.push(t_rect);

                }

                lw_num = Math.floor(line_rect1.w / (cloth_thickness + gap_thickness));
                for (let i = 0; i < lw_num; i++) {
                    if (i < 6 || i > lw_num - 2) continue;
                    if (unvalid_ids.includes(i % 13)) continue;
                    let x_pos = (cloth_thickness + gap_thickness) * i + cloth_thickness / 2;

                    let pos = line_rect1.unproject({ x: -line_rect1.w / 2 + x_pos, y: 0 });

                    let t_rect = new ZRect(cloth_thickness, Math.min(320, parent_rect.cornerWidth * 0.8));
                    t_rect.nor = line_rect1.nor;
                    t_rect.rect_center = pos;

                    cloth_rects.push(t_rect);

                }


                ans_rect.push(...cloth_rects);
                return ans_rect;
            }
        }
    ],
    "一字台面柜": [
        {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1.,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];
                ans_rect.push(parent_rect);
                let back_rect = new ZRect(parent_rect._w, board_thickness);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.updateRect();
                ans_rect.push(back_rect);
                return ans_rect;
            }
        },
        {
            pattern_name: "笔记本电脑",
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];

                let target_rect = new ZRect(450, 260.5);
                target_rect.nor = parent_rect.nor;
                target_rect.rect_center = parent_rect.rect_center;
                ans_rect.push(target_rect);
                return ans_rect;
            }
        }
    ],
    "L型功能衣柜": [
        {
            pattern_name: "一字衣柜",
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];

                let target_rect = new ZRect(parent_rect.w, parent_rect.cornerDepth);
                target_rect.nor = parent_rect.nor.clone();
                target_rect.rect_center = parent_rect.backEdge.unprojectEdge2d({ x: parent_rect.w / 2, y: -parent_rect.cornerDepth / 2 });
                ans_rect.push(target_rect);
                return ans_rect;
            }
        },
        {
            pattern_name: "一字台面柜",
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];

                let target_rect = new ZRect(parent_rect.h - parent_rect.cornerDepth, parent_rect.cornerWidth);
                target_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                target_rect.rect_center = parent_rect.rightEdge.unprojectEdge2d({ x: parent_rect.cornerDepth + (parent_rect.h - parent_rect.cornerDepth) / 2, y: -parent_rect.cornerWidth / 2 });
                ans_rect.push(target_rect);
                return ans_rect;
            }
        }, {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1.,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];
                let side_rect = new ZRect(parent_rect._h, board_thickness);
                side_rect.back_center = parent_rect.rightEdge.center;
                side_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                side_rect.updateRect();
                ans_rect.push(side_rect);
                return ans_rect;
            }
        },
    ],
    "L型功能柜": [
        {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1.,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];
                // back rect
                let back_rect = new ZRect(parent_rect._w, parent_rect.cornerDepth);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.updateRect();
                ans_rect.push(back_rect);

                let side_rect = new ZRect(parent_rect._h, parent_rect.cornerWidth);
                side_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                side_rect.back_center = parent_rect.rightEdge.center;
                side_rect.updateRect();
                ans_rect.push(side_rect);

                return ans_rect;
            }
        },
        {
            fill: "#fff",
            opacity: 1.,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];
                // back rect
                let back_rect = new ZRect(parent_rect._w - border_thickness * 2, parent_rect.cornerDepth - border_thickness);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.updateRect();
                ans_rect.push(back_rect);

                let side_rect = new ZRect(parent_rect._h - border_thickness * 2, parent_rect.cornerWidth - border_thickness);
                side_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                side_rect.back_center = parent_rect.rightEdge.center;
                side_rect.updateRect();
                ans_rect.push(side_rect);

                return ans_rect;
            },

        },
        {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rect: ZRect[] = [];
                // back rect
                let thickness = board_thickness;
                let back_rect = new ZRect(parent_rect._w, thickness);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.updateRect();
                ans_rect.push(back_rect);


                let side_rect = new ZRect(parent_rect._h - thickness, thickness);
                side_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                side_rect.back_center = parent_rect.rightEdge.unprojectEdge2d({ x: parent_rect._h / 2 + thickness / 2, y: 0 });
                side_rect.updateRect();
                ans_rect.push(side_rect);
                return ans_rect;
            }
        },
        {
            pattern_name: "笔记本电脑",
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rects: ZRect[] = [];

                let target_rect = new ZRect(450, 260.5);
                target_rect.nor = parent_rect.nor;
                target_rect.rect_center = parent_rect.backEdge.unprojectEdge2d({ x: parent_rect.w / 2 - parent_rect.cornerWidth / 2, y: -parent_rect.cornerDepth / 2 })
                ans_rects.push(target_rect);
                return ans_rects;

            }
        },
        {
            pattern_name: "书和笔",
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rects: ZRect[] = [];

                let target_rect = new ZRect(450, 260.5);
                target_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                target_rect.rect_center = parent_rect.rightEdge.unprojectEdge2d({ x: parent_rect.h / 2 + parent_rect.cornerDepth / 2, y: -parent_rect.cornerWidth / 2 })
                ans_rects.push(target_rect);
                return ans_rects;

            }
        }
    ],
    "L型岛台组合":
        [
            {
                pattern_name: "岛台",
                drawing_rects_func: (parent_rect: ZRect) => {
                    let ans_rect: ZRect[] = [];
                    let target_rect = new ZRect(parent_rect.w, parent_rect.cornerDepth);
                    target_rect.nor = parent_rect.nor.clone();
                    target_rect.rect_center = parent_rect.backEdge.unprojectEdge2d({ x: parent_rect.w / 2, y: -parent_rect.cornerDepth / 2 });
                    ans_rect.push(target_rect);
                    return ans_rect;
                }
            },
            {
                pattern_name: "餐桌",
                drawing_rects_func: (parent_rect: ZRect) => {
                    let ans_rect: ZRect[] = [];

                    let target_rect = new ZRect(parent_rect.h - parent_rect.cornerDepth, parent_rect.cornerWidth);
                    target_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                    target_rect.rect_center = parent_rect.rightEdge.unprojectEdge2d({ x: parent_rect.cornerDepth + (parent_rect.h - parent_rect.cornerDepth) / 2, y: -parent_rect.cornerWidth / 2 });
                    ans_rect.push(target_rect);
                    return ans_rect;
                }
            },
            {
                stroke: "#2b2b2b",
                fill: "#fff",
                opacity: 1.,
                drawing_rects_func: (parent_rect: ZRect) => {
                    let ans_rect: ZRect[] = [];
                    let side_rect = new ZRect(parent_rect._h, board_thickness);
                    side_rect.back_center = parent_rect.rightEdge.center;
                    side_rect.nor = parent_rect.rightEdge.nor.clone().negate();
                    side_rect.updateRect();
                    ans_rect.push(side_rect);
                    return ans_rect;
                }
            },
        ],
    "一字形床屏": [
        {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rects: ZRect[] = [];
                // back rect
                let thickness = board_thickness;
                let back_rect = new ZRect(parent_rect._w, thickness);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.updateRect();

                let sub_rects: ZRect[] = [];
                let front_thickness = 60;

                let sub_rect_length = 200;
                let num = Math.max(Math.round(parent_rect.w / sub_rect_length), 1);

                sub_rect_length = parent_rect.w / num;

                for (let i = 0; i < num; i++) {

                    let h = front_thickness - thickness;
                    let w = sub_rect_length;
                    let x = -parent_rect._w / 2 + i * w + w / 2;
                    let y = -parent_rect._h / 2 + thickness + h / 2;

                    let s_rect = new ZRectShape(w, h);
                    s_rect.nor = parent_rect.nor;
                    s_rect._rounded_radius = h / 2;
                    s_rect._rounded_indices = [0, 1];
                    s_rect.rect_center = parent_rect.unproject({ x: x, y: y });

                    sub_rects.push(s_rect);
                }
                ans_rects.push(...sub_rects);
                ans_rects.push(back_rect);

                return ans_rects;
            }
        }
    ],
    "L形床屏": [
        {
            stroke: "#2b2b2b",
            fill: "#fff",
            opacity: 1,
            drawing_rects_func: (parent_rect: ZRect) => {
                let ans_rects: ZRect[] = [];
                // back rect
                let thickness = board_thickness;
                let back_rect = new ZRect(parent_rect._w, thickness);
                back_rect.back_center = parent_rect.back_center;
                back_rect.nor = parent_rect.nor;
                back_rect.u_dv = parent_rect.dv;
                back_rect.updateRect();

                let sub_rects: ZRect[] = [];

                let front_thickness = 60;
                let s_rect_length = 200;
                let x_rect_length = s_rect_length;
                let x_num = Math.max(Math.round(parent_rect.w / x_rect_length), 1);
                let y_rect_length = s_rect_length;
                let y_total_length = parent_rect.h - front_thickness;
                let y_num = Math.max(Math.round((y_total_length) / y_rect_length), 1);
                x_rect_length = parent_rect.w / x_num;
                y_rect_length = (y_total_length) / y_num;
                for (let i = 0; i < x_num; i++) {

                    let h = front_thickness - thickness;
                    let w = x_rect_length;
                    let x = -parent_rect._w / 2 + i * w + w / 2;
                    let y = -parent_rect._h / 2 + thickness + h / 2;

                    let s_rect = new ZRectShape(w, h);
                    s_rect.nor = parent_rect.nor;
                    s_rect._rounded_radius = h / 2;
                    s_rect._rounded_indices = [0, 1];
                    s_rect.rect_center = parent_rect.unproject({ x: x, y: y });

                    sub_rects.push(s_rect);
                }
                for (let i = 0; i < y_num; i++) {
                    let h = front_thickness - thickness;
                    let w = y_rect_length;


                    let s_rect = new ZRectShape(w, h);
                    s_rect.nor = parent_rect.dv.clone().negate();
                    s_rect._rounded_radius = h / 2;
                    s_rect._rounded_indices = [0, 1];
                    s_rect.rect_center = back_rect.frontEdge.unprojectEdge2d({ x: h / 2, y: h + w * i + w / 2 });
                    sub_rects.push(s_rect);
                }
                ans_rects.push(...sub_rects);
                ans_rects.push(back_rect);

                return ans_rects;
            }
        }
    ]


}

export function GetDefaultFigureDrawingFunc(labels: string[]) {
    for (let label of labels) {
        if (DefaultFigureDrawingFuncs[label]) {
            return DefaultFigureDrawingFuncs[label];
        }
    }
    return null;
}