
export const FigureDataList = [
    //#region 
    {
        label: '户型',
        child: [
            {
                label: '结构件',

                figureList: [
                    {
                        image: 'square_pillar.svg',
                        png: 'square_pillar.png',
                        title: '方柱',
                        label: 'square_pillar',
                        dragId: "square_pillar",
                        id: 'square_pillar',
                        img: 'static/figures_imgs/square_pillar.svg',
                        icon: 'iconcolumn'
                    },
                    {
                        image: 'platform.svg',
                        png: 'platform.png',
                        title: '地台',
                        label: 'platform',
                        dragId: "platform",
                        id: 'platform',
                        img: 'static/figures_imgs/platform.svg',
                        icon: 'iconplatform1'
                    },

                    {
                        image: 'beam.svg',
                        png: 'beam.png',
                        title: '横梁',
                        dragId: "beam",
                        label: 'beam',
                        id: 'beam',
                        img: 'static/figures_imgs/beam.svg',
                        icon: 'iconbeam'
                    },
                    {
                        image: 'flue.svg',
                        png: 'flue.png',
                        title: '烟道',
                        dragId: "flue",
                        label: 'flue',
                        id: 'flue',
                        img: 'static/figures_imgs/flue.svg',
                        icon: 'iconflue'
                    },
                    {

                        image: 'Phimosis.svg',
                        png: 'Phimosis.png',
                        title: '包管',
                        label: 'Phimosis',
                        dragId: "Phimosis",
                        id: 'Phimosis',
                        img: 'static/figures_imgs/Phimosis.svg',
                        icon: 'iconpackstandpipe'
                    },
                    {
                        image: 'passDoor.svg',
                        png: 'passDoor.png',
                        title: '垭口',
                        dragId: "passDoor",
                        label: 'passDoor',
                        id: 'passDoor',
                        img: 'static/figures_imgs/passDoor.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房', '厨房'],
                        icon: 'iconpass'
                    },
                    {
                        image: 'SlidingDoor.svg',
                        png: 'SlidingDoor.svg',
                        title: '推拉门',
                        dragId: "SlidingDoor",
                        label: 'SlidingDoor',
                        id: 'SlidingDoor',
                        img: 'static/figures_imgs/SlidingDoor.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房', '厨房'],
                        icon: 'iconslidingdoors'
                    },
                    {
                        image: 'OneWindow.svg',
                        png: 'OneWindow.png',
                        title: '一字窗',
                        dragId: "OneWindow",
                        label: 'OneWindow',
                        id: 'OneWindow',
                        img: 'static/figures_imgs/OneWindow.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房', '厨房'],
                        icon: 'iconrectwindows'
                    },
                    {
                        image: 'SingleDoor.svg',
                        png: 'SingleDoor.svg',
                        title: '单开门',
                        dragId: "SingleDoor",
                        label: 'SingleDoor',
                        id: 'SingleDoor',
                        img: 'static/figures_imgs/SingleDoor.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房'],
                        icon: 'iconsinglerdoor'
                    },
                    {
                        image: 'doubledoor.svg',
                        png: 'doubledoor.png',
                        title: '双开门',
                        dragId: "doubledoor",
                        label: 'doubledoor',
                        id: 'doubledoor',
                        img: 'static/figures_imgs/doubledoor.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房', '厨房'],
                        icon: 'icondoubledoor'
                    },
                    {
                        image: 'safetydoor.svg',
                        png: 'safetydoor.png',
                        title: '子母门',
                        dragId: "safetydoor",
                        label: 'safetydoor',
                        id: 'safetydoor',
                        img: 'static/figures_imgs/safetydoor.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房', '厨房'],
                        icon: 'iconunequalleafdoor'
                    },
                    {
                        image: 'BayWindow.svg',
                        png: 'BayWindow.png',
                        title: '飘窗',
                        dragId: "BayWindow",
                        label: 'BayWindow',
                        id: 'BayWindow',
                        img: 'static/figures_imgs/BayWindow.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房', '厨房'],
                        icon: 'iconbaywindow'
                    },
                    {
                        image: 'doorhole.svg',
                        png: 'doorhole.png',
                        title: '门洞',
                        dragId: "doorhole",
                        label: 'doorhole',
                        id: 'doorhole',
                        img: 'static/figures_imgs/doorhole.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房', '厨房'],
                        icon: 'iconarcheddoorway'
                    },
                    {
                        image: 'Railing.svg',
                        png: 'Railing.png',
                        title: '栏杆',
                        dragId: "Railing",
                        label: 'Railing',
                        id: 'Railing',
                        img: 'static/figures_imgs/Railing.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房', '厨房'],
                        icon: 'iconlangan'
                    }
                ]
            },
        ]
    },
    //#endregion

    {
        label: '组合',
        png: 'icon/combination.png',
        child: [
            {
                label: "沙发组合",
                figureList: [
                    {
                        title: '沙发组合-1',
                        group_code: '直排沙发-FC_矩形茶几-FL_休闲椅',
                        label: 'muti_sofa_group-1',
                        dragId: 'muti_sofa_group-1',
                        id: 'muti_sofa_group-1',
                        png: 'combination/sofa-combination-1.png',
                        length: 3100,
                        depth: 2300,
                        roomType: ['客餐厅'],
                    },
                    {
                        title: '沙发组合-2',
                        group_code: '直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅',
                        label: 'muti_sofa_group-2',
                        dragId: 'muti_sofa_group-2',
                        id: 'muti_sofa_group-2',
                        png: 'combination/sofa-combination-2.png',
                        length: 3900,
                        depth: 2300,
                        roomType: ['客餐厅'],
                    },
                    {
                        title: '沙发组合-3',
                        group_code: '直排沙发-FC_矩形茶几',
                        label: 'muti_sofa_group-3',
                        dragId: 'muti_sofa_group-3',
                        id: 'muti_sofa_group-3',
                        png: 'combination/sofa-combination-3.png',
                        length: 2100,
                        depth: 1900,
                        roomType: ['客餐厅'],
                    },
                    {
                        title: '沙发组合-4',
                        group_code: '直排沙发-FC_矩形茶几-FL_脚踏-FR_休闲椅-L_落地灯-R_圆形边几',
                        label: 'muti_sofa_group-4',
                        dragId: 'muti_sofa_group-4',
                        id: 'muti_sofa_group-4',
                        png: 'combination/sofa-combination-4.png',
                        length: 4544, //  落地灯(744)+间距（70）+沙发（3100）+间距（10）+圆形边几（500）
                        depth: 2073, //
                        roomType: ['客餐厅'],
                    },
                    {
                        title: '转角沙发组合-1',
                        group_code: '转角沙发-FC_圆形茶几-FL_休闲椅',
                        label: 'l_sofa_group-2',
                        dragId: 'l_sofa_group-2',
                        id: 'l_sofa_group-2',
                        png: 'combination/zjsofa-combination-1.png',
                        length: 3800,
                        depth: 2300,
                        roomType: ['客餐厅'],
                    },
                ]
            },
            {
                label: "餐桌椅组合",
                figureList: [
                    {
                        title: '餐桌椅组合-1',
                        group_code: '餐桌-FC4_餐椅',
                        label: 'dining_table_group-1',
                        dragId: 'dining_table_group-1',
                        id: 'dining_table_group-1',
                        png: 'combination/dining-set-table-1.png',
                        length: 1500,
                        depth: 1800,
                        roomType: ['客餐厅'],
                    },
                    {
                        title: '餐桌椅组合-2',
                        group_code: '餐桌-FC6_餐椅',
                        label: 'dining_table_group-2',
                        dragId: 'dining_table_group-2',
                        id: 'dining_table_group-2',
                        png: 'combination/dining-set-table-2.png',
                        length: 2400,
                        depth: 1400,
                        roomType: ['客餐厅'],
                    },
                    {
                        title: '餐桌椅组合-3',
                        group_code: '餐桌-FC6T_餐椅',
                        label: 'dining_table_group-3',
                        dragId: 'dining_table_group-3',
                        id: 'dining_table_group-3',
                        png: 'combination/dining-set-table-3.png',
                        length: 2400,
                        depth: 1800,
                        roomType: ['客餐厅'],
                    },
                    {
                        title: '圆桌组合-1',
                        group_code: '圆餐桌-FC6T_餐椅',
                        label: 'round_table_group-1',
                        dragId: 'round_table_group-1',
                        id: 'round_table_group-1',
                        png: 'combination/round_table_group-1.png',
                        length: 3600,
                        depth: 3600,
                        roomType: ['客餐厅'],
                    }
                ]
            },
            {
                label: "床具组合",
                figureList: [
                    {
                        title: '床具组合-1',
                        group_code: '床-FL_床头柜-FR_床头柜',
                        label: '床具组合-1',
                        dragId: '床具组合-1',
                        id: '床具组合-1',
                        png: 'combination/bed-combination-1.png',
                        length: 3000,
                        depth: 2750,
                        roomType: ['卧室'],
                    },
                    {
                        title: '床具组合-2',
                        group_code: '床-FL_床头柜',
                        label: '床具组合-2',
                        dragId: '床具组合-2',
                        id: '床具组合-2',
                        png: 'combination/bed-combination-2.png',
                        length: 2700,
                        depth: 2350,
                        roomType: ['卧室'],
                    },
                    {
                        title: '床具组合-3',
                        group_code: '床-FR_床头柜',
                        label: '床具组合-3',
                        dragId: '床具组合-3',
                        id: '床具组合-3',
                        png: 'combination/bed-combination-3.png',
                        length: 2700,
                        depth: 2350,
                        roomType: ['卧室'],
                    },
                ]

            },
            {
                label: "桌几组合",
                figureList: [
                    {
                        title: '桌几组合-1',
                        group_code: '书桌-FC_书椅',
                        label: '桌几组合-1',
                        dragId: '桌几组合-1',
                        id: '桌几组合-1',
                        png: 'combination/table-combination-1.png',
                        length: 1600,
                        depth: 1200,
                        roomType: ['卧室', '书房'],
                    },
                    {
                        title: '桌几组合-2',
                        group_code: '书桌-FC_餐椅',
                        label: '桌几组合-2',
                        dragId: '桌几组合-2',
                        id: '桌几组合-2',
                        png: 'combination/table-combination-2.png',
                        length: 1600,
                        depth: 1200,
                        roomType: ['卧室', '书房'],
                    },
                    {
                        title: '桌几组合-3',
                        group_code: "书桌-FC_书椅_FL_休闲椅_FR_休闲椅",
                        label: '桌几组合-3',
                        dragId: '桌几组合-3',
                        id: '桌几组合-3',
                        png: 'combination/table-combination-3.png',
                        length: 1600,
                        depth: 1600,
                        roomType: ['卧室', '书房'],
                    },
                    {
                        title: '桌几组合-4',
                        group_code: "圆形茶几-FL_圆形边几",
                        label: '桌几组合-4',
                        dragId: '桌几组合-4',
                        id: '桌几组合-4',
                        png: 'combination/table-combination-4.png',
                        length: 1600,
                        depth: 1600,
                        roomType: ['客餐厅', '卧室', '书房'],
                    }

                ]
            },
            {
                label: "岛台组合",
                figureList: [
                    {
                        title: '岛台组合-1',
                        group_code: '岛台-FC6_餐椅',
                        label: '岛台组合-1',
                        dragId: '岛台组合-1',
                        id: '岛台组合-1',
                        png: 'combination/Island-combination-1.png',
                        length: 4500,
                        depth: 400,
                        roomType: ['客餐厅'],
                    },

                    {
                        image: 'L型岛台组合.svg',
                        title: 'L型岛台组合',
                        group_code: '岛台-L_餐桌_餐椅',
                        label: 'L型岛台组合',
                        dragId: 'L型岛台组合',
                        id: 'L型岛台组合',
                        png: 'L型岛台组合.png',
                        length: 4500,
                        depth: 400,
                        roomType: ['客餐厅'],
                    },
                ]
            },
            {
                label: "榻榻米组合",
                figureList: [
                    {
                        image: 'tatamimat.svg',
                        png: 'tatamimat.png',
                        title: '榻榻米组合',
                        dragId: "tatamimat",
                        label: 'tatamimat',
                        id: 'tatamimat',
                        img: 'static/figures_imgs/tatamimat.svg',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: 'L_tatamimat.svg',
                        png: 'L_tatamimat.png',
                        title: 'L形榻榻米组合',
                        dragId: "L_tatamimat",
                        label: 'L_tatamimat',
                        id: 'L_tatamimat',
                        img: 'static/figures_imgs/L_tatamimat.svg',
                        roomType: ['卧室', '书房'],
                    },
                ]
            },
        ]
    },
    {
        label: '家具',
        png: 'icon/furniture.png',
        child: [
            {
                label: '沙发',
                figureList: [
                    {
                        image: 'Sofa.svg',
                        png: 'Sofa.png',
                        title: '直排沙发',
                        label: 'muti_sofa',
                        dragId: 'muti_sofa',
                        id: 'muti_sofa',
                        img: 'static/figures_imgs/Sofa.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'L_sofa.svg',
                        png: 'L_sofa.png',
                        title: '转角沙发',
                        dragId: "L_sofa",
                        label: 'L_sofa',
                        id: 'L_sofa',
                        img: 'static/figures_imgs/L_sofa.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'cassette.svg',
                        png: 'cassette.png',
                        title: '卡座',
                        dragId: "cassette",
                        label: 'cassette',
                        id: 'cassette',
                        img: 'static/figures_imgs/cassette.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'backless_sofa.svg',
                        png: 'backless_sofa.svg',
                        title: '无靠背沙发',
                        dragId: "backless_sofa",
                        label: 'backless_sofa',
                        id: 'backless_sofa',
                        img: 'static/figures_imgs/backless_sofa.svg',
                        roomType: ['客餐厅', "卧室", "书房"],
                    },
                ]
            },
            {
                label: '桌几',
                figureList: [
                    {
                        image: 'teaTable-y.svg',
                        png: 'teaTable-y.png',
                        title: '圆形茶几',
                        label: 'teaTable-y',
                        dragId: 'teaTable-y',
                        id: 'teaTable-y',
                        img: 'static/figures_imgs/teaTable-y.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'teatable-j.svg',
                        png: 'teatable-j.png',
                        title: '矩形茶几',
                        label: 'teatable-j',
                        dragId: 'teatable-j',
                        id: 'teatable-j',
                        img: 'static/figures_imgs/teatable-j.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'bianji.svg',
                        png: 'bianji.png',
                        title: '矩形边几',
                        label: 'side_table',
                        dragId: 'side_table',
                        id: 'side_table',
                        img: 'static/figures_imgs/bianji.svg',
                        roomType: ['客餐厅', '阳台'],
                    },
                    {
                        image: 'roundTable.svg',
                        png: 'roundTable.png',
                        title: '圆形边几',
                        label: 'round_table',
                        dragId: "round_table",
                        id: 'round_table',
                        img: 'static/figures_imgs/roundTable.svg',
                        roomType: ['客餐厅', '阳台'],
                    },
                    {
                        image: 'board.svg',
                        png: 'board.png',
                        title: '餐桌',
                        label: 'dining_table',
                        dragId: "dining_table",
                        id: 'dining_table',
                        img: 'static/figures_imgs/board.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'roundDinnerTable.svg',
                        png: 'roundDinnerTable.png',
                        title: '圆形餐桌',
                        label: 'roundDinnerTable',
                        dragId: "roundDinnerTable",
                        id: 'roundDinnerTable',
                        img: 'static/figures_imgs/roundDinnerTable.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'desk.svg',
                        png: 'desk.png',
                        title: '书桌',
                        label: 'desk',
                        dragId: "desk",
                        id: 'desk',
                        img: 'static/figures_imgs/desk.svg',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: 'dressingTable.svg',
                        png: 'dressingTable.png',
                        title: '梳妆台',
                        label: 'dressing_table',
                        dragId: "dressing_table",
                        id: 'dressing_table',
                        img: 'static/figures_imgs/dressingTable.svg',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'teatable.svg',
                        png: 'teatables.png',
                        title: '茶台',
                        label: 'teatable',
                        dragId: "teatable",
                        id: 'teatable',
                        img: 'static/figures_imgs/teatable.svg',
                        roomType: ['客餐厅', '书房'],
                    },
                    {
                        image: 'Islandplatform.svg',
                        png: 'Islandplatform.png',
                        title: '岛台',
                        label: 'Islandplatform',
                        dragId: "Islandplatform",
                        id: 'Islandplatform',
                        img: 'static/figures_imgs/Islandplatform.svg',
                        roomType: ['客餐厅'],
                    },
                ]
            },
            {
                label: '床具',
                figureList: [
                    {
                        image: 'bed.svg',
                        png: 'bed.png',
                        title: '床',
                        label: 'bed',
                        dragId: "bed",
                        id: 'bed',
                        img: 'static/figures_imgs/bed.svg',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'bedsideTable.svg',
                        png: 'bedsideTable.png',
                        title: '床头柜',
                        label: 'bedside_table',
                        dragId: "bedside_table",
                        id: 'bedside_table',
                        img: 'static/figures_imgs/bedsideTable.svg',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'bedEndStool.svg',
                        png: 'bedEndStool.png',
                        title: '床尾凳',
                        label: 'bedEndStool',
                        dragId: "bedEndStool",
                        id: 'bedEndStool',
                        img: 'static/figures_imgs/bedEndStool.svg',
                        roomType: ['卧室'],
                    },
                ]
            },
            {
                label: '椅凳',
                figureList: [
                    {
                        image: 'leisureChair.svg',
                        png: 'leisureChair.png',
                        title: '休闲椅',
                        label: 'leisure_chair',
                        dragId: "leisure_chair",
                        id: 'leisure_chair',
                        img: 'static/figures_imgs/leisureChair.svg',
                        roomType: ['客餐厅', '卧室', '阳台'],
                    },
                    {
                        image: 'footPedal.svg',
                        png: 'footPedal.png',
                        title: '脚踏',
                        label: 'foot_pedal',
                        dragId: "foot_pedal",
                        id: 'foot_pedal',
                        img: 'static/figures_imgs/footPedal.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'DiningChair.svg',
                        png: 'DiningChair.png',
                        title: '餐椅',
                        label: 'dining_chair',
                        dragId: "dining_chair",
                        id: 'dining_chair',
                        img: 'static/figures_imgs/DiningChair.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'BookChair.svg',
                        png: 'BookChair.png',
                        title: '书椅',
                        label: 'bookChair',
                        dragId: "bookChair",
                        id: 'bookChair',
                        img: 'static/figures_imgs/BookChair.svg',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: 'stool.svg',
                        png: 'stool.png',
                        title: '凳子',
                        label: 'stool',
                        dragId: "stool",
                        id: 'stool',
                        img: 'static/figures_imgs/stool.svg',
                        roomType: ['客餐厅', '卧室'],
                    },
                    {
                        image: 'bench.svg',
                        png: 'bench.png',
                        title: '长条凳',
                        label: 'bench',
                        dragId: "bench",
                        id: 'bench',
                        img: 'static/figures_imgs/bench.svg',
                        roomType: ['客餐厅', '书房'],
                    },
                ]
            },
            {
                label: '柜架',
                figureList: [
                    {
                        image: 'TVcabinet.svg',
                        png: 'TVcabinet.png',
                        title: '电视柜',
                        label: 'tv_stand',
                        dragId: "tv_stand",
                        id: 'tv_stand',
                        img: 'static/figures_imgs/TVcabinet.svg',
                        roomType: ['客餐厅', '卧室'],
                    },
                    {
                        image: 'mealSideCabinet.svg',
                        png: 'mealSideCabinet.png',
                        title: '餐边柜',
                        label: 'meal_side_cabinet',
                        dragId: "meal_side_cabinet",
                        id: 'meal_side_cabinet',
                        img: 'static/figures_imgs/mealSideCabinet.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'gateCabinet.svg',
                        png: 'gateCabinet.png',
                        title: '玄关柜',
                        label: 'entrance_cabinet',
                        dragId: "entrance_cabinet",
                        id: 'entrance_cabinet',
                        img: 'static/figures_imgs/gateCabinet.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'wardrobe.svg',
                        png: 'wardrobe.png',
                        title: '衣柜',
                        label: 'wardrobe',
                        dragId: "wardrobe",
                        id: 'wardrobe',
                        img: 'static/figures_imgs/wardrobe.svg',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'bookcase.svg',
                        png: 'bookcase.png',
                        title: '书柜',
                        label: 'book_cabinet',
                        dragId: "book_cabinet",
                        id: 'book_cabinet',
                        img: 'static/figures_imgs/bookcase.svg',
                        roomType: ['卧室', '书房'],
                    },
                ]
            },
        ]
    },
    {
        label: '厨卫',
        png: 'icon/kitchen.png',
        child: [
            {
                label: '橱柜',
                figureList: [
                    {
                        "title": "水槽地柜",
                        "image": "sink_floor_cabinet.svg",
                        "png": "sink_floor_cabinet.svg",
                        "label": "水槽地柜",
                        "dragId": "水槽地柜",
                        "id": "水槽地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "炉灶地柜",
                        "image": "stove_top_cabinet.svg",
                        "png": "stove_top_cabinet.svg",
                        "label": "炉灶地柜",
                        "dragId": "炉灶地柜",
                        "id": "炉灶地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "冰箱",
                        "image": "refrigerators.svg",
                        "png": "refrigerators.svg",
                        "label": "冰箱",
                        "dragId": "冰箱",
                        "id": "冰箱",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "假门地柜",
                        "image": "fake_door_cabinet.svg",
                        "png": "fake_door_cabinet.svg",
                        "label": "假门地柜",
                        "dragId": "假门地柜",
                        "id": "假门地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "单门地柜",
                        "image": "single_door_floor_cabinet.svg",
                        "png": "single_door_floor_cabinet.svg",
                        "label": "单门地柜",
                        "dragId": "单门地柜",
                        "id": "单门地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "双门地柜",
                        "image": "double_door_floor_cabinet.svg",
                        "png": "double_door_floor_cabinet.svg",
                        "label": "双门地柜",
                        "dragId": "双门地柜",
                        "id": "双门地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "抽屉地柜",
                        "image": "drawer_cabinet.svg",
                        "png": "drawer_cabinet.svg",
                        "label": "抽屉地柜",
                        "dragId": "抽屉地柜",
                        "id": "抽屉地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "拉篮地柜",
                        "image": "pull_basket_cabinet.svg",
                        "png": "pull_basket_cabinet.svg",
                        "label": "拉篮地柜",
                        "dragId": "拉篮地柜",
                        "id": "拉篮地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "消毒地柜",
                        "image": "disinfection_floor_cabinet.svg",
                        "png": "disinfection_floor_cabinet.svg",
                        "label": "消毒地柜",
                        "dragId": "消毒地柜",
                        "id": "消毒地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "米箱地柜",
                        "image": "rice_chest_floor_cabinet.svg",
                        "png": "rice_chest_floor_cabinet.svg",
                        "label": "米箱地柜",
                        "dragId": "米箱地柜",
                        "id": "米箱地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "转角地柜",
                        "image": "corner_cabinet.svg",
                        "png": "corner_cabinet.svg",
                        "label": "转角地柜",
                        "dragId": "转角地柜",
                        "id": "转角地柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "单门吊柜",
                        "image": "single_door_cabinet.svg",
                        "png": "single_door_cabinet.svg",
                        "label": "单门吊柜",
                        "dragId": "单门吊柜",
                        "id": "单门吊柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "双门吊柜",
                        "image": "double-door_hanging_cabinet.svg",
                        "png": "double-door_hanging_cabinet.svg",
                        "label": "双门吊柜",
                        "dragId": "双门吊柜",
                        "id": "双门吊柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "烟机吊柜",
                        "image": "hood_hanging_cabinet.svg",
                        "png": "hood_hanging_cabinet.svg",
                        "label": "烟机吊柜",
                        "dragId": "烟机吊柜",
                        "id": "烟机吊柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "吊柜收口板",
                        "image": "hanging_cabinet_closing_plate.svg",
                        "png": "hanging_cabinet_closing_plate.svg",
                        "label": "吊柜收口板",
                        "dragId": "吊柜收口板",
                        "id": "吊柜收口板",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "地柜收口板",
                        "image": "floor_cabinet_closing_plate.svg",
                        "png": "floor_cabinet_closing_plate.svg",
                        "label": "地柜收口板",
                        "dragId": "地柜收口板",
                        "id": "地柜收口板",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "高柜",
                        "image": "high_cabinet.svg",
                        "png": "high_cabinet.svg",
                        "label": "高柜",
                        "dragId": "高柜",
                        "id": "高柜",
                        "roomType": [
                            "厨房"
                        ]
                    },
                    {
                        "title": "水吧台",
                        "image": "water_bar.svg",
                        "png": "water_bar.svg",
                        "label": "水吧台",
                        "dragId": "水吧台",
                        "id": "水吧台",
                        "roomType": [
                            "厨房", "卧室", "客餐厅", "卫生间"
                        ]
                    }
                ]
            },
            {
                label: '卫浴',
                figureList: [
                    {
                        image: 'BathroomCabinet.svg',
                        png: 'BathroomCabinet.png',
                        title: '浴室柜',
                        label: 'bathroom_cabinet',
                        dragId: "bathroom_cabinet",
                        id: 'bathroom_cabinet',
                        img: 'static/figures_imgs/BathroomCabinet.svg',
                        roomType: ['卫生间'],
                    },
                    {
                        image: 'closestool.svg',
                        png: 'closestool.png',
                        title: '马桶',
                        label: 'water_closet',
                        dragId: "water_closet",
                        id: 'water_closet',
                        img: 'static/figures_imgs/closestool.svg',
                        roomType: ['卫生间'],
                    },
                    {
                        image: 'showerHead.svg',
                        png: 'showerHead.png',
                        title: '花洒',
                        label: 'shower_head',
                        dragId: "shower_head",
                        id: 'shower_head',
                        img: 'static/figures_imgs/showerHead.svg',
                        roomType: ['卫生间'],
                    },
                    {
                        image: 'towelRack.svg',
                        png: 'towelRack.png',
                        title: '毛巾架',
                        label: 'towel_rack',
                        dragId: "towel_rack",
                        id: 'towel_rack',
                        img: 'static/figures_imgs/towelRack.svg',
                        roomType: ['卫生间'],
                    },
                    {
                        image: 'shower_room_j.svg',
                        png: 'shower_room_j.png',
                        title: '矩形淋浴房',
                        label: 'shower_room_j',
                        dragId: "shower_room_j",
                        id: 'shower_room_j',
                        img: 'static/figures_imgs/fooshower_room_j.svg',
                        roomType: ['卫生间'],
                    },
                    {
                        image: 'shower_room_y.svg',
                        png: 'shower_room_y.png',
                        title: '一字形淋浴房',
                        label: 'shower_room_y',
                        dragId: "shower_room_y",
                        id: 'shower_room_y',
                        img: 'static/figures_imgs/shower_room_y.svg',
                        roomType: ['卫生间'],
                    },
                    {
                        image: 'shower_room_z.svg',
                        png: 'shower_room_z.png',
                        title: '钻石形淋浴房',
                        label: 'shower_room_z',
                        dragId: "shower_room_z",
                        id: 'shower_room_z',
                        img: 'static/figures_imgs/shower_room_z.svg',
                        roomType: ['卫生间'],
                    },
                    {
                        image: 'showerRoom-s.svg',
                        png: 'showerRoom-s.png',
                        title: '弧形淋浴房',
                        label: 'shower_room_s',
                        dragId: "shower_room_s",
                        id: 'shower_room_s',
                        img: 'static/figures_imgs/showerRoom-s.svg',
                        roomType: ['卫生间'],
                    },
                    {
                        image: 'bathtub.svg',
                        png: 'bathtub.png',
                        title: '浴缸',
                        label: 'bathtub',
                        dragId: "bathtub",
                        id: 'bathtub',
                        img: 'static/figures_imgs/bathtub.svg',
                        roomType: ['卫生间'],
                    },
                    {
                        image: 'washingCabinet.svg',
                        png: 'washingCabinet.png',
                        title: '洗衣机柜',
                        label: 'washing_cabinet',
                        dragId: "washing_cabinet",
                        id: 'washing_cabinet',
                        img: 'static/figures_imgs/washingCabinet.svg',
                        roomType: ['阳台'],
                    },
                ]
            },
        ]
    },
    {
        label: '电器',
        png: 'icon/electrical.png',
        child: [
            {
                label: '电器',
                figureList: [
                    {
                        image: 'television.svg',
                        png: 'television.png',
                        title: '电视',
                        label: 'tv',
                        dragId: "tv",
                        id: 'tv',
                        img: 'static/figures_imgs/television.svg',
                        roomType: ['客餐厅', '卧室'],
                    },
                    {
                        image: 'verticalChamber.svg',
                        png: 'verticalChamber.png',
                        title: '立式空调',
                        label: 'verticalChamber',
                        dragId: "verticalChamber",
                        id: 'verticalChamber',
                        img: 'static/figures_imgs/verticalChamber.svg',
                        roomType: ['客餐厅', '卧室', '书房'],
                    },
                    {
                        image: 'freshAirFan.svg',
                        png: 'freshAirFan.png',
                        title: '新风机',
                        label: 'freshAirFan',
                        dragId: "freshAirFan",
                        id: 'freshAirFan',
                        img: 'static/figures_imgs/freshAirFan.svg',
                        roomType: ['客餐厅', '卧室', '书房'],
                    },
                    {
                        image: 'wallAir.svg',
                        png: 'wallAir.png',
                        title: '壁挂空调',
                        label: 'wallAir',
                        dragId: "wallAir",
                        id: 'wallAir',
                        img: 'static/figures_imgs/wallAir.svg',
                        roomType: ['客餐厅', '卧室', '书房'],
                    },
                    {
                        image: 'refrigerator.svg',
                        png: 'refrigerator.png',
                        title: '冰箱',
                        label: 'refrigerator',
                        dragId: "refrigerator",
                        id: 'refrigerator',
                        img: 'static/figures_imgs/refrigerator.svg',
                        roomType: ['客餐厅', '厨房'],
                    },
                ]
            },
        ]
    },
    {
        label: '装饰',
        png: 'icon/decoration.png',
        child: [
            {
                label: '布艺软饰',
                figureList: [
                    {
                        image: 'carpet.svg',
                        png: 'carpet.png',
                        title: '地毯',
                        label: 'carpet',
                        dragId: "carpet",
                        id: 'carpet',
                        img: 'static/figures_imgs/carpet.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'curtain.svg',
                        png: 'curtain.png',
                        title: '窗帘',
                        label: 'curtain',
                        dragId: "curtain",
                        id: 'curtain',
                        img: 'static/figures_imgs/curtain.svg',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房'],
                    },
                ]
            },
            {
                label: '陈设饰品',
                figureList: [
                    {
                        image: 'sculpture.svg',
                        png: 'sculpture.png',
                        title: '雕塑',
                        label: 'sculpture',
                        dragId: "sculpture",
                        id: 'sculpture',
                        img: 'static/figures_imgs/sculpture.svg',
                        roomType: ['客餐厅'],
                    },
                ]
            },
            {
                label: '灯饰',
                figureList: [
                    {
                        image: 'mainLight.svg',
                        png: 'mainLight.png',
                        title: '主灯',
                        label: 'main_light',
                        dragId: "main_light",
                        id: 'main_light',
                        img: 'static/figures_imgs/mainLight.svg',
                        roomType: ['客餐厅', '卧室', '书房', '阳台', '卫生间'],
                    },
                    {
                        image: 'floorLamp.svg',
                        png: 'floorLamp.png',
                        title: '落地灯',
                        label: 'floor_lamp',
                        dragId: "floor_lamp",
                        id: 'floor_lamp',
                        img: 'static/figures_imgs/floorLamp.svg',
                        roomType: ['客餐厅'],
                    },
                    {
                        image: 'wall_lamp.svg',
                        png: 'wall_lamp.png',
                        title: '壁灯',
                        label: 'wall_lamp',
                        dragId: "wall_lamp",
                        id: 'wall_lamp',
                        img: 'static/figures_imgs/wall_lamp.svg',
                        roomType: ['卧室', "书房", "客餐厅", "阳台", "卫生间"],
                    },
                    {
                        image: 'chandelier.svg',
                        png: 'chandelier.png',
                        title: '床头吊灯',
                        label: 'chandelier',
                        dragId: "chandelier",
                        id: 'chandelier',
                        img: 'static/figures_imgs/chandelier.svg',
                        roomType: ['卧室'],
                    },
                ]
            },
            {
                label: '鲜花园艺',
                figureList: [
                    {
                        image: 'green_plant.svg',
                        png: 'green_plant.png',
                        title: '绿植',
                        label: 'green_plant',
                        dragId: "green_plant",
                        id: 'green_plant',
                        img: 'static/figures_imgs/green_plant.svg',
                        roomType: ['客餐厅'],
                    },
                ]
            },
        ]
    },
    {
        label: '硬装',
        png: 'icon/hard.png',
        child: [
            {
                label: '硬装',
                figureList: [
                    {
                        image: 'Backdrop.svg',
                        png: 'Backdrop.png',
                        title: '背景墙',
                        label: 'background_wall',
                        dragId: "background_wall",
                        id: 'background_wall',
                        img: 'static/figures_imgs/Backdrop.svg',
                        roomType: ['客餐厅'],
                    },
                ]
            }
        ]
    },
    {
        label: '视角',
        png: 'icon/view.png',
        child: [
            {
                label: '效果图',
                figureList: [
                    {
                        image: 'Camera.svg',
                        png: 'Camera.png',
                        title: '相机',
                        dragId: "Camera",
                        label: 'Camera',
                        id: 'Camera',
                        img: 'static/figures_imgs/Camera.png',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房'],
                    }
                ]
            },
            {
                label: '动线',
                figureList: [
                    {
                        image: 'Man.svg',
                        png: 'Man.png',
                        title: '人物',
                        dragId: "Man",
                        label: 'Man',
                        id: 'Man',
                        img: 'static/figures_imgs/Man.png',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房'],
                    }
                ]
            }
        ]
    },
    {
        label: '定制',
        png: 'icon/furniture.png',
        child: [
            {
                label: '定制单元',
                figureList: [
                    {
                        image: '一字衣柜.svg',
                        png: '一字衣柜.png',
                        title: '一字衣柜',
                        dragId: "linear_shape_wardrobe",
                        label: 'linear_shape_wardrobe',
                        id: 'linear_shape_wardrobe',
                        img: 'static/figures_imgs/一字衣柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'L型衣柜.svg',
                        png: 'L型衣柜.png',
                        title: 'L型衣柜',
                        dragId: "l_shape_wardrobe",
                        label: 'l_shape_wardrobe',
                        id: 'l_shape_wardrobe',
                        img: 'static/figures_imgs/L型衣柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '侧边柜.svg',
                        png: '侧边柜.png',
                        title: '侧边柜',
                        dragId: "side_cabinet",
                        label: 'side_cabinet',
                        id: 'side_cabinet',
                        img: 'static/figures_imgs/侧边柜.png',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: '单列功能衣柜.svg',
                        png: '单列功能衣柜.png',
                        title: '单列功能衣柜',
                        dragId: "linear_function_cabinet",
                        label: 'linear_function_cabinet',
                        id: 'linear_function_cabinet',
                        img: 'static/figures_imgs/单列功能衣柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'L型功能衣柜.svg',
                        png: 'L型功能衣柜.png',
                        title: 'L型功能衣柜',
                        dragId: "l_shape_function_wardrobe",
                        label: 'l_shape_function_wardrobe',
                        id: 'l_shape_function_wardrobe',
                        img: 'static/figures_imgs/L型功能衣柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'L型功能柜.svg',
                        png: 'L型功能柜.png',
                        title: 'L型功能柜',
                        dragId: "l_shape_function_cabinet",
                        label: 'l_shape_function_cabinet',
                        id: 'l_shape_function_cabinet',
                        img: 'static/figures_imgs/L型功能柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '榻榻米.svg',
                        png: '榻榻米.png',
                        title: '榻榻米',
                        dragId: "tatami",
                        label: 'tatami',
                        id: 'tatami',
                        img: 'static/figures_imgs/榻榻米.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '榻榻米脚踏.svg',
                        png: '榻榻米脚踏.png',
                        title: '榻榻米脚踏',
                        dragId: "footstool",
                        label: 'footstool',
                        id: 'footstool',
                        img: 'static/figures_imgs/榻榻米脚踏.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '榻榻米床品.svg',
                        png: '榻榻米床品.png',
                        title: '榻榻米床品',
                        dragId: "tatami_bedding",
                        label: 'tatami_bedding',
                        id: 'tatami_bedding',
                        img: 'static/figures_imgs/榻榻米床品.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '一字台面柜.svg',
                        png: '一字台面柜.png',
                        title: '一字台面柜',
                        dragId: "linear_table_cabinet",
                        label: 'linear_table_cabinet',
                        id: 'linear_table_cabinet',
                        img: 'static/figures_imgs/一字台面柜.png',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: '一字书柜.svg',
                        png: '一字书柜.png',
                        title: '一字书柜',
                        dragId: "linear_book_cabinet",
                        label: 'linear_book_cabinet',
                        id: 'linear_book_cabinet',
                        img: 'static/figures_imgs/一字书柜.png',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: '一字形床屏.png',
                        png: '一字形床屏.png',
                        title: '一字形床屏',
                        dragId: "linear_bedscreen",
                        label: 'linear_bedscreen',
                        id: 'linear_bedscreen',
                        img: 'static/figures_imgs/一字形床屏.png',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: 'L形床屏.png',
                        png: 'L形床屏.png',
                        title: 'L形床屏',
                        dragId: "L_bedscreen",
                        label: 'L_bedscreen',
                        id: 'L_bedscreen',
                        img: 'static/figures_imgs/L形床屏.png',
                        roomType: ['卧室', '书房'],
                    }
                ]
            }
        ]
    },
    {
        label: '健身',
        png: 'icon/fitness.png',
        child: [
            {
                label: '健身',
                figureList: [
                    {
                        image: 'fitness/跑步机.svg',
                        png: 'fitness/跑步机.png',
                        title: '跑步机',
                        dragId: "running_machine",
                        label: 'running_machine',
                        id: 'running_machine',
                        img: 'static/figures_imgs/fitness/跑步机.png',
                        roomType: ['客餐厅', '卧室', '书房', '阳台', '其他'],
                    },
                    {
                        image: 'fitness/健身架.svg',
                        png: 'fitness/健身架.png',
                        title: '健身架',
                        dragId: "fitness_frame",
                        label: 'fitness_frame',
                        id: 'fitness_frame',
                        img: 'static/figures_imgs/fitness/健身架.png',
                        roomType: ['客餐厅', '卧室', '书房', '阳台', '其他'],
                    },
                    {
                        image: 'fitness/瑜伽垫.svg',
                        png: 'fitness/瑜伽垫.png',
                        title: '瑜伽垫',
                        dragId: "yoga_mat",
                        label: 'yoga_mat',
                        id: 'yoga_mat',
                        img: 'static/figures_imgs/fitness/瑜伽垫.png',
                        roomType: ['客餐厅', '卧室', '书房', '阳台', '其他'],
                    }
                ]
            }
        ]
    },
    {
        label: '影音',
        png: 'icon/tv.png',
        child: [
            {
                label: '影音',
                figureList: [
                    {
                        image: 'tv/投影仪.svg',
                        png: 'tv/投影仪.png',
                        title: '投影仪',
                        dragId: "projector",
                        label: 'projector',
                        id: 'projector',
                        img: 'static/figures_imgs/tv/投影仪.png',
                        roomType: ['客餐厅', '卧室', '书房', '阳台', '其他'],
                    },
                    {
                        image: 'tv/投影布.svg',
                        png: 'tv/投影布.png',
                        title: '投影布',
                        dragId: "projector_screen",
                        label: 'projector_screen',
                        id: 'projector_screen',
                        img: 'static/figures_imgs/tv/投影布.png',
                        roomType: ['客餐厅', '卧室', '书房', '阳台', '其他'],
                    },
                    {
                        image: 'tv/音响.svg',
                        png: 'tv/音响.png',
                        title: '音响',
                        dragId: "speaker",
                        label: 'speaker',
                        id: 'speaker',
                        img: 'static/figures_imgs/tv/音响.png',
                        roomType: ['客餐厅', '卧室', '书房', '阳台', '其他'],
                    }
                ]
            }
        ]
    }
];

/**
 * @description 新版左侧模型位数据
 */
export const modelLocList = [
    {
        label: '家具',
        png: 'icon/furniture.png',
        child: [
            { "label": "沙发", "figureList": [] },
            { "label": "休闲椅", "figureList": [] },
            { "label": "脚踏", "figureList": [] },
            { "label": "餐椅", "figureList": [] },
            { "label": "凳子", "figureList": [] },
            { "label": "卡座", "figureList": [] },
            { "label": "长条凳", "figureList": [] },
            { "label": "书椅", "figureList": [] },
            { "label": "床尾凳", "figureList": [] },
            { "label": "梳妆凳", "figureList": [] },
            { "label": "茶几", "figureList": [] },
            { "label": "边几", "figureList": [] },
            { "label": "餐桌", "figureList": [] },
            { "label": "茶台", "figureList": [] },
            { "label": "吧台", "figureList": [] },
            { "label": "无靠背沙发", "figureList": [] },
            { "label": "梳妆台", "figureList": [] },
            { "label": "书桌", "figureList": [] },
            { "label": "衣柜", "figureList": [] },
            { "label": "电视柜", "figureList": [] },
            { "label": "餐边柜", "figureList": [] },
            { "label": "玄关柜", "figureList": [] },
            { "label": "书柜", "figureList": [] },
            { "label": "岛台", "figureList": [] },
            { "label": "床", "figureList": [] },
            { "label": "床头柜", "figureList": [] },
            { "label": "床屏", "figureList": [] }
        ]
    },
    {
        label: '组合',
        png: 'icon/combination.png',
        child: [
            {
                label: "沙发组合",
                figureList: [
                ]
            },
            {
                label: "餐桌椅组合",
                figureList: [
                ]
            },
            {
                label: "床具组合",
                figureList: [
                ]

            },
            {
                label: "书桌组合",
                figureList: [
                ]
            },
            {
                label: "岛台组合",
                figureList: [
                ]
            },
            {
                label: "榻榻米组合",
                figureList: [
                    {
                        image: 'tatamimat.svg',
                        png: 'tatamimat.png',
                        title: '榻榻米组合',
                        dragId: "tatamimat",
                        label: 'tatamimat',
                        id: 'tatamimat',
                        img: 'static/figures_imgs/tatamimat.svg',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: 'L_tatamimat.svg',
                        png: 'L_tatamimat.png',
                        title: 'L形榻榻米组合',
                        dragId: "L_tatamimat",
                        label: 'L_tatamimat',
                        id: 'L_tatamimat',
                        img: 'static/figures_imgs/L_tatamimat.svg',
                        roomType: ['卧室', '书房'],
                    },
                ]
            },
        ]
    },
    {
        label: '卫浴',
        png: 'icon/kitchen.png',
        child: [
            { "label": "浴室柜", "figureList": [] },
            { "label": "淋浴房", "figureList": [] },
            { "label": "花洒", "figureList": [] },
            { "label": "马桶", "figureList": [] },
            { "label": "浴缸", "figureList": [] },
            { "label": "毛巾架", "figureList": [] },
            { "label": "洗衣机柜", "figureList": [] }
        ]
    },
    {
        label: '电器',
        png: 'icon/electrical.png',
        child: [
            { "label": "电视", "figureList": [] },
            { "label": "空调", "figureList": [] },
            { "label": "冰箱", "figureList": [] },
            { "label": "新风机", "figureList": [] }
        ]
    },
    {
        label: '装饰',
        png: 'icon/decoration.png',
        child: [
            { "label": "绿植", "figureList": [] },
            { "label": "雕塑", "figureList": [] },
            { "label": "客厅墙饰", "figureList": [] },
            { "label": "餐厅墙饰", "figureList": [] },
            { "label": "卧室墙饰", "figureList": [] },
            { "label": "电视柜饰品", "figureList": [] },
            { "label": "茶几饰品", "figureList": [] },
            { "label": "边几饰品", "figureList": [] },
            { "label": "餐桌饰品", "figureList": [] },
            { "label": "餐具饰品", "figureList": [] },
            { "label": "餐边柜饰品", "figureList": [] },
            { "label": "书桌饰品", "figureList": [] },
            { "label": "床头柜饰品", "figureList": [] },
            { "label": "客厅主灯", "figureList": [] },
            { "label": "餐厅主灯", "figureList": [] },
            { "label": "卧室主灯", "figureList": [] },
            { "label": "书房主灯", "figureList": [] },
            { "label": "卫生间主灯", "figureList": [] },
            { "label": "阳台主灯", "figureList": [] },
            { "label": "床头吊灯", "figureList": [] },
            { "label": "筒灯", "figureList": [] },
            { "label": "落地灯", "figureList": [] },
            { "label": "壁灯", "figureList": [] },
            { "label": "地毯", "figureList": [] },
            { "label": "窗帘", "figureList": [] }
        ]
    },
    {
        label: '硬装',
        png: 'icon/hard.png',
        child: [
            { "label": "沙发背景墙", "figureList": [] },
            { "label": "电视背景墙", "figureList": [] },
            { "label": "卧室背景墙", "figureList": [] },
            { "label": "L形背景墙", "figureList": [] },
        ]
    },
    {
        label: '视角',
        png: 'icon/view.png',
        child: [
            {
                label: '效果图',
                figureList: [
                    {
                        image: 'Camera.svg',
                        png: 'Camera.png',
                        title: '相机',
                        dragId: "Camera",
                        label: 'Camera',
                        id: 'Camera',
                        img: 'static/figures_imgs/Camera.png',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房'],
                    }
                ]
            },
            {
                label: '动线',
                figureList: [
                    {
                        image: 'Man.svg',
                        png: 'Man.png',
                        title: '人物',
                        dragId: "Man",
                        label: 'Man',
                        id: 'Man',
                        img: 'static/figures_imgs/Man.png',
                        roomType: ['客餐厅', '卧室', '阳台', '卫生间', '书房'],
                    }
                ]
            }
        ]
    },
    {
        label: '定制',
        png: 'icon/furniture.png',
        child: [
            {
                label: '定制单元',
                figureList: [
                    {
                        image: '一字衣柜.svg',
                        png: '一字衣柜.png',
                        title: '一字衣柜',
                        dragId: "linear_shape_wardrobe",
                        label: 'linear_shape_wardrobe',
                        id: 'linear_shape_wardrobe',
                        img: 'static/figures_imgs/一字衣柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'L型衣柜.svg',
                        png: 'L型衣柜.png',
                        title: 'L型衣柜',
                        dragId: "l_shape_wardrobe",
                        label: 'l_shape_wardrobe',
                        id: 'l_shape_wardrobe',
                        img: 'static/figures_imgs/L型衣柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '侧边柜.svg',
                        png: '侧边柜.png',
                        title: '侧边柜',
                        dragId: "side_cabinet",
                        label: 'side_cabinet',
                        id: 'side_cabinet',
                        img: 'static/figures_imgs/侧边柜.png',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: '单列功能衣柜.svg',
                        png: '单列功能衣柜.png',
                        title: '单列功能衣柜',
                        dragId: "linear_function_cabinet",
                        label: 'linear_function_cabinet',
                        id: 'linear_function_cabinet',
                        img: 'static/figures_imgs/单列功能衣柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'L型功能衣柜.svg',
                        png: 'L型功能衣柜.png',
                        title: 'L型功能衣柜',
                        dragId: "l_shape_function_wardrobe",
                        label: 'l_shape_function_wardrobe',
                        id: 'l_shape_function_wardrobe',
                        img: 'static/figures_imgs/L型功能衣柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: 'L型功能柜.svg',
                        png: 'L型功能柜.png',
                        title: 'L型功能柜',
                        dragId: "l_shape_function_cabinet",
                        label: 'l_shape_function_cabinet',
                        id: 'l_shape_function_cabinet',
                        img: 'static/figures_imgs/L型功能柜.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '榻榻米.svg',
                        png: '榻榻米.png',
                        title: '榻榻米',
                        dragId: "tatami",
                        label: 'tatami',
                        id: 'tatami',
                        img: 'static/figures_imgs/榻榻米.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '榻榻米脚踏.svg',
                        png: '榻榻米脚踏.png',
                        title: '榻榻米脚踏',
                        dragId: "footstool",
                        label: 'footstool',
                        id: 'footstool',
                        img: 'static/figures_imgs/榻榻米脚踏.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '榻榻米床品.svg',
                        png: '榻榻米床品.png',
                        title: '榻榻米床品',
                        dragId: "tatami_bedding",
                        label: 'tatami_bedding',
                        id: 'tatami_bedding',
                        img: 'static/figures_imgs/榻榻米床品.png',
                        roomType: ['卧室'],
                    },
                    {
                        image: '一字台面柜.svg',
                        png: '一字台面柜.png',
                        title: '一字台面柜',
                        dragId: "linear_table_cabinet",
                        label: 'linear_table_cabinet',
                        id: 'linear_table_cabinet',
                        img: 'static/figures_imgs/一字台面柜.png',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: '一字书柜.svg',
                        png: '一字书柜.png',
                        title: '一字书柜',
                        dragId: "linear_book_cabinet",
                        label: 'linear_book_cabinet',
                        id: 'linear_book_cabinet',
                        img: 'static/figures_imgs/一字书柜.png',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: '一字形床屏.png',
                        png: '一字形床屏.png',
                        title: '一字形床屏',
                        dragId: "linear_bedscreen",
                        label: 'linear_bedscreen',
                        id: 'linear_bedscreen',
                        img: 'static/figures_imgs/一字形床屏.png',
                        roomType: ['卧室', '书房'],
                    },
                    {
                        image: 'L形床屏.png',
                        png: 'L形床屏.png',
                        title: 'L形床屏',
                        dragId: "L_bedscreen",
                        label: 'L_bedscreen',
                        id: 'L_bedscreen',
                        img: 'static/figures_imgs/L形床屏.png',
                        roomType: ['卧室', '书房'],
                    }
                ]
            }
        ]
    },
    {
        label: '健身',
        png: 'icon/fitness.png',
        child: [
            { "label": "跑步机", "figureList": [] },
            { "label": "健身架", "figureList": [] },
            { "label": "瑜伽垫", "figureList": [] },
        ]
    },
    {
        label: '影音',
        png: 'icon/tv.png',
        child: [
            { "label": "投影仪", "figureList": [] },
            { "label": "投影布", "figureList": [] },
            { "label": "音响", "figureList": [] }
        ]
    },
    {
        label: '其它',
        png: 'icon/floor.png',
        child: [
            { "label": "其它", "figureList": [] },
        ]
    }
];





interface FigureImagePath {
    img_path: string;
    ratio?: number
    alias?: string;
    length?: number;
    depth?: number;
    height?: number;
    corner_width?: number;
    corner_depth?: number;
    zval?: number;
    modelLoc: string;
    public_category?: string;
    shape?: string;
    subCategory: string;
    png: string;
    off_land_rule?: string
    rotation_z?: number;
    isFurniture?: boolean;
}

export var g_FigureImagePaths: { [key: string]: FigureImagePath } = {
    "床": {
        "img_path": "./static/figures_imgs/bed.svg",
        "png": "./static/figures_imgs/bed.png",
        'alias': "床",
        'length': 1600,
        'depth': 2000,
        'height': 450,
        "modelLoc": "床",
        "subCategory": "床"
    },
    "电视柜": {
        "img_path": "./static/figures_imgs/TVcabinet.svg",
        "png": "./static/figures_imgs/TVcabinet.png",
        'alias': "电视柜",
        "ratio": 0.5,
        'length': 3600,
        'depth': 470,
        'height': 2400,
        "modelLoc": "电视柜",
        "subCategory": "电视柜"
    },
    "转角沙发": {
        "img_path": "./static/figures_imgs/L_sofa.svg",
        "png": "./static/figures_imgs/L_sofa.png",
        "alias": "转角沙发",
        'length': 2700,
        'depth': 1300,
        'height': 820,
        "modelLoc": "沙发",
        "public_category": "转角沙发",
        "subCategory": "转角沙发"
    },
    "衣柜": {
        "img_path": "./static/figures_imgs/wardrobe.svg",
        "png": "./static/figures_imgs/wardrobe.png",
        "alias": "衣柜",
        'length': 2500,
        'depth': 620,
        "height": 2400,
        "modelLoc": "衣柜",
        "subCategory": "衣柜"
    },
    "矩形边几": {
        "img_path": "./static/figures_imgs/bianji.svg",
        "png": "./static/figures_imgs/bianji.png",
        'alias': "边几",
        'length': 500,
        'depth': 500,
        'height': 400,
        "modelLoc": "边几",
        "subCategory": "矩形边几"
    },
    "直排沙发": {
        "img_path": "./static/figures_imgs/Sofa.svg",
        "png": "./static/figures_imgs/Sofa.png",
        'alias': "多人沙发",
        'length': 2100,
        'depth': 900,
        'height': 820,
        "modelLoc": "沙发",
        "public_category": "多人沙发",
        "subCategory": "直排沙发"
    },
    "多人沙发": {
        "img_path": "./static/figures_imgs/Sofa.svg",
        "png": "./static/figures_imgs/Sofa.png",
        'alias': "多人沙发",
        'length': 2100,
        'depth': 900,
        'height': 820,
        "modelLoc": "沙发",
        "public_category": "多人沙发",
        "subCategory": "多人沙发"
    },
    "餐边柜": {
        "img_path": "./static/figures_imgs/mealSideCabinet.svg",
        "png": "./static/figures_imgs/mealSideCabinet.png",
        'alias': "餐边柜",
        'length': 1600,
        'depth': 460,
        'height': 2400,
        "modelLoc": "餐边柜",
        "subCategory": "餐边柜"
    },
    "电视": {
        "img_path": "./static/figures_imgs/television.svg",
        "png": "./static/figures_imgs/television.png",
        'alias': "电视",
        'length': 1400,
        'depth': 100,
        'height': 1200,
        "zval": 800,
        "modelLoc": "电视",
        "subCategory": "电视"
    },
    "餐桌": {
        "img_path": "./static/figures_imgs/board.svg",
        "png": "./static/figures_imgs/board.png",
        'alias': "餐桌",
        'length': 1800,
        'depth': 900,
        'height': 780,
        "modelLoc": "餐桌",
        "subCategory": "餐桌"
    },
    "圆形餐桌": {
        "img_path": "./static/figures_imgs/roundDinnerTable.svg",
        "png": "./static/figures_imgs/roundDinnerTable.png",
        'alias': "圆形餐桌",
        'length': 1400,
        'depth': 1400,
        'height': 780,
        "modelLoc": "餐桌",
        "subCategory": "圆形餐桌"
    },
    "单人沙发": {
        "img_path": "./static/figures_imgs/<EMAIL>",
        "png": "./static/figures_imgs/<EMAIL>",
        'alias': "单人沙发",
        'length': 800,
        'depth': 750,
        'height': 820,
        "modelLoc": "休闲椅",
        "public_category": "单人沙发",
        "subCategory": "单人沙发"
    },
    "窗帘": {
        "img_path": "./static/figures_imgs/curtain.svg",
        "png": "./static/figures_imgs/curtain.png",
        "alias": "窗帘",
        'length': 2600,
        'depth': 200,
        'height': 2600,
        "modelLoc": "窗帘",
        "public_category": "双开帘",
        "subCategory": "窗帘"
    },
    "转角窗帘": {
        "img_path": "./static/figures_imgs/<EMAIL>",
        "png": "./static/figures_imgs/<EMAIL>",
        "alias": "转角窗帘",
        'length': 600,
        'depth': 730,
        'height': 2600,
        "modelLoc": "窗帘",
        "subCategory": "转角窗帘"
    },
    "餐椅": {
        "img_path": "./static/figures_imgs/DiningChair.svg",
        "png": "./static/figures_imgs/DiningChair.png",
        'alias': "餐椅",
        'length': 550,
        'depth': 600,
        'height': 780,
        "modelLoc": "餐椅",
        "subCategory": "餐椅"
    },
    "书桌": {
        "img_path": "./static/figures_imgs/desk.svg",
        "png": "./static/figures_imgs/desk.png",
        'alias': "书桌",
        'length': 900,
        'depth': 500,
        "height": 700,
        "modelLoc": "书桌",
        "subCategory": "书桌"
    },
    "书椅": {
        "img_path": "./static/figures_imgs/BookChair.svg",
        "png": "./static/figures_imgs/BookChair.png",
        'alias': "书椅",
        'length': 850,
        'depth': 750,
        "height": 700,
        "modelLoc": "书椅",
        "public_category": "写字椅",
        "subCategory": "书椅"
    },
    "绿植": {
        "img_path": "./static/figures_imgs/green_plant.svg",
        "png": "./static/figures_imgs/green_plant.png",
        'alias': "绿植",
        'length': 800,
        'depth': 800,
        "height": 700,
        "modelLoc": "绿植",
        "subCategory": "绿植"
    },

    "圆形茶几": {
        "img_path": "./static/figures_imgs/teaTable-y.svg",
        "png": "./static/figures_imgs/teaTable-y.png",
        'alias': "圆形茶几",
        'length': 900,
        'depth': 690,
        "height": 450,
        "modelLoc": "茶几",
        "shape": "圆形",
        "subCategory": "圆形茶几"
    },
    "矩形茶几": {
        "img_path": "./static/figures_imgs/teatable-j.svg",
        "png": "./static/figures_imgs/teatable-j.png",
        'alias': "矩形茶几",
        'length': 1100,
        'depth': 700,
        "height": 450,
        "modelLoc": "茶几",
        "shape": "矩形",
        "subCategory": "矩形茶几"
    },
    "茶几": {
        "img_path": "./static/figures_imgs/teatable-j.svg",
        "png": "./static/figures_imgs/teatable-j.png",
        'alias': "矩形茶几",
        'length': 1100,
        'depth': 700,
        "height": 450,
        "modelLoc": "茶几",
        "subCategory": "茶几"
    },
    "床头柜": {
        "img_path": "./static/figures_imgs/bedsideTable.svg",
        "png": "./static/figures_imgs/bedsideTable.png",
        "alias": "床头柜",
        'length': 500,
        'depth': 400,
        "height": 600,
        "modelLoc": "床头柜",
        "subCategory": "床头柜"
    },
    "马桶": {
        "img_path": "./static/figures_imgs/closestool.svg",
        "png": "./static/figures_imgs/closestool.png",
        "alias": "马桶",
        'length': 400,
        'depth': 600,
        "height": 400,
        "zval": 150,
        "modelLoc": "马桶",
        "subCategory": "马桶"
    },
    // "toilet": {
    //     "img_path": "./static/figures_imgs/<EMAIL>",
    //     "alias":"蹲便器",
    //     'length': 460,
    //     'depth': 600,
    //     "modelLoc": "马桶",
    //     "public_category": "马桶",
    //     "subCategory": "马桶"
    // },
    "书柜": {
        "img_path": "./static/figures_imgs/bookcase.svg",
        "png": "./static/figures_imgs/bookcase.png",
        "alias": "书柜",
        'length': 2940,
        'depth': 350,
        "modelLoc": "书柜",
        "subCategory": "书柜"
    },
    // "balcony_cabinet": {
    //     "img_path": "./static/figures_imgs/<EMAIL>",
    //     "alias":"阳台柜",
    //     'length': 1200,
    //     'depth': 600

    // },
    // "washing_machine": {
    //     "img_path": "./static/figures_imgs/<EMAIL>",
    //     "alias":"洗衣机",
    //     'length': 1580,
    //     'depth': 620
    // },
    "洗衣机柜": {
        "img_path": "./static/figures_imgs/washingCabinet.svg",
        "png": "./static/figures_imgs/washingCabinet.png",
        "alias": "洗衣机柜",
        'length': 1500,
        'depth': 700,
        "modelLoc": "洗衣机柜",
        "subCategory": "洗衣机柜"
    },
    "烟管": {
        "img_path": "",
        "png": "",
        "alias": "烟管",
        'length': 730,
        'depth': 140,
        "modelLoc": "烟管",
        "subCategory": "烟管"
    },
    "玄关柜": {
        "img_path": "./static/figures_imgs/gateCabinet.svg",
        "png": "./static/figures_imgs/gateCabinet.png",
        "alias": "玄关柜",
        'length': 1500,
        'depth': 400,
        "modelLoc": "玄关柜",
        "subCategory": "玄关柜"
    },
    // "榻榻米": {
    //   "img_path": "./static/figures_imgs/<EMAIL>",
    //   "png": "./static/figures_imgs/<EMAIL>",
    //   "alias": "榻榻米",
    //   'length': 1800,
    //   'depth': 1700,
    //   "modelLoc": "榻榻米",
    //   "subCategory": "榻榻米"
    // },
    "地毯": {
        "img_path": "./static/figures_imgs/carpet.svg",
        "png": "./static/figures_imgs/carpet.png",
        'length': 4000,
        'depth': 3000,
        'height': 8,
        "modelLoc": "地毯",
        "subCategory": "地毯"
    },
    "浴室柜": {
        "img_path": "./static/figures_imgs/BathroomCabinet.svg",
        "png": "./static/figures_imgs/BathroomCabinet.png",
        "alias": "浴室柜",
        'length': 1200,
        'depth': 600,
        "modelLoc": "浴室柜",
        "subCategory": "浴室柜"
    },
    "贵妃椅": {
        "img_path": "./static/figures_imgs/<EMAIL>",
        "png": "./static/figures_imgs/<EMAIL>",
        "alias": "贵妃椅",
        'length': 800,
        'depth': 400,
        "modelLoc": "休闲椅",
        "public_category": "贵妃椅",
        "subCategory": "贵妃椅"
    },
    "雕塑": {
        "img_path": "./static/figures_imgs/sculpture.svg",
        "png": "./static/figures_imgs/sculpture.png",
        "alias": "雕塑",
        'length': 550,
        'depth': 550,
        'height': 1200,
        "modelLoc": "雕塑",
        "subCategory": "雕塑"
    },
    "弧形淋浴房": {
        "img_path": "./static/figures_imgs/showerRoom-s.svg",
        "png": "./static/figures_imgs/showerRoom-s.png",
        "alias": "弧形淋浴房",
        'length': 1200,
        'depth': 1200,
        "height": 2000,
        "modelLoc": "淋浴房",
        "shape": "弧形",
        "subCategory": "弧形淋浴房"
    },
    "花洒": {
        "img_path": "./static/figures_imgs/showerHead.svg",
        "png": "./static/figures_imgs/showerHead.png",
        "alias": "花洒",
        'length': 400,
        'depth': 600,
        'height': 600,
        'zval': 518,
        "modelLoc": "花洒",
        "subCategory": "花洒"
    },
    "凳子": {
        "img_path": "./static/figures_imgs/stool.svg",
        "png": "./static/figures_imgs/stool.png",
        "alias": "凳子",
        'length': 400,
        'depth': 400,
        'height': 400,
        "modelLoc": "梳妆凳",
        "public_category": "梳妆凳",
        "subCategory": "凳子"
    },
    "岛台": {
        "img_path": "./static/figures_imgs/Islandplatform.svg",
        "png": "./static/figures_imgs/Islandplatform.png",
        "alias": "岛台",
        "length": 1000,
        "depth": 620,
        "height": 750,
        "modelLoc": "岛台",
        "subCategory": "岛台"
    },
    "卡座": {
        "img_path": "./static/figures_imgs/cassette.svg",
        "png": "./static/figures_imgs/cassette.png",
        "alias": "卡座",
        "length": 2000,
        "depth": 400,
        "height": 700,
        "modelLoc": "卡座",
        "subCategory": "卡座"
    },
    "长条凳": {
        "img_path": "./static/figures_imgs/bench.svg",
        "png": "./static/figures_imgs/bench.png",
        "alias": "长条凳",
        "length": 1450,
        "depth": 400,
        "height": 400,
        "modelLoc": "长条凳",
        "subCategory": "长条凳"
    },
    "床头吊灯": {
        "img_path": "./static/figures_imgs/chandelier.svg",
        "png": "./static/figures_imgs/chandelier.png",
        "alias": "床头吊灯",
        'length': 300,
        'depth': 350,
        "modelLoc": "床头吊灯",
        "subCategory": "床头吊灯"
    },
    "主灯": {
        "img_path": "./static/figures_imgs/mainLight.svg",
        "png": "./static/figures_imgs/mainLight.png",
        "alias": "主灯",
        'length': 1000,
        'depth': 700,
        'height': 600,
        'zval': 2400,
        "modelLoc": "主灯",
        "subCategory": "主灯"
    },
    "客厅主灯": {
        "img_path": "./static/figures_imgs/mainLight.svg",
        "png": "./static/figures_imgs/mainLight.png",
        "alias": "主灯",
        'length': 1000,
        'depth': 700,
        'height': 600,
        'zval': 2400,
        "modelLoc": "客厅主灯",
        "subCategory": "客厅主灯"
    },
    "餐厅主灯": {
        "img_path": "./static/figures_imgs/mainLight.svg",
        "png": "./static/figures_imgs/mainLight.png",
        "alias": "主灯",
        'length': 1000,
        'depth': 700,
        'height': 600,
        'zval': 2400,
        "modelLoc": "餐厅主灯",
        "subCategory": "餐厅主灯"
    },
    "吊灯": {
        "img_path": "./static/figures_imgs/mainLight.svg",
        "png": "./static/figures_imgs/mainLight.png",
        "alias": "主灯",
        'length': 1000,
        'depth': 700,
        'height': 600,
        'zval': 2400,
        "modelLoc": "吊灯",
        "subCategory": "吊灯"
    },
    "梳妆台": {
        "img_path": "./static/figures_imgs/dressingTable.svg",
        "png": "./static/figures_imgs/dressingTable.png",
        "alias": "梳妆台",
        'length': 1200,
        'depth': 500,
        'height': 1500,
        "modelLoc": "梳妆台",
        "subCategory": "梳妆台"
    },
    "落地灯": {
        "img_path": "./static/figures_imgs/floorLamp.svg",
        "png": "./static/figures_imgs/floorLamp.png",
        "alias": "落地灯",
        'length': 800,
        'depth': 800,
        'height': 1300,
        "modelLoc": "落地灯",
        "subCategory": "落地灯"
    },
    "脚踏": {
        "img_path": "./static/figures_imgs/footPedal.svg",
        "png": "./static/figures_imgs/footPedal.png",
        "alias": "脚踏",
        'length': 480,
        'depth': 800,
        'height': 800,
        "modelLoc": "脚踏",
        "subCategory": "脚踏"
    },
    "矩形淋浴房": {
        "img_path": "./static/figures_imgs/shower_room_j.svg",
        "png": "./static/figures_imgs/shower_room_j.png",
        "alias": "矩形淋浴房",
        'length': 1200,
        'depth': 1200,
        "height": 2000,
        "modelLoc": "淋浴房",
        "shape": "矩形",
        "subCategory": "矩形淋浴房"
    },
    "一字形淋浴房": {
        "img_path": "./static/figures_imgs/shower_room_y.svg",
        "png": "./static/figures_imgs/shower_room_y.png",
        "alias": "一字形淋浴房",
        'length': 1500,
        'depth': 200,
        "height": 2000,
        "modelLoc": "淋浴房",
        "shape": "一字形",
        "subCategory": "一字形淋浴房"
    },

    "钻石形淋浴房": {
        "img_path": "./static/figures_imgs/shower_room_z.svg",
        "png": "./static/figures_imgs/shower_room_z.png",
        "alias": "钻石形淋浴房",
        'length': 1200,
        'depth': 1200,
        "height": 2000,
        "modelLoc": "淋浴房",
        "shape": "钻石形",
        "subCategory": "钻石形淋浴房"
    },

    "毛巾架": {
        "img_path": "./static/figures_imgs/towelRack.svg",
        "png": "./static/figures_imgs/towelRack.png",
        "alias": "毛巾架",
        'length': 600,
        'depth': 300,
        "zval": 1300,
        "modelLoc": "毛巾架",
        "subCategory": "毛巾架"
    },
    "背景墙": {
        "img_path": "./static/figures_imgs/Backdrop.svg",
        "png": "./static/figures_imgs/Backdrop.svg",
        "alias": "背景墙",
        'length': 1600,
        'depth': 100,
        "modelLoc": "背景墙",
        "subCategory": "背景墙"
    },
    "卧室背景墙": {
        "img_path": "./static/figures_imgs/Backdrop.svg",
        "png": "./static/figures_imgs/Backdrop.svg",
        "alias": "背景墙",
        'length': 1600,
        'depth': 100,
        "modelLoc": "背景墙",
        "subCategory": "卧室背景墙"
    },
    "沙发背景墙": {
        "img_path": "./static/figures_imgs/Backdrop.svg",
        "png": "./static/figures_imgs/Backdrop.svg",
        "alias": "背景墙",
        'length': 1600,
        'depth': 100,
        "modelLoc": "背景墙",
        "subCategory": "沙发背景墙"
    },
    "电视背景墙": {
        "img_path": "./static/figures_imgs/Backdrop.svg",
        "png": "./static/figures_imgs/Backdrop.svg",
        "alias": "背景墙",
        'length': 1600,
        'depth': 100,
        "modelLoc": "背景墙",
        "subCategory": "电视背景墙"
    },
    "圆形边几": {
        "img_path": "./static/figures_imgs/roundTable.svg",
        "png": "./static/figures_imgs/roundTable.png",
        "alias": "圆形边几",
        'length': 500,
        'depth': 500,
        "modelLoc": "边几",
        "shape": "圆形",
        "subCategory": "圆形边几"
    },
    "休闲椅": {
        "img_path": "./static/figures_imgs/leisureChair.svg",
        "png": "./static/figures_imgs/leisureChair.png",
        "alias": "休闲椅",
        'length': 800,
        'depth': 800,
        'height': 800,
        "modelLoc": "休闲椅",
        "subCategory": "休闲椅"
    },
    // 橱柜...
    "水槽地柜": {
        "img_path": "./static/figures_imgs/sink_floor_cabinet.svg",
        "png": "./static/figures_imgs/sink_floor_cabinet.svg",
        "alias": "水槽地柜",
        "length": 600,
        "depth": 560,
        "height": 710,
        "zval": 100,
        "modelLoc": "地柜-水槽地柜",
        "subCategory": "水槽地柜"
    },
    "炉灶地柜": {
        "img_path": "./static/figures_imgs/stove_top_cabinet.svg",
        "png": "./static/figures_imgs/stove_top_cabinet.svg",
        "alias": "炉灶地柜",
        "length": 800,
        "depth": 560,
        "height": 710,
        "zval": 100,
        "modelLoc": "地柜-炉灶地柜",
        "subCategory": "炉灶地柜"
    },
    "假门地柜": {
        "img_path": "./static/figures_imgs/fake_door_cabinet.svg",
        "png": "./static/figures_imgs/fake_door_cabinet.svg",
        "alias": "假门地柜",
        "length": 600,
        "depth": 560,
        "height": 710,
        "modelLoc": "地柜-假门地柜",
        "subCategory": "假门地柜"
    },
    "单门地柜": {
        "img_path": "./static/figures_imgs/single_door_floor_cabinet.svg",
        "png": "./static/figures_imgs/single_door_floor_cabinet.svg",
        "alias": "单门地柜",
        "length": 600,
        "depth": 560,
        "height": 710,
        "modelLoc": "地柜-单门地柜",
        "subCategory": "单门地柜"
    },
    "双门地柜": {
        "img_path": "./static/figures_imgs/double_door_floor_cabinet.svg",
        "png": "./static/figures_imgs/double_door_floor_cabinet.svg",
        "alias": "双门地柜",
        "length": 600,
        "depth": 560,
        "height": 710,
        "modelLoc": "地柜-双门地柜",
        "subCategory": "双门地柜"
    },
    "抽屉地柜": {
        "img_path": "./static/figures_imgs/drawer_cabinet.svg",
        "png": "./static/figures_imgs/drawer_cabinet.svg",
        "alias": "抽屉地柜",
        "length": 600,
        "depth": 560,
        "height": 710,
        "modelLoc": "地柜-抽屉地柜",
        "subCategory": "抽屉地柜"
    },
    "拉篮地柜": {
        "img_path": "./static/figures_imgs/pull_basket_cabinet.svg",
        "png": "./static/figures_imgs/pull_basket_cabinet.svg",
        "alias": "拉篮地柜",
        "length": 600,
        "depth": 560,
        "height": 710,
        "modelLoc": "地柜-拉篮地柜",
        "subCategory": "拉篮地柜"
    },
    "消毒地柜": {
        "img_path": "./static/figures_imgs/disinfection_floor_cabinet.svg",
        "png": "./static/figures_imgs/disinfection_floor_cabinet.svg",
        "alias": "消毒地柜",
        "length": 600,
        "depth": 560,
        "modelLoc": "地柜-消毒地柜",
        "subCategory": "消毒地柜"
    },
    "米箱地柜": {
        "img_path": "./static/figures_imgs/rice_chest_floor_cabinet.svg",
        "png": "./static/figures_imgs/rice_chest_floor_cabinet.svg",
        "alias": "米箱地柜",
        "length": 600,
        "depth": 560,
        "height": 710,
        "modelLoc": "地柜-米箱地柜",
        "subCategory": "米箱地柜"
    },
    "转角地柜": {
        "img_path": "./static/figures_imgs/corner_cabinet.svg",
        "png": "./static/figures_imgs/corner_cabinet.svg",
        "alias": "转角地柜",
        "length": 600,
        "depth": 560,
        "height": 710,
        "modelLoc": "地柜-转角地柜",
        "subCategory": "转角地柜"
    },
    "单门吊柜": {
        "img_path": "./static/figures_imgs/single_door_cabinet.svg",
        "png": "./static/figures_imgs/single_door_cabinet.svg",
        "alias": "单门吊柜",
        "length": 600,
        "depth": 375,
        "height": 704,
        "zval": 1680,
        "modelLoc": "吊柜-单门吊柜",
        "subCategory": "单门吊柜"
    },
    "双门吊柜": {
        "img_path": "./static/figures_imgs/double-door_hanging_cabinet.svg",
        "png": "./static/figures_imgs/double-door_hanging_cabinet.svg",
        "alias": "双门吊柜",
        "length": 600,
        "depth": 375,
        "height": 704,
        "zval": 1680,
        "modelLoc": "吊柜-双门吊柜",
        "subCategory": "双门吊柜"
    },
    "烟机吊柜": {
        "img_path": "./static/figures_imgs/hood_hanging_cabinet.svg",
        "png": "./static/figures_imgs/hood_hanging_cabinet.svg",
        "alias": "烟机吊柜",
        "length": 600,
        "depth": 375,
        "height": 704,
        "zval": 1680,
        "modelLoc": "吊柜-烟机吊柜",
        "subCategory": "烟机吊柜"
    },
    "吊柜收口板": {
        "img_path": "./static/figures_imgs/hanging_cabinet_closing_plate.svg",
        "png": "./static/figures_imgs/hanging_cabinet_closing_plate.svg",
        "alias": "吊柜收口板",
        "length": 30,
        "depth": 375,
        "height": 710,
        "zval": 1680,
        "modelLoc": "吊柜收口板",
        "subCategory": "吊柜收口板"
    },
    "地柜收口板": {
        "img_path": "./static/figures_imgs/floor_cabinet_closing_plate.svg",
        "png": "./static/figures_imgs/floor_cabinet_closing_plate.svg",
        "alias": "地柜收口板",
        "length": 30,
        "depth": 560,
        "height": 710,
        "modelLoc": "地柜收口板",
        "subCategory": "地柜收口板"
    },
    "地柜转角封板": {
        "img_path": "./static/figures_imgs/floor_cabinet_corner_sealing_plate.svg",
        "png": "./static/figures_imgs/floor_cabinet_corner_sealing_plate.svg",
        "alias": "地柜转角封板",
        "length": 30,
        "depth": 560,
        "height": 710,
        "modelLoc": "地柜转角封板",
        "public_category": "地柜转角封板",
        "subCategory": "地柜转角封板"
    },
    "地柜见光板": {
        "img_path": "./static/figures_imgs/floor_cabinet_light_panel.svg",
        "png": "./static/figures_imgs/floor_cabinet_light_panel.svg",
        "alias": "地柜见光板",
        "length": 30,
        "depth": 560,
        "modelLoc": "地柜见光板",
        "public_category": "地柜见光板",
        "subCategory": "地柜见光板"
    },
    "吊柜见光板": {
        "img_path": "./static/figures_imgs/lighting_panel_for_hanging_cabinet.svg",
        "png": "./static/figures_imgs/lighting_panel_for_hanging_cabinet.svg",
        "alias": "吊柜见光板",
        "length": 30,
        "depth": 375,
        "modelLoc": "吊柜见光板",
        "public_category": "吊柜见光板",
        "subCategory": "吊柜见光板"
    },
    "高柜": {
        "img_path": "./static/figures_imgs/high_cabinet.svg",
        "png": "./static/figures_imgs/high_cabinet.svg",
        "alias": "高柜",
        "length": 600,
        "depth": 560,
        "height": 2400,
        "modelLoc": "高柜",
        "subCategory": "高柜"
    },
    "茶台": {
        "img_path": "./static/figures_imgs/teatable.svg",
        "png": "./static/figures_imgs/teatable.svg",
        "alias": "茶台",
        "length": 2000,
        "depth": 850,
        "height": 420,
        "modelLoc": "茶台",
        "subCategory": "茶台"
    },
    "新风机": {
        "img_path": "./static/figures_imgs/freshAirFan.svg",
        "png": "./static/figures_imgs/freshAirFan.svg",
        "alias": "新风机",
        "length": 1000,
        "depth": 500,
        'zval': 2565,
        "modelLoc": "新风机",
        "subCategory": "新风机"
    },
    "立式空调": {
        "img_path": "./static/figures_imgs/verticalChamber.svg",
        "png": "./static/figures_imgs/verticalChamber.svg",
        "alias": "立式空调",
        "length": 600,
        "depth": 560,
        "modelLoc": "空调",
        "subCategory": "立式空调",
        "off_land_rule": '落地'
    },
    "地柜": {
        "img_path": "./static/figures_imgs/rect_teapoy.svg",
        "png": "./static/figures_imgs/rect_teapoy.svg",
        "alias": "地柜",
        'length': 600,
        'depth': 550,
        "modelLoc": "地柜",
        "subCategory": "地柜",
        isFurniture: false
    },
    "壁挂空调": {
        "img_path": "./static/figures_imgs/wallAir.svg",
        "png": "./static/figures_imgs/wallAir.svg",
        "alias": "壁挂空调",
        "length": 1000,
        "depth": 200,
        "zval": 2000,
        "modelLoc": "空调",
        "subCategory": "壁挂空调",
        "off_land_rule": '离地'
    },
    "冰箱": {
        "img_path": "./static/figures_imgs/refrigerator.svg",
        "png": "./static/figures_imgs/refrigerator.svg",
        "alias": "冰箱",
        "length": 950,
        "depth": 700,
        "modelLoc": "冰箱",
        "subCategory": "冰箱"
    },
    "浴缸": {
        "img_path": "./static/figures_imgs/bathtub.svg",
        "png": "./static/figures_imgs/bathtub.svg",
        "alias": "浴缸",
        "length": 1600,
        "depth": 800,
        "modelLoc": "浴缸",
        "subCategory": "浴缸"
    },
    "床尾凳": {
        "img_path": "./static/figures_imgs/bedEndStool.svg",
        "png": "./static/figures_imgs/bedEndStool.svg",
        "alias": "床尾凳",
        "length": 1600,
        "depth": 700,
        "height": 450,
        "modelLoc": "床尾凳",
        "subCategory": "床尾凳"
    },
    "箭头": {
        "img_path": "./static/icons/arrow.svg",
        "png": "",
        "alias": "箭头",
        "length": 200,
        "depth": 30,
        "modelLoc": "箭头",
        "subCategory": "箭头",
        isFurniture: false
    },
    "旋转": {
        "img_path": "./static/icons/rotate.svg",
        "png": "",
        "alias": "旋转",
        "length": 30,
        "depth": 30,
        "modelLoc": "旋转",
        "subCategory": "旋转",
        isFurniture: false
    },
    "箭头hover": {
        "img_path": "./static/icons/arrow-hover.svg",
        "png": "",
        "alias": "箭头hover",
        "length": 200,
        "depth": 30,
        "modelLoc": "箭头hover",
        "subCategory": "箭头hover",
        isFurniture: false
    },
    "删除": {
        "img_path": "./static/icons/close.svg",
        "png": "",
        "alias": "删除",
        "length": 50,
        "depth": 50,
        "modelLoc": "删除",
        "subCategory": "删除",
        isFurniture: false
    },
    "单开门": {
        "img_path": "./static/icons/SingleDoor.svg",
        "png": "",
        "alias": "单开门",
        "length": 900,
        "depth": 120,
        "modelLoc": "单开门",
        "subCategory": "单开门"
    },
    "推拉门": {
        "img_path": "./static/icons/arrow-hover.svg",
        "png": "",
        "alias": "推拉门",
        "length": 2000,
        "depth": 120,
        "modelLoc": "推拉门",
        "subCategory": "推拉门"
    },
    "一字窗": {
        "img_path": "./static/icons/OneWindow.svg",
        "png": "",
        "alias": "一字窗",
        "length": 2000,
        "depth": 240,
        "modelLoc": "一字窗",
        "subCategory": "一字窗",
        isFurniture: false
    },
    "飘窗": {
        "img_path": "./static/icons/arrow-hover.svg",
        "png": "",
        "alias": "飘窗",
        "length": 2000,
        "depth": 750,
        "modelLoc": "飘窗",
        "subCategory": "飘窗",
        isFurniture: false
    },
    "双开门": {
        "img_path": "./static/figures_imgs/doubledoor.svg",
        "png": "./static/figures_imgs/doubledoor.png",
        "alias": "双开门",
        "length": 1200,
        "depth": 240,
        "modelLoc": "双开门",
        "subCategory": "双开门",
        isFurniture: false
    },
    "门洞": {
        "img_path": "./static/figures_imgs/doorhole.svg",
        "png": "./static/figures_imgs/doorhole.png",
        "alias": "门洞",
        "length": 960,
        "depth": 240,
        "modelLoc": "门洞",
        "subCategory": "门洞",
        isFurniture: false
    },
    "子母门": {
        "img_path": "./static/figures_imgs/safetydoor.svg",
        "png": "./static/figures_imgs/safetydoor.png",
        "alias": "子母门",
        "length": 1200,
        "depth": 240,
        "modelLoc": "子母门",
        "subCategory": "子母门",
        isFurniture: false
    },
    "垭口": {
        "img_path": "./static/figures_imgs/passDoor.svg",
        "png": "./static/figures_imgs/passDoor.png",
        "alias": "垭口",
        "length": 800,
        "depth": 240,
        "modelLoc": "垭口",
        "subCategory": "垭口",
        isFurniture: false
    },
    "栏杆": {
        "img_path": "./static/figures_imgs/Railing.svg",
        "png": "./static/figures_imgs/Railing.png",
        "alias": "栏杆",
        "length": 2000,
        "depth": 240,
        "height": 2700,
        "zval": 0,
        "modelLoc": "栏杆",
        "subCategory": "栏杆",
        isFurniture: false
    },

    "相机": {
        "img_path": "./static/figures_imgs/Camera.svg",
        "png": "./static/figures_imgs/Camera.png",
        "alias": "相机",
        "length": 500,
        "depth": 500,
        "height": 50,
        "zval": 1150,
        "rotation_z": Math.PI / 2,
        "modelLoc": "相机",
        "subCategory": "相机",
        isFurniture: false
    },
    "人物": {
        "img_path": "./static/figures_imgs/Man.svg",
        "png": "./static/figures_imgs/Man.png",
        "alias": "人物",
        "length": 500,
        "depth": 500,
        "zval": 1600,
        "modelLoc": "人物",
        "subCategory": "人物",
        isFurniture: false
    },
    "包管": {
        "img_path": "./static/figures_imgs/Phimosis.svg",
        "png": "./static/figures_imgs/Phimosis.png",
        "alias": "包管",
        "length": 350,
        "depth": 300,
        "modelLoc": "包管",
        "subCategory": "包管",
        isFurniture: false
    },
    "地台": {
        "img_path": "./static/figures_imgs/platform.svg",
        "png": "./static/figures_imgs/platform.png",
        "alias": "地台",
        "length": 1700,
        "depth": 900,
        "modelLoc": "地台",
        "subCategory": "地台",
        isFurniture: false
    },
    "方柱": {
        "img_path": "./static/figures_imgs/square_pillar.svg",
        "png": "./static/figures_imgs/square_pillar.png",
        "alias": "方柱",
        "length": 500,
        "depth": 500,
        "zval": 1600,
        "modelLoc": "方柱",
        "subCategory": "方柱",
        isFurniture: false
    },
    "横梁": {
        "img_path": "./static/figures_imgs/beam.svg",
        "png": "./static/figures_imgs/beam.png",
        "alias": "横梁",
        "length": 1200,
        "depth": 200,
        "modelLoc": "横梁",
        "subCategory": "横梁",
        isFurniture: false
    },
    "烟道": {
        "img_path": "./static/figures_imgs/flue.svg",
        "png": "./static/figures_imgs/flue.png",
        "alias": "烟道",
        "length": 400,
        "depth": 250,
        "modelLoc": "烟道",
        "subCategory": "烟道",
        isFurniture: false
    },
    "L型功能柜": {
        "img_path": "./static/figures_imgs/L型功能柜.png",
        "png": "./static/figures_imgs/L型功能柜.png",
        "alias": "L型功能柜",
        "length": 1550,
        "depth": 1550,
        "height": 750,
        "corner_width": 550,
        "corner_depth": 550,
        "modelLoc": "L型功能柜",
        "subCategory": "L型功能柜",
        "shape": "L形"
    },
    "L型功能衣柜": {
        "img_path": "./static/figures_imgs/L型功能衣柜.svg",
        "png": "./static/figures_imgs/L型功能衣柜.png",
        "alias": "L型功能衣柜",
        "length": 1050,
        "depth": 1620,
        "height": 2400,
        "corner_width": 550,
        "corner_depth": 600,
        "modelLoc": "L型功能衣柜",
        "subCategory": "L型功能衣柜",
        "shape": "L形"
    },
    "L型衣柜": {
        "img_path": "./static/figures_imgs/L型衣柜.svg",
        "png": "./static/figures_imgs/L型衣柜.png",
        "alias": "L型衣柜",
        "length": 1050,
        "depth": 1050,
        "height": 2400,
        "corner_width": 600,
        "corner_depth": 600,
        "modelLoc": "L型衣柜",
        "subCategory": "L型衣柜",
        "shape": "L形"
    },
    "一字形床屏": {
        "img_path": "./static/figures_imgs/L型衣柜.png",
        "png": "./static/figures_imgs/L型衣柜.png",
        "alias": "L型衣柜",
        "length": 1500,
        "depth": 80,
        "height": 2400,
        "modelLoc": "一字形床屏",
        "subCategory": "一字形床屏",
    },
    "L形床屏": {
        "img_path": "./static/figures_imgs/L形床屏.png",
        "png": "./static/figures_imgs/L形床屏.png",
        "alias": "L形床屏",
        "length": 1500,
        "depth": 600,
        "height": 2400,
        "modelLoc": "L形床屏",
        "subCategory": "L形床屏",
    },
    "侧边柜": {
        "img_path": "./static/figures_imgs/侧边柜.svg",
        "png": "./static/figures_imgs/侧边柜.png",
        "alias": "侧边柜",
        "length": 370,
        "depth": 620,
        "height": 2400,
        "modelLoc": "侧边柜",
        "subCategory": "侧边柜"
    },
    "单列功能衣柜": {
        "img_path": "./static/figures_imgs/单列功能衣柜.svg",
        "png": "./static/figures_imgs/单列功能衣柜.png",
        "alias": "单列功能衣柜",
        "length": 550,
        "depth": 1620,
        "height": 2400,
        "modelLoc": "单列功能衣柜",
        "subCategory": "单列功能衣柜"
    },
    "一字衣柜": {
        "img_path": "./static/figures_imgs/一字衣柜.svg",
        "png": "./static/figures_imgs/一字衣柜.png",
        "alias": "一字衣柜",
        "length": 900,
        "depth": 620,
        "height": 2400,
        "modelLoc": "一字衣柜",
        "subCategory": "一字衣柜"
    },
    "榻榻米脚踏": {
        "img_path": "./static/figures_imgs/榻榻米脚踏.svg",
        "png": "./static/figures_imgs/榻榻米脚踏.png",
        "alias": "榻榻米脚踏",
        "length": 1000,
        "depth": 300,
        "height": 420,
        "modelLoc": "榻榻米脚踏",
        "subCategory": "榻榻米脚踏"
    },
    "榻榻米": {
        "img_path": "./static/figures_imgs/榻榻米.svg",
        "png": "./static/figures_imgs/榻榻米.png",
        "alias": "榻榻米",
        "length": 1550,
        "depth": 2050,
        "height": 420,
        "modelLoc": "榻榻米",
        "subCategory": "榻榻米"
    },
    "榻榻米床品": {
        "img_path": "./static/figures_imgs/榻榻米床品.svg",
        "png": "./static/figures_imgs/榻榻米床品.png",
        "alias": "榻榻米床品",
        "length": 1500,
        "depth": 2000,
        "height": 100,
        "modelLoc": "榻榻米床品",
        "subCategory": "榻榻米床品"
    },
    "一字书柜": {
        "img_path": "./static/figures_imgs/一字书柜.svg",
        "png": "./static/figures_imgs/一字书柜.png",
        "alias": "一字书柜",
        "length": 900,
        "depth": 370,
        "height": 2400,
        "modelLoc": "一字书柜",
        "subCategory": "一字书柜"
    },
    "一字台面柜": {
        "img_path": "./static/figures_imgs/一字台面柜.svg",
        "png": "./static/figures_imgs/一字台面柜.png",
        "alias": "一字台面柜",
        "length": 1000,
        "depth": 620,
        "height": 750,
        "modelLoc": "一字台面柜",
        "subCategory": "一字台面柜"
    },
    "榻榻米组合": {
        "img_path": "./static/figures_imgs/tatamimat.svg",
        "png": "./static/figures_imgs/tatamimat.png",
        "alias": "榻榻米组合",
        "length": 2400,
        "depth": 2700,
        "height": 0,
        "modelLoc": "榻榻米组合",
        "subCategory": "榻榻米组合"
    },
    "L形榻榻米组合": {
        "img_path": "./static/figures_imgs/L_tatamimat.svg",
        "png": "./static/figures_imgs/L_tatamimat.png",
        "alias": "榻榻米组合",
        "length": 2700,
        "depth": 2700,
        "height": 0,
        "modelLoc": "榻榻米组合",
        "subCategory": "L形榻榻米组合"
    },
    "水吧台": {
        "img_path": "./static/figures_imgs/water_bar.svg",
        "png": "",
        "alias": "水吧台",
        "length": 600,
        "depth": 560,
        "height": 1000,
        "modelLoc": "水吧台",
        "public_category": "水吧台",
        "subCategory": "水吧台"
    },
    "无靠背沙发": {
        "img_path": "./static/figures_imgs/backless_sofa.svg",
        "png": "",
        "alias": "无靠背沙发",
        'length': 1800,
        'depth': 800,
        'height': 820,
        "modelLoc": "无靠背沙发",
        "public_category": "无靠背沙发",
        "subCategory": "无靠背沙发"
    },
    "壁灯": {
        "img_path": "./static/figures_imgs/wall_lamp.svg",
        "png": "",
        "alias": "壁灯",
        'length': 400,
        'depth': 450,
        'height': 400,
        "zval": 1400,
        "modelLoc": "壁灯",
        "public_category": "壁灯",
        "subCategory": "壁灯"
    },
    "跑步机": {
        "img_path": "./static/figures_imgs/fitness/跑步机.svg",
        "png": "./static/figures_imgs/fitness/跑步机.png",
        "alias": "跑步机",
        "length": 750,
        "depth": 1700,
        "height": 1200,
        "modelLoc": "健身-跑步机",
        "subCategory": "跑步机"
    },
    "健身架": {
        "img_path": "./static/figures_imgs/fitness/健身架.svg",
        "png": "./static/figures_imgs/fitness/健身架.png",
        "alias": "健身架",
        "length": 1600,
        "depth": 1200,
        "height": 1900,
        "modelLoc": "健身-健身架",
        "subCategory": "健身架"
    },
    "瑜伽垫": {
        "img_path": "./static/figures_imgs/fitness/瑜伽垫.svg",
        "png": "./static/figures_imgs/fitness/瑜伽垫.png",
        "alias": "瑜伽垫",
        "length": 800,
        "depth": 1800,
        "modelLoc": "健身-瑜伽垫",
        "subCategory": "瑜伽垫"
    },

    "投影仪": {
        "img_path": "./static/figures_imgs/tv/投影仪.svg",
        "png": "./static/figures_imgs/tv/投影仪.png",
        "alias": "投影仪",
        "length": 570,
        "depth": 500,
        "height": 100,
        'zval': 2400,
        "modelLoc": "影音-投影仪",
        "subCategory": "投影仪"
    },
    "投影布": {
        "img_path": "./static/figures_imgs/tv/投影布.svg",
        "png": "./static/figures_imgs/tv/投影布.png",
        "alias": "投影布",
        "length": 2650,
        "depth": 320,
        "height": 1600,
        "modelLoc": "影音-投影布",
        "subCategory": "投影布"
    },
    "音响": {
        "img_path": "./static/figures_imgs/tv/音响.svg",
        "png": "./static/figures_imgs/tv/音响.png",
        "alias": "音响",
        "length": 230,
        "depth": 230,
        "height": 280,
        "modelLoc": "影音-音响",
        "subCategory": "音响"
    },


    // "L型岛台组合": {
    //   "img_path": "./static/figures_imgs/L型岛台组合.svg",
    //   "png": "./static/figures_imgs/L型岛台组合.png",
    //   "alias": "L型岛台组合",
    //   "length": 2000,
    //   "depth": 2050,
    //   "height":2400,
    //   "corner_width":600,
    //   "corner_depth":600,
    //   "modelLoc": "L型岛台组合",
    //   "subCategory": "L型岛台组合",
    //   "shape": "L形"
    // },
}

export var g_figure_alias_dict: { [key: string]: string } = {};
for (let key in g_FigureImagePaths) {
    if (g_FigureImagePaths[key].alias) {
        g_figure_alias_dict[g_FigureImagePaths[key].alias] = key;
    }
}

let public_category_2_figure_label_dict: { [key: string]: string } = {};
let model_loc_2_figure_label_dict: { [key: string]: string } = {};
for (let key in g_FigureImagePaths) {
    if (g_FigureImagePaths[key].public_category) {
        let attr = g_FigureImagePaths[key].public_category;
        if (attr) {
            public_category_2_figure_label_dict[attr] = g_FigureImagePaths[key].alias || "";
        }
    }
    if (g_FigureImagePaths[key].modelLoc) {
        let attr = g_FigureImagePaths[key].modelLoc;
        if (attr) {
            model_loc_2_figure_label_dict[attr] = g_FigureImagePaths[key].alias || "";
        }
    }
    if (g_FigureImagePaths[key].img_path) {
        g_FigureImagePaths[key].img_path = g_FigureImagePaths[key].img_path;
    }
}

export function determineFigureLabel(publicCategory: string, modelLoc: string, sub_category: string = ""): string {

    if (sub_category) {
        if (g_FigureImagePaths[sub_category]) return sub_category;
    }
    let figureLabel: string = public_category_2_figure_label_dict[publicCategory];
    if (!figureLabel && modelLoc) {
        figureLabel = model_loc_2_figure_label_dict[modelLoc];
    }
    return figureLabel;
}