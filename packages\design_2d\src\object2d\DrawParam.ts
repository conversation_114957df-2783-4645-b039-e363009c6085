

/**
* @description canvas 绘制参数
* <AUTHOR>
* @date 2025-06-28
* @lastEditTime 2025-06-28 11:20:47
* @lastEditors xuld
*/
export class DrawParam {
    // 填充颜色
    protected _fillStyle: string = "#fff";
    // 描边颜色
    protected _strokeStyle: string = "#000";
    // 描边宽度
    protected _lineWidth: number = 1;
    // 字体大小
    protected _fontSize: number = 10;
    // 透明度
    protected _alpha: number = 1;

    get fillStyle() {
        return this._fillStyle;
    }

    set fillStyle(value: string) {
        this._fillStyle = value;
    }

    get strokeStyle() {
        return this._strokeStyle;
    }

    set strokeStyle(value: string) {
        this._strokeStyle = value;
    }

    get lineWidth() {
        return this._lineWidth;
    }

    set lineWidth(value: number) {
        this._lineWidth = value;
    }

    get fontSize() {
        return this._fontSize;
    }

    set fontSize(value: number) {
        this._fontSize = value;
    }

    get alpha() {
        return this._alpha;
    }

    set alpha(value: number) {
        this._alpha = value;
    }
}