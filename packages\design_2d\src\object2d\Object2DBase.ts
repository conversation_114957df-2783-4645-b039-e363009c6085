import { generateUUID } from "three/src/math/MathUtils";
import { compareNames } from "z_polygon";

import { DomainApiHub, IEntityBase } from "@layoutai/design_domain";

import { TGraphBasicConfigs } from "../config/TGraphBasicConfigs";
import { TPainter } from "../draw/TPainter";
import { DrawParam } from "./DrawParam";


/**
 * 2D 对象基类
 */
export class Object2DBase {
    private _entityUuid: string;
    private _uuid: string = generateUUID();
    // 绘制参数
    protected _drawParam: DrawParam = new DrawParam();

    constructor(uuid: string) {
        this._entityUuid = uuid;
    }

    public get uuid(): string {
        return this._uuid;
    }

    public get entity(): IEntityBase | undefined {
        let hub: DomainApiHub = DomainApiHub.instance;
        if (!hub) {
            console.error("DomainApiHub 未初始化");
            return;
        }
        return hub.getEntity(this._entityUuid);
    }


    public render(painter: TPainter): void {
    }

    public update(): any | undefined {
    }

    public updateDrawParam(ctx: CanvasRenderingContext2D, drawParam: DrawParam): void {
        // 设置绘制参数
        ctx.fillStyle = drawParam.fillStyle;
        ctx.strokeStyle = drawParam.strokeStyle;
        ctx.lineWidth = drawParam.lineWidth;
        ctx.font = `${drawParam.fontSize}px`;
        ctx.globalAlpha = drawParam.alpha;

        this._drawParam = drawParam;
    }

    public static getDrawingOrder(names: string[]): number {
        let level_order = 6;
        let sub_level_order = 0;
        let is_found = false;
        for (let li in TGraphBasicConfigs.OnDrawingLevels) {

            for (let id in TGraphBasicConfigs.OnDrawingLevels[li]) {
                let name = TGraphBasicConfigs.OnDrawingLevels[li][id];

                if (compareNames(names, [name], false)) {
                    level_order = ~~li;
                    sub_level_order = ~~id;
                    is_found = true;
                    break;
                }
            }
            if (is_found) break;
        }

        return level_order + sub_level_order / 100;
    }
} 