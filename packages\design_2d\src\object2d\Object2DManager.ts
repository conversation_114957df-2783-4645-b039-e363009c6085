

import { DomainApiHub, EntityType, IEntityBase } from "@layoutai/design_domain";

import { TPainter } from "../draw/TPainter";
import { Furniture2D } from "./Furniture2D";
import { FurnitureGroup2D } from "./FurnitureGroup2D";
import { Object2DBase } from "./Object2DBase";
import { Room2D } from "./Room2D";
import { Wall2D } from "./Wall2D";
import { Window2D } from "./Window2D";


/**
 * 2D对象管理器
 */
export class Object2DManager {
    private static _instance: Object2DManager;
    private _isInit: boolean = false;
    private _object2DMap: Map<string, Object2DBase> = new Map();
    private _entityClassMap: Map<EntityType, typeof Object2DBase> = new Map();
    private _ctx: CanvasRenderingContext2D | undefined;
    private _painter: TPainter | undefined;

    public static get instance(): Object2DManager {
        if (!this._instance) {
            this._instance = new Object2DManager();
        }
        return this._instance;
    }

    public init(ctx: CanvasRenderingContext2D): void {
        if (this._isInit) {
            return;
        }
        this._isInit = true;
        this._ctx = ctx;
        this._painter = new TPainter(this._ctx.canvas);
        this._painter.p_scale = 1;
        this._registerEntityObject2D();
        this._clearAllObjects();
    }

    private _registerEntityObject2D(): void {
        this._entityClassMap.set(EntityType.wall, Wall2D);
        this._entityClassMap.set(EntityType.room, Room2D);
        this._entityClassMap.set(EntityType.furniture, Furniture2D);
        this._entityClassMap.set(EntityType.furnitureGroup, FurnitureGroup2D);
        this._entityClassMap.set(EntityType.window, Window2D);
    }

    public createObject2DByEntity(entity: IEntityBase): Object2DBase | undefined {
        let cls = this._entityClassMap.get(entity.entityType);
        if (!cls) {
            console.warn(`未找到实体类型 ${entity.entityType} 对应的 2D 对象`);
            return;
        }

        let obj2D = new cls(entity.uuid);
        this._object2DMap.set(entity.uuid, obj2D);
        return obj2D;
    }

    /**
     * 通过类型和UUID直接创建2D对象
     * @param entityType 实体类型
     * @param uuid 实体UUID
     * @returns 创建的2D对象
     */
    public createObject2D(entityType: EntityType, uuid: string): Object2DBase | undefined {
        let cls = this._entityClassMap.get(entityType);
        if (!cls) {
            console.warn("实体类型未注册显示对象", entityType);
            return;
        }

        let obj2D = new cls(uuid);
        this._object2DMap.set(uuid, obj2D);
        return obj2D;
    }

    public getObject2D(uuid: string): Object2DBase | undefined {
        return this._object2DMap.get(uuid);
    }

    public removeObject2D(uuid: string): boolean {
        return this._object2DMap.delete(uuid);
    }

    public getAllObjects2D(): Object2DBase[] {
        return Array.from(this._object2DMap.values());
    }

    public updateObject2D(): void {
        let curUUIDs: Map<string, boolean> = new Map();

        let entities = DomainApiHub.instance.getAllEntities();
        for (let entity of entities) {
            let obj2D = this._object2DMap.get(entity.uuid);
            if (!obj2D) {
                obj2D = this.createObject2D(entity.entityType, entity.uuid);
            }

            if (obj2D) {
                obj2D.update();
                if (this._painter) {
                    obj2D.render(this._painter);
                }
            }

            curUUIDs.set(entity.uuid, true);
        }

        let removeUUIDs: string[] = [];
        for (let uuid of this._object2DMap.keys()) {
            if (!curUUIDs.has(uuid)) {
                removeUUIDs.push(uuid);
            }
        }
        for (let uuid of removeUUIDs) {
            this._object2DMap.delete(uuid);
        }
    }

    public clearAllObjects(): void {
        this._clearAllObjects();
    }

    private _clearAllObjects(): void {
        this._object2DMap.clear();
    }

    public dispose(): void {
        this._clearAllObjects();
        this._isInit = false;
    }
} 