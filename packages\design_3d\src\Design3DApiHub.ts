

import { Object3DManager } from "./object3d/Object3DManager";
import { SceneManager } from "./scene/SceneManager";
import { DebugUtils } from "./utils/DebugUtils";


/**
* @description description
* <AUTHOR>
* @date 2025-06-18
* @lastEditTime 2025-06-18 14:10:25
* @lastEditors xuld
*/
export class Design3DApiHub {

    private static _instance: Design3DApiHub;
    private _isInit: boolean = false;

    public static get instance(): Design3DApiHub {
        if (!this._instance) {
            this._instance = new Design3DApiHub();
        }
        return this._instance;
    }

    /**
     * @description 初始化
     */
    public init(): void {
        if (this._isInit) {
            return;
        }
        this._isInit = true;
        SceneManager.instance.init();
    }

    /**
     * @description 绑定主div
     * @param div 主div
     */
    public bindMainDiv(div: HTMLDivElement): void {
        SceneManager.instance.bindMainDiv(div);
        SceneManager.instance.startRender();
    }

    /**
     * @description 更新场景
     * @param force 是否强制更新
     */
    public async updateScene3D(force: boolean = false): Promise<void> {
        return SceneManager.instance.updateScene3D(force);
    }
}