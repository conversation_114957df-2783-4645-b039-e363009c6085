import { BufferGeometry, Mesh } from "three";

import { DomainApiHub, IEntityRoom } from "@layoutai/design_domain";

import { MeshName } from "../const/MeshName";
import { SwitchConfig } from "../const/SwitchConfig";
import { CeilingMeshBuilder } from "./builder/CeilingMeshBuilder";
import { SimpleCeilingMaterial } from "./material/SimpleCeilingMaterial";


/**
* @description 天花板 3D 对象
* <AUTHOR>
* @date 2025-06-20 10:09:36
* @lastEditTime 2025-06-20 10:09:36
* @lastEditors xuld
*/

export class Ceiling3D {
    private _ceilingMesh: Mesh;
    private _roomUuid: string;

    constructor(roomUuid: string) {
        this._roomUuid = roomUuid;

        this._ceilingMesh = new Mesh(new BufferGeometry(), SimpleCeilingMaterial.create());
        this._ceilingMesh.name = MeshName.Ceiling;
        this._ceilingMesh.receiveShadow = SwitchConfig.shadowSwitch;
    }

    public get ceilingMesh(): Mesh {
        return this._ceilingMesh;
    }

    public get room(): IEntityRoom | undefined {
        return DomainApiHub.instance.getEntity(this._roomUuid) as IEntityRoom;
    }

    public async update(): Promise<Mesh> {
        if (!this.room) {
            console.error("天花板没有绑定房间，无法更新");
            return this._ceilingMesh;
        }
        let offset = 1;
        let ceilingZval = this.room.height - offset;
        this._ceilingMesh.geometry = CeilingMeshBuilder.buildMesh(this._ceilingMesh, this.room.roomPoly, ceilingZval);
        return this._ceilingMesh;
    }
}