import { Box3, <PERSON>, Mesh, Object3D } from "three";
import { IEntityDoor } from "@layoutai/design_domain";
import { Object3DBase } from "./Object3DBase";
import { BoxMeshBuilder } from "./builder/BoxMeshBuilder";
import { MeshName } from "../const/MeshName";
import { UserDataKey } from "../const/UserDataKey";
import { ModelLoader } from "../utils/ModelLoader";
import { ResourceTracker } from "../utils/ResourceTracker";


/**
* @description 门 3D 对象
*/
export class Door3D extends Object3DBase {
    private _boxMesh: Mesh = BoxMeshBuilder.createBoxMesh();
    private _model: Object3D | undefined;

    constructor(uuid: string) {
        super(uuid);
        this._object3D = new Group();
        this._object3D.name = MeshName.WinDoor;
        this._object3D.add(this._boxMesh);
        this._object3D.userData[UserDataKey.EntityOfMesh] = true;
    }

    public get door(): IEntityDoor {
        return this.entity as IEntityDoor;
    }

    public async update(): Promise<Object3D | undefined> {
        this.updateBoxMesh();
        await this.updateModel();
        if (this._object3D) {
            this._object3D.userData[UserDataKey.EntityOfMesh] = this;
        }
        return this._object3D;
    }

    private updateBoxMesh() {
        this._boxMesh.visible = true;

        let pos = this.door.rect.rect_center_3d.clone();
        this._boxMesh.position.copy(pos as any);
        this._boxMesh.rotation.set(0, 0, this.door.rect.rotation_z);
        this._boxMesh.scale.set(this.door.length, this.door.width, this.door.height);
    }

    private async updateModel() {
        if (this._model) {
            this._model.removeFromParent();
            ResourceTracker.disposeObject(this._model);
        }

        if (!this.door.materialId) {
            console.warn("Door3D.updateModel: 门没有材质ID", this.door);
            return;
        }
        let dvo = await this.door.materialInfo();
        if (!dvo) {
            console.warn("Door3D.updateModel: 门没有材质信息", this.door);
            return;
        }
        let groupNode = await ModelLoader.loadModel(dvo, false);
        if (!groupNode) {
            console.warn("Door3D.updateModel: 门加载失败", this.door);
            return;
        }

        if (this._object3D) {
            this._object3D.add(groupNode);
        }
        this.updateModelPose(groupNode);

        this._model = groupNode;
        this._boxMesh.visible = false;
    }

    private updateModelPose(groupNode: Group) {
        let box = new Box3();
        box.setFromObject(groupNode);
        let boxLength = box.max.x - box.min.x;
        let boxWidth = box.max.y - box.min.y;
        let boxHeight = box.max.z - box.min.z;
        groupNode.position.set(this.door.x, this.door.y, this.door.z);
        groupNode.rotation.set(0, 0, this.door.rect.rotation_z);
        groupNode.scale.set(this.door.length / boxLength, this.door.width / boxWidth, this.door.height / boxHeight);
    }
}