import { Object3D } from "three";
import { generateUUID } from "three/src/math/MathUtils";

import { DomainApiHub, IEntityBase } from "@layoutai/design_domain";


/**
* @description 3D 对象基类
* <AUTHOR>
* @date 2025-06-18
* @lastEditTime 2025-06-18 15:27:34
* @lastEditors xuld
*/
export class Object3DBase {
    private _uuid: string = generateUUID();
    private _entityUuid: string;
    protected _object3D: Object3D | undefined;

    constructor(uuid: string) {
        this._entityUuid = uuid;
    }

    public get uuid(): string {
        return this._uuid;
    }

    public get entity(): IEntityBase | undefined {
        let hub: DomainApiHub = DomainApiHub.instance;
        if (!hub) {
            console.error("DomainApiHub 未初始化");
            return;
        }
        return hub.getEntity(this._entityUuid);
    }

    public get object3D(): Object3D | undefined {
        return this._object3D;
    }

    public async update(): Promise<Object3D | undefined> {
        return this._object3D;
    }
}