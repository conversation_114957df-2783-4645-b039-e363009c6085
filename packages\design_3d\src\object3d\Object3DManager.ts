import { Group, Object3D } from "three";

import { DomainApiHub, EntityType, IEntityBase } from "@layoutai/design_domain";

import { DisposeUtils } from "../utils/DisposeUtils";
import { Furniture3D } from "./Furniture3D";
import { Object3DBase } from "./Object3DBase";
import { Room3D } from "./Room3D";
import { Wall3D } from "./Wall3D";
import { Window3D } from "./Window3D";
import { Door3D } from "./Door3D";
import { FurnitureGroup3D } from "./FurnitureGroup3D";
import { SceneManager } from "../scene/SceneManager";


/**
* @description 3D 对象管理器
* <AUTHOR>
* @date 2025-06-18
* @lastEditTime 2025-06-18 14:04:32
* @lastEditors xuld
*/
export class Object3DManager {

    private _entityClassMap: Map<EntityType, [typeof Object3DBase, Group]> = new Map();
    private _object3DMap: Map<string, Object3DBase> = new Map();

    public init(): void {
        this._registerEntityObject3D();
    }

    private _registerEntityObject3D(): void {
        this._entityClassMap.set(EntityType.wall, [Wall3D, SceneManager.instance.groupWall]);
        this._entityClassMap.set(EntityType.room, [Room3D, SceneManager.instance.groupRoom]);
        this._entityClassMap.set(EntityType.furniture, [Furniture3D, SceneManager.instance.groupFurniture]);
        this._entityClassMap.set(EntityType.window, [Window3D, SceneManager.instance.groupDoorWindow]);
        this._entityClassMap.set(EntityType.door, [Door3D, SceneManager.instance.groupDoorWindow]);
        this._entityClassMap.set(EntityType.furnitureGroup, [FurnitureGroup3D, SceneManager.instance.groupFurniture]);
    }

    public createObject3DByEntity(entity: IEntityBase): Object3DBase | undefined {
        let item = this._entityClassMap.get(entity.entityType);

        if (!item) {
            console.warn(`没有对实体类型进行注册: ${entity.entityType}`);
            return;
        }

        let cls = item[0];
        if (!cls) {
            console.warn(`没有注册显示对象的实体类型: ${entity.entityType}`);
            return;
        }

        let obj3D = new cls(entity.uuid);
        this._object3DMap.set(entity.uuid, obj3D);

        let parent = item[1];
        if (!parent) {
            console.warn(`没有注册显示对象的父对象: ${entity.entityType} ${entity.uuid}`);
            return;
        }

        if (obj3D.object3D) {
            parent.add(obj3D.object3D);
        }

        return obj3D;
    }

    public clearObject3D(): void {
        for (let obj3D of this._object3DMap.values()) {
            if (obj3D.object3D) {
                DisposeUtils.disposeObject(obj3D.object3D);
            }
        }
        this._object3DMap.clear();
    }

    public async updateObject3D(): Promise<void> {
        let entities = DomainApiHub.instance.getAllEntities();

        let promises: Promise<Object3D | undefined>[] = [];

        let curUUIDs: Map<string, boolean> = new Map();
        for (let entity of entities) {
            let obj3D = this._object3DMap.get(entity.uuid);
            if (!obj3D) {
                obj3D = this.createObject3DByEntity(entity);
                if (!obj3D) {
                    console.warn(`显示对象创建失败: ${entity.entityType} ${entity.uuid}`);
                    continue;
                }
            }
            curUUIDs.set(entity.uuid, true);

            if (obj3D) {
                promises.push(obj3D.update());
            }
        }

        await Promise.allSettled(promises);

        let removeUUIDs: string[] = [];
        for (let uuid of this._object3DMap.keys()) {
            if (!curUUIDs.has(uuid)) {
                removeUUIDs.push(uuid);
            }
        }
        for (let uuid of removeUUIDs) {
            let obj3D = this._object3DMap.get(uuid);
            if (obj3D && obj3D.object3D) {
                DisposeUtils.disposeObject(obj3D.object3D);
            }
            this._object3DMap.delete(uuid);
        }
    }
}
