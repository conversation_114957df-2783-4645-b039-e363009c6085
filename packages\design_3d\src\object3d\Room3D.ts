import { IEntityRoom } from "@layoutai/design_domain";
import { Object3DBase } from "./Object3DBase";
import { Group, Mesh, Object3D } from "three";
import { MeshName } from "../const/MeshName";
import { SwitchConfig } from "../const/SwitchConfig";
import { GeometryBuilder } from "./builder/GeometryBuilder";
import { InnerWallGeometryBuilder } from "./builder/InnerWallGeometryBuilder";
import { Ceiling3D } from "./Ceiling3D";


/**
* @description 房间 3D 对象
* <AUTHOR>
* @date 2025-06-18
* @lastEditTime 2025-06-18 15:26:53
* @lastEditors xuld
*/
export class Room3D extends Object3DBase {

    private _floorMesh: Mesh;
    private _innerWallMesh: Mesh;
    private _ceiling3D: Ceiling3D;

    constructor(uuid: string) {
        super(uuid);
        this._object3D = new Group();
        this._object3D.name = MeshName.Room;

        this._floorMesh = new Mesh();
        this._floorMesh.name = MeshName.Floor;
        this._floorMesh.receiveShadow = SwitchConfig.shadowSwitch;
        this._object3D.add(this._floorMesh);

        this._innerWallMesh = new Mesh();
        this._innerWallMesh.name = MeshName.InnerWall;
        this._innerWallMesh.receiveShadow = SwitchConfig.shadowSwitch;
        this._object3D.add(this._innerWallMesh);

        this._ceiling3D = new Ceiling3D(uuid);
        this._object3D.add(this._ceiling3D.ceilingMesh);
    }

    public get room(): IEntityRoom | undefined {
        return this.entity as IEntityRoom;
    }


    public async update(): Promise<Object3D | undefined> {
        this._updateFloorMesh();
        this._updateInnerWallMesh();
        this._ceiling3D.update();
        return this._object3D;
    }

    private _updateFloorMesh(): Mesh {
        if (!this.room) {
            console.error("房间不存在，无法更新地板");
            return this._floorMesh;
        }
        this._floorMesh.geometry = GeometryBuilder.makePolyGeometry(this.room.roomPoly.clone().expandPolygon(60));
        return this._floorMesh;
    }

    private _updateInnerWallMesh(): Mesh {
        if (!this.room) {
            console.error("房间不存在，无法更新内墙");
            return this._innerWallMesh;
        }
        this._innerWallMesh.geometry = InnerWallGeometryBuilder.build(this.room.roomPoly.clone());
        return this._innerWallMesh;
    }
}