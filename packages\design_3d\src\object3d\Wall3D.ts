import { IEntityWall } from "@layoutai/design_domain";
import { Object3DBase } from "./Object3DBase";
import { BufferGeometry, Material, Mesh, Object3D } from "three";
import { WallGeometryBuilder } from "./builder/WallGeometryBuilder";
import { SwitchConfig } from "../const/SwitchConfig";
import { MeshName } from "../const/MeshName";
import { UserDataKey } from "../const/UserDataKey";
import { SimpleSolidWallMaterial } from "./material/SimpleSolidWallMaterial";


/**
* @description 墙 3D 对象
* <AUTHOR>
* @date 2025-06-18
* @lastEditTime 2025-06-18 15:26:53
* @lastEditors xuld
*/
export class Wall3D extends Object3DBase {
    private static _wallMaterial: Material;

    public static get wallMaterial(): Material {
        if (!Wall3D._wallMaterial) {
            Wall3D._wallMaterial = SimpleSolidWallMaterial.create();
        }
        return Wall3D._wallMaterial;
    }

    constructor(uuid: string) {
        super(uuid);
        this._object3D = new Mesh(new BufferGeometry(), Wall3D.wallMaterial);
        this._object3D.receiveShadow = SwitchConfig.shadowSwitch;
        this._object3D.name = MeshName.Wall;
        this._object3D.userData[UserDataKey.SkipOutline] = true;
    }

    public get wall(): IEntityWall {
        return this.entity as IEntityWall;
    }

    public async update(): Promise<Object3D | undefined> {
        let mesh = this._object3D as Mesh;
        let geometry = WallGeometryBuilder.build(this.wall);
        mesh.geometry = geometry;

        mesh.position.copy(this.wall.rect.rect_center);
        mesh.rotation.set(0, 0, this.wall.rect.rotation_z);

        mesh.userData[UserDataKey.EntityOfMesh] = this;
        return mesh;
    }
}