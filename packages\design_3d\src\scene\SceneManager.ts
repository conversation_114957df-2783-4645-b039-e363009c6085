import { BoxGeometry, Camera, Group, Mesh, MeshBasicMaterial, Object3D, OrthographicCamera, PerspectiveCamera, Scene, Vector2, WebGLRenderer } from "three";

import { RootGroupName } from "../const/RootGroupName";
import { CameraViewMode } from "../const/SceneMode";
import { UserDataKey } from "../const/UserDataKey";
import { LightManager } from "./LightManager";
import { OutlinePostProcess } from "./OutlinePostProcess";
import { SimpleGridHelper } from "./sky/SimpleGridHelper";
import { Sky } from "./sky/sky";
import { DisposeUtils } from "../utils/DisposeUtils";
import { Object3DManager } from "../object3d/Object3DManager";
import { EntityType } from "@layoutai/design_domain";
import { OrbitControls } from "three/examples/jsm/Addons";
import { MaterialManager } from "../object3d/material/MaterialManager";


/**
* @description 场景管理器
* <AUTHOR>
* @date 2025-06-18
* @lastEditTime 2025-06-18 14:07:52
* @lastEditors xuld
*/
export class SceneManager {

    private static _instance: SceneManager;

    public static get instance(): SceneManager {
        if (!this._instance) {
            this._instance = new SceneManager();
        }
        return this._instance;
    }

    private _isRendering: boolean = false;

    private _parentDiv: HTMLDivElement | undefined;
    private _scene!: Scene;
    private _groupRoot!: Group;
    private _renderer!: WebGLRenderer;
    private _outlinePostProcess!: OutlinePostProcess;

    private _groupWall!: Group;
    private _groupFurniture!: Group;
    private _groupDoorWindow!: Group;
    private _groupRoom!: Group;

    private _groupDayLight!: Group;
    private _groupDecoration!: Group;
    private _groupNightLight!: Group;
    private _groupAiLight!: Group;
    private _groupLightModel!: Group;

    private _gridHelper!: SimpleGridHelper;
    private _control: OrbitControls | null = null;
    private _sky!: Sky;


    private _pixelRatio: number = 1;
    private _viewWidth: number = 900;
    private _viewHeight: number = 900;
    private _cameraViewMode: CameraViewMode = CameraViewMode.BirdsEye;
    private _cameraMap: Map<CameraViewMode, Camera> = new Map();

    private _o3dMgr: Object3DManager = new Object3DManager();

    public init(): void {
        this._scene = this._createScene();
        this._renderer = this._createRenderer(this._pixelRatio);
        this._createCamera();
        this._outlinePostProcess = this._createOutlinePostProcess();
        this._createLight();

        this._control = new OrbitControls(this.camera, this._renderer.domElement);

        this.onSizeChanged();
        this._o3dMgr.init();
    }

    private _createScene(): Scene {
        const scene = new Scene();

        this._gridHelper = new SimpleGridHelper(500000, 500, 0xffffff, 0xffffff);
        this._gridHelper.userData[UserDataKey.SkipOutline] = true;
        this._gridHelper.rotateX(Math.PI / 2);
        this._gridHelper.position.set(0, 0, -10);
        scene.add(this._gridHelper);

        this._sky = new Sky();
        this._sky.userData[UserDataKey.SkipOutline] = true;
        this._sky.scale.setScalar(450000);
        scene.add(this._sky);

        this._groupRoot = new Group();
        this._groupRoot.name = RootGroupName.Root;
        scene.add(this._groupRoot);

        this._groupWall = new Group();
        this._groupWall.name = RootGroupName.Walls;
        this._groupRoot.add(this._groupWall);

        this._groupFurniture = new Group();
        this._groupFurniture.name = RootGroupName.Furniture;
        this._groupRoot.add(this._groupFurniture);

        this._groupDoorWindow = new Group();
        this._groupDoorWindow.name = RootGroupName.Windows;
        this._groupRoot.add(this._groupDoorWindow);

        this._groupRoom = new Group();
        this._groupRoom.name = RootGroupName.Rooms;
        this._groupRoot.add(this._groupRoom);

        this._groupDayLight = new Group();
        this._groupDayLight.name = RootGroupName.DayLights;
        this._groupRoot.add(this._groupDayLight);

        this._groupDecoration = new Group();
        this._groupDecoration.name = RootGroupName.DecorationLights;
        this._groupRoot.add(this._groupDecoration);

        this._groupNightLight = new Group();
        this._groupNightLight.name = RootGroupName.NightLights;
        this._groupNightLight.visible = false;
        this._groupRoot.add(this._groupNightLight);

        this._groupAiLight = new Group();
        this._groupAiLight.name = RootGroupName.NightLights;
        this._groupRoot.add(this._groupAiLight);

        this._groupLightModel = new Group();
        this._groupLightModel.name = RootGroupName.NightLights;
        this._groupRoot.add(this._groupLightModel);

        return scene;
    }

    private _createRenderer(pixelRatio: number): WebGLRenderer {
        let renderer = new WebGLRenderer({
            antialias: false,
            preserveDrawingBuffer: true,
            alpha: true
        });
        renderer.shadowMap.enabled = true;
        renderer.setPixelRatio(pixelRatio);
        renderer.setSize(this._viewWidth, this._viewHeight);
        return renderer;
    }

    private _createCamera(): void {
        let roamingCamera = new PerspectiveCamera(65, 1, 300, 20000);
        roamingCamera.up.set(0, 0, 1);
        roamingCamera.position.set(0, 0, 1200);
        roamingCamera.rotateX(Math.PI / 2);
        roamingCamera.updateProjectionMatrix();
        this._cameraMap.set(CameraViewMode.Roaming, roamingCamera);

        let birdsEyeCamera = new PerspectiveCamera(15, 1, 5000, 200000);
        roamingCamera.up.set(0, 0, 1);
        birdsEyeCamera.lookAt(0, 0, 0);
        birdsEyeCamera.position.set(0, 0, 60000);
        birdsEyeCamera.updateProjectionMatrix();
        this._cameraMap.set(CameraViewMode.BirdsEye, birdsEyeCamera);

        let orthographicCamera = new OrthographicCamera(-1000, 1000, 1000, -1000, 1, 100000);
        orthographicCamera.lookAt(0, 0, 0);
        orthographicCamera.up.set(0, 1, 0);
        orthographicCamera.position.set(0, 0, 0);
        orthographicCamera.updateProjectionMatrix();
        this._cameraMap.set(CameraViewMode.Orthographic, orthographicCamera);
    }

    private _createOutlinePostProcess(): OutlinePostProcess {
        return new OutlinePostProcess(
            this._renderer,
            this._scene,
            this.camera,
            this._viewWidth,
            this._viewHeight,
        );
    }

    private _createLight(): void {
        LightManager.createDayLight(this._groupDayLight);
        LightManager.createNightLight(this._groupNightLight);
    }

    public get camera(): Camera {
        return this._cameraMap.get(this._cameraViewMode)!;
    }

    public get groupWall(): Group {
        return this._groupWall;
    }

    public get groupFurniture(): Group {
        return this._groupFurniture;
    }

    public get groupDoorWindow(): Group {
        return this._groupDoorWindow;
    }

    public get groupRoom(): Group {
        return this._groupRoom;
    }

    public clearScene3D(): void {
        DisposeUtils.disposeObject(this._groupWall);
        this._groupWall.remove(...this._groupWall.children);

        DisposeUtils.disposeObject(this._groupFurniture);
        this._groupFurniture.remove(...this._groupFurniture.children);

        DisposeUtils.disposeObject(this._groupDoorWindow);
        this._groupDoorWindow.remove(...this._groupDoorWindow.children);

        DisposeUtils.disposeObject(this._groupRoom);
        this._groupRoom.remove(...this._groupRoom.children);
    }

    public bindMainDiv(div: HTMLDivElement): void {
        if (this._parentDiv) {
            this.stopRender();
            this._parentDiv.removeChild(this._renderer.domElement);
            this._parentDiv = undefined;
        }

        this._parentDiv = div;
        this._renderer.setSize(div.clientWidth * this._pixelRatio, div.clientHeight * this._pixelRatio);
        div.appendChild(this._renderer.domElement);
    }

    public setSize(width: number, height: number): void {
        this._viewWidth = width;
        this._viewHeight = height;
        this.onSizeChanged();
    }

    private onSizeChanged(): void {
        this._renderer.setSize(this._viewWidth, this._viewHeight);
        this._outlinePostProcess.onResize(this._viewWidth, this._viewHeight);
        MaterialManager.updateResolution(new Vector2(this._viewWidth, this._viewHeight));

        for (let camera of this._cameraMap.values()) {
            if (camera instanceof PerspectiveCamera) {
                camera.aspect = this._viewWidth / this._viewHeight;
                camera.updateProjectionMatrix();
            }
            else if (camera instanceof OrthographicCamera) {
                camera.left = -this._viewWidth / 2;
                camera.right = this._viewWidth / 2;
                camera.top = this._viewHeight / 2;
                camera.bottom = -this._viewHeight / 2;
                camera.updateProjectionMatrix();
            }
        }
    }

    public setPixelRatio(pixelRatio: number): void {
        this._pixelRatio = pixelRatio;
        this._renderer.setPixelRatio(pixelRatio);
    }

    public startRender(): void {
        if (!this._scene) return;
        this._isRendering = true;
        this._renderer.setAnimationLoop(this.onUpdate.bind(this));

        this.showCoordSystem();
    }

    public showCoordSystem(): void {
        const scene = this.scene;
        const boxGeometry = new BoxGeometry(100, 100, 100);

        const materialO = new MeshBasicMaterial({ color: 0x000000 });
        const boxO = new Mesh(boxGeometry, materialO);
        boxO.position.set(0, 0, 0);
        scene.add(boxO);

        // X轴上的box
        const materialX = new MeshBasicMaterial({ color: 0xff0000 });
        const boxX = new Mesh(boxGeometry, materialX);
        boxX.position.set(1000, 0, 0);
        scene.add(boxX);

        // Y轴上的box 
        const materialY = new MeshBasicMaterial({ color: 0x00ff00 });
        const boxY = new Mesh(boxGeometry, materialY);
        boxY.position.set(0, 1000, 0);
        scene.add(boxY);

        // Z轴上的box
        const materialZ = new MeshBasicMaterial({ color: 0x0000ff });
        const boxZ = new Mesh(boxGeometry, materialZ);
        boxZ.position.set(0, 0, 1000);
        scene.add(boxZ);
    }

    public stopRender(): void {
        this._isRendering = false;
        this._renderer.setAnimationLoop(null);
    }

    private onUpdate() {
        if (!this._scene) return;

        if (!this._isRendering) {
            return;
        }

        if (!this._parentDiv) {
            return;
        }

        if (this._parentDiv.clientWidth !== this._viewWidth || this._parentDiv.clientHeight !== this._viewHeight) {
            this.setSize(this._parentDiv.clientWidth, this._parentDiv.clientHeight);
        }

        if (this._control) {
            this._control.update();
        }

        if (this._outlinePostProcess.isEnabled) {
            this._outlinePostProcess.render();
        }
        else {
            this._renderer.render(this._scene, this.camera);
        }
    }

    public async updateScene3D(force: boolean = false): Promise<void> {
        if (force) {
            this._o3dMgr.clearObject3D();
            this.clearScene3D();
        }

        return this._o3dMgr.updateObject3D();
    }

    public get scene(): Scene {
        return this._scene;
    }
}

(globalThis as any).SceneManager = SceneManager;