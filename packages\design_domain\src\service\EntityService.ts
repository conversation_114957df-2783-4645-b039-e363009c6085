import { applySnapshot, getSnapshot, Instance } from "mobx-state-tree";

import { EntityBase, IEntityBase } from "../entity/EntityBase";
import { EntityType } from "../entity/EntityType";
import { IStoreScheme } from "../store/StoreScheme";
import { EntityStore, IEntityStore, IEntityStoreSnapshotIn, IEntityStoreSnapshotOut } from "./EntityStore";
import { IEntityServiceObserver } from "./IEntityServiceObserver";


/**
* @description 实体服务，用于:
* 1 操作实体数据存储仓库
* 2 通知观察者实体发生变化
* <AUTHOR>
* @date 2025-06-16
* @lastEditTime 2025-06-16 11:04:10
* @lastEditors xuld
*/
export class EntityService {

    private _entityStore: IEntityStore = EntityStore.create({});
    private _services: IEntityServiceObserver[] = [];

    /**
     * @description 添加实体
     * @param entity 要添加的实体
     * @returns 是否添加成功
     */
    public async addEntity(entity: Instance<typeof EntityBase>): Promise<boolean> {
        let result = this._entityStore.addEntity(entity);
        if (result) {
            this._services.forEach(async service => await service.onEntityAdded(entity.uuid));
        }
        return result;
    }

    /**
     * @description 删除实体
     * @param uuid 要删除的实体ID
     * @returns 是否删除成功
     */
    public async removeEntity(uuid: string): Promise<boolean> {
        if (!uuid) return false;

        let entity = this.getEntity(uuid);
        if (entity) {
            this._services.forEach(async service => await service.beforeEntityRemoved(uuid));
        }

        let result = this._entityStore.removeEntity(uuid);

        if (result && entity) {
            this._services.forEach(async service => await service.afterEntityRemoved(uuid));
        }

        return result;
    }

    public getScheme(): IStoreScheme {
        return this._entityStore.scheme;
    }

    /**
     * @description 获取所有实体
     * @returns 所有实体
     */
    public getAllEntities(): IEntityBase[] {
        return this._entityStore.getAllEntities();
    }

    /**
     * @description 通过 uuid 获取实体
     * @param uuid 实体 uuid
     * @returns 实体对象,如果不存在返回undefined
     */
    public getEntity(uuid: string): IEntityBase | undefined {
        if (!uuid) return undefined;
        return this._entityStore.getEntity(uuid);
    }

    /**
     * @description 获取指定类型的实体
     * @param type 实体类型
     * @returns 实体对象数组
     */
    public getEntitiesByType(type: EntityType): IEntityBase[] {
        return this._entityStore.getEntitiesByType(type);
    }

    /**
     * @description 清空所有实体
     */
    public async clearEntity(): Promise<void> {
        this._entityStore.clear();
        this._services.forEach(async service => await service.onClearEntity());
    }

    /**
     * @description 清空指定类型的实体
     * @param type 实体类型
     */
    public clearEntityByType(type: EntityType): void {
        this._entityStore.clearEntityByType(type);
    }

    /**
     * @description 添加观察者
     * @param observer 观察者
     */
    public addObserver(observer: IEntityServiceObserver): boolean {
        if (this._services.includes(observer)) {
            return false;
        }

        this._services.push(observer);
        this._services.sort((a, b) => a.getPriority() - b.getPriority());
        return true;
    }

    /**
     * @description 删除观察者
     * @param observer 观察者
     */
    public removeObserver(observer: IEntityServiceObserver): void {
        this._services = this._services.filter(service => service !== observer);
    }

    /**
     * @description 获取实体仓库快照
     * @returns 实体仓库快照
     */
    public getStoreSnapshot(): IEntityStoreSnapshotOut {
        return getSnapshot(this._entityStore);
    }

    /**
     * @description 应用实体仓库快照
     * @param snapshot 实体仓库快照
     */
    public applyStoreSnapshot(snapshot: IEntityStoreSnapshotIn): void {
        // 清空
        this.clearEntity();

        // 应用快照
        applySnapshot(this._entityStore, snapshot);
    }
}

