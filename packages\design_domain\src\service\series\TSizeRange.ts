import { Box3, Vector3, Vector3Like } from "three";
import { Vec3toMeta } from "../../Utils/basic_utils";


/**
 *   统一尺寸定义 ---> length 长; width/depth:宽/深;  height: 高
 */
export class TSize implements Vector3Like {
    x: number;
    y: number;
    z: number;
    constructor(x?: number, y?: number, z?: number) {
        this.x = x || 0;
        this.y = y || 0;
        this.z = z || 0;
    }

    static fromVector3(v: Vector3Like) {
        return new TSize(v.x, v.y, v.z);
    }

    get length() {
        return this.x;
    }

    get width() {
        return this.y;
    }

    get depth() {
        return this.width;
    }

    get height() {
        return this.z;
    }

    set length(x: number) {
        this.x = x;
    }
    set width(y: number) {
        this.y = y;
    }

    set depth(y: number) {
        this.width = y;
    }
    set height(z: number) {
        this.z = z;
    }

}

export interface I_SizeRange {
    min?: Vector3Like;
    max?: Vector3Like;
}
/**
 *  图元尺寸范围
 */
export class TSizeRange extends Box3 {
    constructor(min?: Vector3Like, max?: Vector3Like) {
        super(min as Vector3, max as Vector3);
    }

    toJson() {
        return { min: Vec3toMeta(this.min), max: Vec3toMeta(this.max) };
    }
    fromJson(data: I_SizeRange) {
        this.min.set(
            data.min?.x === undefined ? -1000000 : data.min.x,
            data.min?.y === undefined ? -1000000 : data.min.y,
            data.min?.z === undefined ? -1000000 : data.min.z);

        this.max.set(
            data.max?.x === undefined ? 1000000 : data.max.x,
            data.max?.y === undefined ? 1000000 : data.max.y,
            data.max?.z === undefined ? 1000000 : data.max.z);
        return this;
    }
    setFromBox3(box3: Box3) {
        return this.copy(box3);
    }

    get minLength() {
        return this.min.x;
    }

    get maxLength() {
        return this.max.x;
    }

    get minWidth() {
        return this.min.y;
    }

    get maxWidth() {
        return this.max.y;
    }

    get minHeight() {
        return this.min.z;
    }
    get maxHeight() {
        return this.max.z;
    }



}