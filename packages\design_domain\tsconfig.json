{
    "compilerOptions": {
        "target": "ES2020",
        "module": "ESNext",
        "moduleResolution": "node",
        "declaration": true,
        "declarationMap": true,
        "sourceMap": true,
        "esModuleInterop": true,
        "allowJs": true,
        "forceConsistentCasingInFileNames": false,
        "strict": true,
        "noImplicitReturns": true,
        "noUnusedLocals": false,
        "skipLibCheck": true,
        "resolveJsonModule": true,
        "outDir": "./dist",
        "rootDir": "./"
    },
    "include": [
        "src/**/*",
    ],
    "exclude": [
        "node_modules",
        "dist",
        "test"
    ]
}