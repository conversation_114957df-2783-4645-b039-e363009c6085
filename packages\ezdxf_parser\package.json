{"name": "@layoutai/ezdxf_parser", "version": "1.0.0", "type": "module", "description": "", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "buildIndex": "cti entrypoint ./src -b -o index.ts"}, "devDependencies": {"create-ts-index": "^1.14.0", "tsx": "^4.19.2", "typescript": "^5.6.3", "three": "^0.171.0", "@types/three": "^0.171.0"}, "peerDependencies": {}, "dependencies": {"z_polygon": "workspace:^", "@layoutai/basic_data": "workspace:^", "@layoutai/layout_scheme": "workspace:^"}, "keywords": [], "author": "sunvega", "license": "ISC"}