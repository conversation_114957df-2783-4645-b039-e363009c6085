import { Vector3, Vector3<PERSON><PERSON> } from "three";
import { CadEntityType, I_CadEntity, I_EzdxfAttribs, I_EzdxfEntity, I_EzdxfLayout, Vec3FromArray,ZEdge, ZPolygon, ZPolyline } from "z_polygon";



export interface I_EzdxfJsonData {
    modelspace: I_EzdxfLayout;
    blocks?: { [key: string]: I_EzdxfLayout };
    layers?: { [key: string]: I_EzdxfAttribs };

    view_center?: Vector3Like;
    view_radius?: number;

    complete?: boolean;

    layer_name_dict?: { [key: string]: string };

}

export const StandardLayerNames = [
    "墙", "单开门", "推拉门", "一字窗", "烟道", "包管", "柱子", "横梁", "地台", "软装家具", "定制家具", "其它"
]


export function ezdxfEntity_to_CadEntity(data: I_EzdxfEntity) {
    let cad_entity: I_CadEntity = {};

    cad_entity.layer = data.attribs.layer;
    cad_entity.color = data.attribs?.color || 0;
    cad_entity.options = data.attribs;

    if (data.type === "LINE") {
        let start = data.attribs?.start;
        let end = data.attribs?.end;

        let pos0 = Vec3FromArray(start);
        let pos1 = Vec3FromArray(end);
        let edge: ZEdge = new ZEdge({ pos: pos0 }, { pos: pos1 });
        edge.computeNormal();

        cad_entity.type = CadEntityType.Edges;
        cad_entity.edges = [edge];
        cad_entity.layer = data.attribs?.layer || "0";
        cad_entity.color = data.attribs.color;

    }
    else if (data.type == "LWPOLYLINE") {
        let points: Vector3[] = [];

        let lw_points: number[][] = data.lwpoints || null;
        if (!lw_points) return null;

        for (let p of lw_points) {
            points.push(Vec3FromArray(p));
        }
        let polyline = new ZPolyline();

        polyline.initByVertices(points);


        if (points[points.length - 1].distanceTo(points[0]) < 1) {
            let poly = new ZPolygon();
            poly.initByVertices(points);
            polyline = poly;
        }
        polyline.computeZNor();
        polyline.computeBBox();
        cad_entity.type = CadEntityType.LWPoly;
        cad_entity.polyline = polyline;
        cad_entity.edges = polyline.edges;


    }
    else if (data.type == "INSERT") {
        cad_entity.type = CadEntityType.Block;
        cad_entity.block_name = data.attribs.name;
        // cad_entity.
    }
    else if (data.type == "MTEXT") {
        cad_entity.text = data.text || "";
        cad_entity.type = CadEntityType.Text;
    }
    else {
        return null;
    }

    return cad_entity;

}