{"name": "@layoutai/layout_basic_services", "version": "1.0.0", "type": "module", "description": "layoutAI相关的基本服务层", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "buildIndex": "cti entrypoint ./src -b -o index.ts"}, "devDependencies": {"@types/three": "^0.171.0", "create-ts-index": "^1.14.0", "tsx": "^4.19.2", "typescript": "^5.6.3"}, "peerDependencies": {"three": "^0.171.0"}, "dependencies": {"z_polygon": "workspace:^", "fflate": "^0.8.2", "buffer": "^6.0.3", "xmldom-ts": "^0.3.1", "@svg/oss-upload": "^2.0.3", "@layoutai/basic_data": "workspace:^", "@layoutai/basic_request": "workspace:^", "@layoutai/layout_scheme": "workspace:^", "@layoutai/layout_scores": "workspace:^"}, "keywords": [], "author": "sunvega", "license": "ISC"}