import { BasicRequest } from "@layoutai/basic_request";
import { SchemeXmlParseService } from "./SchemeXmlParseService";
import { gatewayUrl } from "@layoutai/basic_data";

/**
 *  户型库相关服务
 */
const api_url = "/dev-plugin-web/api/buildingroom/v1/getBuildingRoomById";
export interface I_BuildRecord
{
    "id": string,
    "modelName": string,
    "buildingName": string,
    "area": number,
    "roomTypeName": string,
    "roomType": string,
    "imagePath": string,
    "province": string,
    "city": string,
    "district": string,
    "provinceName": string,
    "cityName": string,
    "districtName": string,
    "roomId": string,
    "schemeId": string,
    "orientationsType": number,
    "groundingTime": string,
    "openRoomId": string,
    "address": string,
}
export class BuildingService 
{
    static async ipParse()
    {
        let res =await BasicRequest.magiccubeRequest({
            method: 'get',
            url: `/swj-map-web/district/ipParse`,
            timeout: 5000
        }).catch(()=>null as void) as any;
       
        console.log(res);
        return res?.data || null;
    }
    static async getBuildingList(keyword:string, city:string="440100")
    {
        let query_data = {
            city : city,
            keyWord : keyword,
            current : 1,
            size : 5
        }
        let res =await BasicRequest.magiccubeRequest({
            method: 'post',
            url: `/dev-plugin-web/api/building/v1/getBuildingList`,
            timeout: 5000,
            data : {...query_data}
        }).catch(()=>null as void) as any;
       
        // if(!res?.data?.schemeId) return null;
        return res?.data || [];
    }

    static async search(keyword:string,city:string="",size:number=10,current=1)
    {
               
        let postReqBody : any = {
            accurate:false,
            city: city,
            collection: true,
            current: current,
            keyWord: keyword,
            maxArea: 100000,
            minArea: 0,
            province: "",
            roomTypeList: [],
            size: size
        }
        try {
            const res = await BasicRequest.magiccubeRequest({
                method: 'post',
                url: `/dev-plugin-web/api/buildingroom/v1/search`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
                });
                let result = res?.data || null;
            return result as any;
        } catch (error) {
            return null;
        }
    }
    static async getBuildingRoomById(id:string)
    {
        let res =await BasicRequest.magiccubeRequest({
            method: 'get',
            url: `${gatewayUrl}/dev-plugin-web/api/buildingroom/v1/getBuildingRoomById?id=${id}`,
            timeout: 5000
        }).catch(()=>null as void) as any;
       
        if(!res?.data?.schemeId) return null;
        return res.data;
    }

    static async getOpenBuildingRoomContentById(id:string)
    {
        let res : any =await BasicRequest.magiccubeRequest({
            method: 'post',
            url: `${gatewayUrl}/api/v1/buildingroom/getRoomContentById`,
            data: {
                "buildingRoomId": id

            },
            timeout: 5000
        }).catch(()=>null as void);
       
        return res?.data;
    }

    static async getBuildingRoomSchemeById(id:string,authCode:string,use_server_parser:boolean = true)
    {
        let data = await BuildingService.getBuildingRoomById(id);
        if(!data) return null;

        let schemeId = data.schemeId;
        if(use_server_parser)
        {
            let res = await SchemeXmlParseService.getSchemeXmlBySchemeId(schemeId,authCode);
            return res;
        }
        else{
            let res = SchemeXmlParseService.getSchemeXmlBySchemeId_WithLocalParser(schemeId,authCode);
            return res;
        }



    }
}