import { BasicRequest } from "@layoutai/basic_request";

export class ConfigService
{
    static uploadRulesJson(rulesJson:string, versionTip:string,onSuccess:()=>void, onFail:()=>void) : void {
        let postReqBody = {
            cfgContent: rulesJson,
            desc: versionTip,
            enable: 0
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/save`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response:any) => {
                if (!response || !response.success) {
                    console.error("Fail to upload rules json to server.");
                    onFail();
                } else {
                    onSuccess();
                }
            }).catch((e:any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static downloadLayoutRule(ruleVersionId:number, onSuccess:(json:any)=>void, onFail:()=>void) : void {
        let postReqBody = {
            id: ruleVersionId
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/get`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response:any) => {
                if (response && response.success && response.result && response.result.configUrl) {
                    fetch(response.result.configUrl)
                    .then((configResponse:any) => {
                        if (configResponse) {
                            configResponse.json().then((json:any) => {
                                onSuccess(json);
                            }).catch((e:any) => {
                                console.error(e);
                                onFail();
                            });
                        }
                    })
                    .catch((e:any) => {
                        console.error(e);
                        onFail();
                    });
                } else {
                    console.error("Fail to get rules json from server.");
                    onFail();
                }
            })
            .catch((e:any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static peekRulesJson(onSuccess:(json:any[])=>void, onFail:()=>void) : void {
        let postReqBody = {
            isDelete: 0,
            orderBy: "create_date desc",
            "pageIndex": 1,
            "pageSize": 100
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/listByPage`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response:any) => {
                if (response && response.success && response.result.result) {
                    onSuccess(response.result.result);
                } else {
                    console.error("Fail to get rules json from server.");
                    onFail();
                }
            })
            .catch((e:any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static getCurrentVersionLayoutRule(onSuccess:(json:any[])=>void, onFail:()=>void) : void {
        let postReqBody = {
            isDelete: 0,
            enable: 1,
            orderBy: "create_date desc",
            "pageIndex": 1,
            "pageSize": 100
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/listByPage`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response:any) => {
                if (response && response.success && response.result.result) {
                    onSuccess(response.result.result);
                } else {
                    console.error("Fail to get rules json from server.");
                    onFail();
                }
            })
            .catch((e:any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static applyRuleVersion(ruleVersion:number, onSuccess:()=>void, onFail:()=>void) : void {
        let postReqBody = {
            id: ruleVersion
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/enableConfig`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response:any) => {
                if (!response || !response.success) {
                    console.error("Fail to apply rule version.");
                    onFail();
                } else {
                    onSuccess();
                }
            }).catch((e:any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static deleteRuleVersion(ruleVersion:number, onSuccess:()=>void, onFail:()=>void) : void {
        let postReqBody = {
            id: ruleVersion,
            enable: 0,
            isDelete: 1
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/edit`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response:any) => {
                if (!response || !response.success) {
                    console.error("Fail to apply rule version.");
                    onFail();
                } else {
                    onSuccess();
                }
            }).catch((e:any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }
}