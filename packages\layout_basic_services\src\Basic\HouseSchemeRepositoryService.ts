import { I_SwjXmlScheme } from "@layoutai/basic_data";
import { BasicRequest } from "@layoutai/basic_request";
import { I_TLayoutScheme, TRoom } from "@layoutai/layout_scheme";


export interface I_HouseSchemeInfo {
    /**
 *  户型ID
 */
    buildingRoomId?: string;

    buildingName?: string;

    buildingRoomTypeName?: string;

    area?: string;
    /**
     *  方案ID
     */
    schemeId?: string;

    /**
     *  方案名称
     */
    schemeName?: string;

    cityName ?: string;
}

/**
 *   空间方案的测试信息
 */
export interface I_HouseSchemeTestingInfo extends I_HouseSchemeInfo{
    /**
     *  户型ID
     */
    buildingRoomId?: string;

    buildingName?: string;

    buildingRoomTypeName?: string;

    area?: string;
    /**
     *  方案ID
     */
    schemeId?: string;

    /**
     *  方案名称
     */
    schemeName?: string;
    /**
     *  方案json
     */
    schemeXmlJson?: I_SwjXmlScheme;


    /**
     *  单空间测试信息列表
     */
    room_testing_info_list?: I_RoomTestingInfo[];



    scheme_label?: "default" | "room_error" | "layout_error";
}

/**
 *  单空间测试结果
 */
export interface I_RoomTestingInfo {

    room?: TRoom;


    roomname?: string;

    schemeId?: string;

    uid?: string;


    /**
     *  是否求解成功: 失败、迁移、图灵
     */
    solved?: "None" | "Transfer" | "Self-Transfer" | "Turing" | "Logic";
    /**
     *  所有备选解数量
     */
    candidate_num?: number;

    /**
     *  有效备选解数量---通过评分器筛选的备选解数量
     */
    valid_candidate_num?: number;

    layoutSchemeList?: I_TLayoutScheme[];

    /**
     *  特征码
     */
    codeA?: string;

    /**
     *  最优解相似的scheme_room_id
     */
    transfer_from_scheme_room_id?: string;
    /**
     *  
     */
    transfer_from_self?: boolean;



}

export class HouseSchemeData {
    public index: number;
    private address: string;
    public area: number;
    private buildingId: number;
    public buildingName: string;
    private city: string;
    private createDate: string;
    private createUser: string;
    private district: string;
    private grounding: number;
    private houseType: string;
    private innerWallJsonUrl: string;
    private isAiAdjust: number;
    private isDelete: number;
    private layoutId: string;
    public layoutImage: string;
    public layoutName: string;
    private province: string;
    public schemeId: string;
    private updateDate: string;
    private updateUser: string;
    public xmlUrl: string;
    public passRooms: TRoom[];
    public failRooms: TRoom[];

    constructor() {
        this.passRooms = [];
        this.failRooms = [];
    }

    toString(): string {
        return this.schemeId + ": " + this.buildingName + ", " + this.layoutName + ", " + this.area + "㎡";
    }

    public equalsWith(another: HouseSchemeData): boolean {
        if (this.schemeId != another.schemeId
            || this.xmlUrl != another.xmlUrl
            || this.buildingId != another.buildingId
            || this.area != another.area) {
            return false;
        }
        return true;
    }
}

export class HouseSchemeRepositoryService {
    private latestSearchAfter: any;
    private latestPageIndex: number;
    public totalSize: number;
    public pageSize: number;

    constructor(totalSizeValue: number) {
        this.latestSearchAfter = null;
        this.latestPageIndex = 0;
        this.totalSize = totalSizeValue;
        this.pageSize = 25;
    }

    reset() {
        this.latestPageIndex = 0;
        this.latestSearchAfter = null;
    }

    async fetchHouseSchemeList(pageIndexValue?: number): Promise<HouseSchemeData[]> {
        let postReqBody = {
            searchAfter: this.latestSearchAfter,
            pageIndex: pageIndexValue ? pageIndexValue : this.latestPageIndex + 1,
            pageSize: this.pageSize
        }

        let responseResult: any = null;

        try {
            const res:any = await BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/vrlayout/getLayoutInfo`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            });
            responseResult = res?.result?.result;
            this.latestSearchAfter = res?.result?.searchAfter;
        } catch (error) {
            console.error(error);
            return null;
        }
        if (!responseResult) {
            console.error("Fail to get house info.");
            return null;
        }

        let houseSchemeDataArray: HouseSchemeData[] = [];
        for (let i = 0; i < responseResult.length; i++) {
            let newHouseSchemeData = new HouseSchemeData();
            Object.assign(newHouseSchemeData, responseResult[i]);
            newHouseSchemeData.index = this.latestPageIndex * this.pageSize + i + 1;
            houseSchemeDataArray.push(newHouseSchemeData);
        }
        this.latestPageIndex += 1;
        return houseSchemeDataArray;
    }

    public static async getHouseSchemeDataBySchemeId(schemeId: string): Promise<HouseSchemeData> {
        let postReqBody = {
            schemeId: schemeId
        }

        let responseResult: any = null;

        try {
            const res : any = await  BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/vrlayout/getSingleLayout`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            });
            responseResult = res?.result;
        } catch (error) {
            console.error(error);
            return null;
        }
        if (responseResult) {
            let newHouseSchemeData = new HouseSchemeData();
            Object.assign(newHouseSchemeData, responseResult);
            return newHouseSchemeData;
        } else {
            return null;
        }
    }

    public static async getHouseSchemeDataBySchemeIds(schemeIds: string[]): Promise<HouseSchemeData[]> {
        let houseSchemeDataArray: HouseSchemeData[] = [];
        let allPromises: Promise<HouseSchemeData>[] = [];

        schemeIds.forEach(async schemeId => {
            allPromises.push(this.getHouseSchemeDataBySchemeId(schemeId));
        });
        const results = await Promise.all(allPromises);
        for (let i = 0; i < results.length; i++) {
            if (results[i]) {
                results[i].index = i + 1;
                houseSchemeDataArray.push(results[i]);
            };
        }
        return houseSchemeDataArray;
    }


}
