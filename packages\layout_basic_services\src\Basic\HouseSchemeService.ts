import { BasicRequest } from "@layoutai/basic_request";
import { TLayoutEntityContainer, deflate_to_base64_str,LayoutSchemeJsonSaver} from "@layoutai/layout_scheme";
import { formatCurrentTime } from "z_polygon";
import { uploadFileToOss, uploadImageToOss } from "./basic_utils";



/**
 * @description 户型方案服务
 */
export class HouseSchemeService {

    public static getHouseSchemeUrl = "api/njvr/layoutSchemeHouseType/get";
    public static updateHouseSchemeUrl = "api/njvr/layoutSchemeHouseType/edit";
    public static createHouseSchemeUrl = "api/njvr/layoutSchemeHouseType/insert";

    public static get layoutContainer() {
        return  TLayoutEntityContainer.instance;
    }
    
    /**
     * @description 创建户型方案
     * @param params 
     */
    public static async createHouseScheme(params:IHouseSchemeParams):Promise<boolean> {
        let {fileUrl, imageUrl} = await this.makeHouseSchemeJsonAndImageUrl(params);

        if (!fileUrl || !imageUrl) {
            return false;
        }

        params.dataUrl = fileUrl;
        params.imageUrl = imageUrl;

        const res :any= await BasicRequest.magiccubeRequest({
            method: 'post',
            url: this.createHouseSchemeUrl,
            data: params,
            timeout: 60000,
        })
        if(res?.result) {
            this.layoutContainer.curEditHouseSchemeId = res.result;
        }else{
            this.layoutContainer.curEditHouseSchemeId = '';
        }
        return res?.result;
    }

    /**
     * @description 更新户型方案
     * @param params 
     * @returns 
     */
    public static async updateHouseScheme(params: any): Promise<boolean> {
        let curEditHouseSchemeId = this.layoutContainer.curEditHouseSchemeId
        if(!curEditHouseSchemeId) {
            console.warn("当前没有编辑的户型方案");
            return false;
        }
        let {fileUrl, imageUrl} = await this.makeHouseSchemeJsonAndImageUrl(params);
        
        if (!fileUrl || !imageUrl) {
            return false;
        }
        params.id = curEditHouseSchemeId;
        params.dataUrl = fileUrl;
        params.imageUrl = imageUrl;

        const res :any= await BasicRequest.magiccubeRequest({
            method: 'post',
            url: this.updateHouseSchemeUrl,
            data: params,
            timeout: 60000,
        })
        if(!res?.result) {
            this.layoutContainer.curEditHouseSchemeId = '';
        }
        return res?.result;
    }

    /**
     * @description 获取户型方案数据
     * @param params 
     * @returns 
     */
    public static async getHouseSchemeData(curEditHouseSchemeId: string) {
        const res :any = await  BasicRequest.magiccubeRequest({
            method: 'post',
            url: `api/njvr/layoutSchemeHouseType/get`,
            data: {
                id: curEditHouseSchemeId,
            },
            timeout: 60000,
        })
        return res?.result;
    }
    /**
     * @description 生成户型方案json文件的url
     * @param params 
     * @returns 
     */
    public static async makeHouseSchemeJsonAndImageUrl(params: any) {
        this.layoutContainer.updateRoomsFromEntities();
        this.layoutContainer._current_swj_layout_data = this.layoutContainer.swj_scheme_json_data;
        this.layoutContainer._src_swj_layout_data = this.layoutContainer._current_swj_layout_data;
        let houseSchemeJsonObj = this.layoutContainer.toXmlSchemeData();
        const houseSchemeBase64 = deflate_to_base64_str(JSON.stringify(houseSchemeJsonObj));
        // 生成文件名：houseScheme_时间戳.json
        const timestamp = formatCurrentTime();
        const fileName = `houseScheme_${timestamp}.json`;
        const file = new File([houseSchemeBase64], fileName, { type: 'application/json' }); // 创建 File 对象
        
        // 将base64字符串作为文件内容上传到OSS
        const fileUrl = await uploadFileToOss(file, fileName);

        let imageUrl = await uploadImageToOss(
            LayoutSchemeJsonSaver.saveLayoutSchemeImage(1200, 1200, 0.9),
            "snapShot" + Math.floor(Math.random() * 10000) + ".png"
        );
        return {
            fileUrl,
            imageUrl
        };
    }
}

export interface IHouseSchemeParams {
    area: number, // 面积
    room: string, // 房
    hall: string,  // 厅
    kitchen: string, // 厨
    bathroom: string,  // 卫
    buildingName: string, //小区名称
    city: string, // 市代码
    dataUrl: string, // 户型数据
    district: string, //区代码
    dwgUrl: string, //dwg文件路径
    houseSchemeId: string,  // 户型对应布局方案ID
    houseTypeName: string, // 户型名称
    imageUrl: string, // 户型图url
    platLayoutId: string, // 平台库户型ID
    province: string, // 省代码
    source: string, // 来源1户型库2上传CAD3上传临摹图
    towards: string // 朝向
}


// 放入全局对象，方便调试
(globalThis as any).HouseSchemeService = HouseSchemeService;