import { BasicRequest, _source, mode_type } from "@layoutai/basic_request";
import { LayoutContainerUtils, TLayoutEntityContainer } from "@layoutai/layout_scheme";
import { uploadImageToOss } from "./basic_utils";

export interface CustomerInfo {
    address: string;
    areaCode: string;
    buildingName: string;
    city: string;
    cityCode: string;
    clientId: string;
    cntactMan: string;
    district: string;
    email: string;
    mobile: string;
    note: string;
    province: string;
    provinceCode: string;
    qq: string;
    sex: string;
    webSiteSource: string;
}
export interface HouseTypeParam {
    area: string,
    bathroom: string,
    buildingName: string,
    city: string,
    dataUrl: string,
    district: string,
    hall: string,
    houseSchemeId: string,
    houseTypeName: string,
    imageUrl: string,
    kitchen: string,
    platLayoutId: string,
    province: string,
    room: string,
    source: string,
    towards: string
    dwgUrl: string
}



export interface LayoutSchemeData {
    area: number,
    contentUrl: string,
    coverImage: string,
    createDate: string,
    createUser: string,
    id: string,
    isDelete: number,
    layoutSchemeName: string,
    svjSchemeId: string,
    tenantId: string,
    updateDate: string,
    updateUser: string
    dreamerScheme: string
    // 项目ID(多方案的originSchemeId) 
    projectId?: string
}

export interface LayoutSchemeList {
    layoutSchemeDataList: LayoutSchemeData[],
    total: number
}

export class LayoutSchemeService {
    private static saveLayoutSchemeApiPath: string = "/api/njvr/layoutScheme/save";
    private static listLayoutSchemeApiPath: string = "/api/njvr/layoutScheme/listByPage";
    private static deleteLayoutSchemeApiPath: string = "/api/njvr/layoutScheme/delete";
    private static getLayoutSchemeApiPath: string = "/api/njvr/layoutScheme/get";

    private static layout2DreamerSchemeMap: Map<string, string> = new Map<string, string>();

    public static setDreamerScheme(layoutSchemeId: string, dreamerSchemeId: string) {
        LayoutSchemeService.layout2DreamerSchemeMap.set(layoutSchemeId, dreamerSchemeId);
    }

    public static getDreamerScheme(layoutSchemeId: string): string {
        return LayoutSchemeService.layout2DreamerSchemeMap.get(layoutSchemeId);
    }

    public static removeDreamerScheme(layoutSchemeId: string) {
        LayoutSchemeService.layout2DreamerSchemeMap.delete(layoutSchemeId);
    }

    public static async saveLayoutScheme(
        id: string, 
        layoutSchemeName: string, 
        layoutSchemeJson: string, 
        layoutSchemeImage: string, 
        roomLayoutInfoList: any[], 
        svjSchemeId: string, 
        ruleId: string,
        area: number, 
        customerInfo: CustomerInfo,
        houseTypeParam: HouseTypeParam,
        scoreData: string,
        designerId: string,
        designerOpenId: string
    ): Promise<string> {
        let requestBodyData = {
            area: area,
            content: layoutSchemeJson,
            coverImage: layoutSchemeImage,
            id: id,
            ruleId: ruleId,
            layoutSchemeName: layoutSchemeName,
            svjSchemeId: svjSchemeId,
            roomList: roomLayoutInfoList,
            houseTypeParam: houseTypeParam,
            scoreData: scoreData,
            designerId: designerId,
            designerOpenId: designerOpenId,
            source: _source   // 来源
        }
        if (customerInfo != null) {
            (requestBodyData as any)["customerInfo"] = customerInfo;
        }

        const response = await BasicRequest.openApiRequest({
            method: 'post',
            url: `${LayoutSchemeService.saveLayoutSchemeApiPath}`,
            data: requestBodyData,
            timeout: 60000,
        }).catch((e: any) => {
            console.error(e);
        }) as any;

        let layoutSchemeId: string = null;

        if (response && response.success) {
            layoutSchemeId = response.result;
        }

        return layoutSchemeId;
    }

    public static async listLayoutScheme(): Promise<LayoutSchemeList> {
        let requestBodyData = {
            isDelete: 0,
            pageIndex: 1,
            pageSize: 100
        };

        const response = await BasicRequest.openApiRequest({
            method: 'post',
            url: `${LayoutSchemeService.listLayoutSchemeApiPath}`,
            data: requestBodyData,
            timeout: 60000,
        }).catch((e: any) => {
            console.error(e);
            return null;
        }) as any;

        let layoutSchemeDataList = null;

        if (response && response.success) {
            layoutSchemeDataList = [];
            for (let element of response.result.result) {
                let layoutSchemeData: LayoutSchemeData = {
                    area: element.area,
                    contentUrl: element.contentUrl,
                    coverImage: element.coverImage,
                    createDate: element.createDate,
                    createUser: element.createUser,
                    id: element.id,
                    isDelete: element.isDelete,
                    layoutSchemeName: element.layoutSchemeName,
                    svjSchemeId: element.svjSchemeId,
                    tenantId: element.tenantId,
                    updateDate: element.updateDate,
                    updateUser: element.updateUser,
                    dreamerScheme: LayoutSchemeService.getDreamerScheme(element.id),
                    projectId: element?.projectId
                };
                layoutSchemeDataList.push(layoutSchemeData);
            };
        }

        return {layoutSchemeDataList: layoutSchemeDataList, total: response.result.total};
    }

    public static async deleteLayoutScheme(layoutSchemeId: string): Promise<boolean> {
        let requestBodyData = {
            ids: [layoutSchemeId]
        };

        const response = await BasicRequest.openApiRequest({
            method: 'post',
            url: `${LayoutSchemeService.deleteLayoutSchemeApiPath}`,
            data: requestBodyData,
            timeout: 60000,
        }).catch((e: any) => {
            console.error(e);
        }) as any;

        if (response && response.success) {
            return true;
        }
        return false;
    }

    public static async getLayoutSchemeById(layoutSchemeId: string): Promise<LayoutSchemeData> {
        let requestBodyData = {
            id: layoutSchemeId
        };

        const response = await BasicRequest.openApiRequest({
            method: 'post',
            url: `${LayoutSchemeService.getLayoutSchemeApiPath}`,
            data: requestBodyData,
            timeout: 60000,
        }).catch((e: any) => {
            console.error(e);
        }) as any;

        let layoutSchemeData: LayoutSchemeData = null;
        if (response && response.success && response.result != null) {
            let data = response.result;
            layoutSchemeData = {
                area: data.area,
                contentUrl: data.contentUrl,
                coverImage: data.coverImage,
                createDate: data.createDate,
                createUser: data.createUser,
                id: data.id,
                isDelete: data.isDelete,
                layoutSchemeName: data.layoutSchemeName,
                svjSchemeId: data.svjSchemeId,
                tenantId: data.tenantId,
                updateDate: data.updateDate,
                updateUser: data.updateUser,
                projectId: data?.projectId,
                dreamerScheme: LayoutSchemeService.getDreamerScheme(data.id)
            };
        }
        return layoutSchemeData;
    }

    public static async getLayoutSchemeList(params: any): Promise<LayoutSchemeList> {
        let response = await BasicRequest.openApiRequest({
            method: 'post',
            url: `${LayoutSchemeService.listLayoutSchemeApiPath}`,
            data: {
                ...params,
            },
            timeout: 60000,
        }).catch((e: any) => {
        }) as any; 
        let layoutSchemeDataList: LayoutSchemeData[] = null;
        if (response && response.success && response.result.result) {
            layoutSchemeDataList = [];
            for (let element of response.result.result) {
                let layoutSchemeData: LayoutSchemeData = {
                    area: element.area,
                    contentUrl: element.contentUrl,
                    coverImage: element.coverImage,
                    createDate: element.createDate,
                    createUser: element.createUser,
                    id: element.id,
                    isDelete: element.isDelete,
                    layoutSchemeName: element.layoutSchemeName,
                    svjSchemeId: element.svjSchemeId,
                    tenantId: element.tenantId,
                    updateDate: element.updateDate,
                    updateUser: element.updateUser,
                    dreamerScheme: LayoutSchemeService.getDreamerScheme(element.id),
                    projectId: element?.projectId
                };
                layoutSchemeDataList.push(layoutSchemeData);
            };
        }

        return {layoutSchemeDataList: layoutSchemeDataList, total: response.result?response.result.recordCount:0};
    }



}