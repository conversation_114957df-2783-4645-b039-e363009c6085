import { BasicRequest } from "@layoutai/basic_request";
import { CadDrawingLayerType, LayoutAI_App, <PERSON><PERSON>, TAppManagerBase, TLayoutEntityContainer, TRoom, TRoomLayoutScheme } from "@layoutai/layout_scheme";
import { GenDateUUid, saveBufferAs } from "z_polygon";
import { DrawingFigureMode } from "@layoutai/basic_data";
import { uploadImageToOss } from "./basic_utils";
import { LayoutSchemeJsonSaver } from "@layoutai/layout_scheme";
/**
 * @description 创建布局方案附件
 */
export async function createLayoutSchemeAttachment(params: any) {
    const res : any = await BasicRequest.openApiRequest({
      method: 'post',
      url: `/api/njvr/layoutSchemeFiles/insert`,
      data: {
          ...params,
        }
    }).catch((e:any)=>{
      console.error(e);
    });
    if (res?.success && res.result) {
      return res.result;
    } else {
      return null;
    }
  }
  
export interface I_TuringLayoutItem {
    name: string;
    type: string;
    pos_x: number;
    pos_y: number;
    pos_z: number;
    length: number;
    width: number;
    height: number;
    rotate_z: number;
    left_depth: number;
    right_depth: number;
    fixed: boolean;
    min_length:number;
    max_length:number;
    min_width:number;
    max_width:number;
    min_height:number;
    max_height:number;
    shape:string;
    level:number;
    style_id:string;
    organ_id: string;
    is_big_groove: boolean;
    annotation: number;
    model_id:string;
    material_id:string;
    appendix_material_id:string;
    apply_space:string[];
    is_parallel_with_bed: boolean;
    same_wall_with_door: boolean;
    need_clone: number;
    need_replace: number;
    layout_rule: string;
}

export class LayoutService {
    private static debug:boolean = true;

    public static async generateRoomLayout(room:TRoom, xml_base64_str:string, logger:Logger, forceAll:boolean = false): Promise<TRoomLayoutScheme[]> {
        let apiUrl = null;

        let roomType = TRoom.getRoomTypeByName(room.roomname);
        let layoutSchemeList: TRoomLayoutScheme[] = [];

        if (roomType == "客餐厅") {
            apiUrl = `/dp-ai-web/layoutLivingRoom`;
        } else if (roomType == "卧室") {
            apiUrl = `/dp-ai-web/layoutBedRoom`;
            if(!forceAll) return null;
        } else if (roomType == "卫生间") {
            apiUrl = `/dp-ai-web/layoutBathRoom`;
        } else if (roomType == "书房") {
            apiUrl = `/dp-ai-web/layoutStudyRoom`;
            return null;
        } else if (roomType == "阳台") {
            apiUrl = `/dp-ai-web/layoutBalcony`;
        } else if (roomType == "厨房") {
            apiUrl = `/dp-ai-web/layoutKitchen`;
        } else {
            return null;
        }

        if (!apiUrl) {
            return null;
        }

        try {
            let postReqBody = {
                roomUid: room.uid,
                roomName: room.roomname,
                styleId: 362467,
                styleName: "时序系列",
                seedSchemeId: null as null,
                xmlBase64: xml_base64_str,
                sysCode: 28005,
                traceId: logger.traceId,
                organizationId: "C10287625",
                roomFurnitureList: {}
            }
            let apiPath = apiUrl.substring(apiUrl.lastIndexOf("/") + 1);
            if (LayoutService.debug) {
                logger.time(room.name + room.uid + "_" + apiPath);
                logger.log(">>>>>> 开始计算图灵空间布局, call Turing Backend API " + apiPath + " for: " + room);
            }
            const postResponseBody = await BasicRequest.magiccubeDpAiWebRequest({
            method: 'post',
            url: apiUrl,
            data: {
                ...postReqBody,
            },
            timeout: 300 * 1000,
            }) as any;
            if (LayoutService.debug) logger.timeEnd(room.name + room.uid + "_" + apiPath);
            if (postResponseBody.success == false || !postResponseBody.data || postResponseBody.data.success == false) {
                console.error("Fail to generate turing layout for: " + room.toString());
                return null;
            }

            let layoutResult:any = postResponseBody.data.data;
            if (!layoutResult) layoutResult = postResponseBody.data.result;
            if (layoutResult) {
                // console.log(room.uid + ":" + roomType + " ###########################");
               for (const roomUid in layoutResult) {
                    const layouts = layoutResult[roomUid];
                    if (!layouts || layouts.length == 0) {
                        console.error("Fail to generate turing layout for: " + room.toString());
                    } else {
                        let layoutScheme = new TRoomLayoutScheme();
                        layoutSchemeList.push(layoutScheme);
                        if (roomUid == room.uid) {
                            layoutScheme.room = room;
                        } else {
                            layoutScheme.room = new TRoom();
                            layoutScheme.room.uid = roomUid;
                        }
                        try {
                            for (let layoutIndex = 0; layoutIndex < layouts.length; layoutIndex ++) {
                                let layoutItem = layouts[layoutIndex];
                                if (Array.isArray(layoutItem)) {
                                    let layoutSchemeRef = layoutScheme;
                                    if (layoutIndex > 0) {
                                        let anotherLayoutScheme = new TRoomLayoutScheme();
                                        anotherLayoutScheme.room = layoutScheme.room;
                                        layoutSchemeList.push(anotherLayoutScheme);
                                        layoutSchemeRef = anotherLayoutScheme;
                                    }
                                    for(let layoutItemKey in layoutItem) {
                                        let layoutItemValue = layoutItem[layoutItemKey];
                                        let subTuringLayoutItem: I_TuringLayoutItem = layoutItemValue as I_TuringLayoutItem;
                                        // layoutSchemeRef.addTuringLayoutItem(subTuringLayoutItem);
                                    }
                                } else {
                                    let turingLayoutItem: I_TuringLayoutItem = layoutItem as I_TuringLayoutItem;
                                    // layoutScheme.addTuringLayoutItem(turingLayoutItem);
                                }
                            };
                        } catch(e) {
                            console.error(e);
                        }
                    }
               }
            }
        } catch (error) {
            console.error("Fail to generate layout for：" + room.toString() + "\n" + error.toString());
            return null;
        }

        return layoutSchemeList;
    }



    // 导出dwg
    public static async exportCadData() {

        // let drawing_layers =  (LayoutAI_App.instance as TAppManagerBase)?.drawing_layers;
        // if(!drawing_layers) return;
        // let layer = drawing_layers[CadDrawingLayerType.CadEzdxfDrawing];
        // if(!layer) return;
        // if (layer) {
        //     let json_data = layer.exportEzdxfCadData();

        //     let scheme_id = (LayoutAI_App.instance as TAppManagerBase).layout_container._scheme_id;
        //     if (!scheme_id) {
        //         scheme_id = (LayoutAI_App.instance as TAppManagerBase).layout_container._scheme_name || GenDateUUid();
        //     }
        //     let query_data = {
        //         data: json_data,
        //         filename: (LayoutAI_App.instance as TAppManagerBase).layout_container._scheme_id
        //     }
        //     // 预发和线上才有效
        //     let url = "/oda-dxfjson/api/oda/getdwg";
        //     const response = await BasicRequest.magiccubeRequest({
        //         method: 'post',
        //         url: url,
        //         data: query_data,
        //         timeout: 60000,
        //     }).catch((e: any) => {
        //         console.error(e);
        //     });

        //     if (response && response.data) {
        //         let buffer = Buffer.from(response.data, 'latin1');
        //         saveBufferAs(buffer, query_data.filename + ".dwg");
        //     }
        // }
    }

    // 导出图片
    public static async exportImage(layoutContainer:TLayoutEntityContainer,type:"layoutImage" | "colorScreen" | "outline") {

        let preFigureMode = layoutContainer.drawing_figure_mode;
        if(type == "layoutImage")
        {
            if(layoutContainer._furniture_entities.length < 1)
            {
                return;
            }
            layoutContainer.drawing_figure_mode = DrawingFigureMode.Figure2D;
        }
        else if(type == "outline")
        {
            layoutContainer.drawing_figure_mode = DrawingFigureMode.Outline;
        }
        else{
            layoutContainer.drawing_figure_mode = DrawingFigureMode.Texture;
        }
        let image = LayoutSchemeJsonSaver.saveLayoutSchemeImage(1200, 1200, 0.9);
        // layoutContainer.saveLayoutSchemeImage(1200, 1200, 0.9);
        layoutContainer.drawing_figure_mode = preFigureMode;
        let imageUrl = await uploadImageToOss(image,"snapShot" + Math.floor(Math.random() * 10000) + ".png");

        // 触发下载
        await this._downloadImage(imageUrl);
        
        const res = await createLayoutSchemeAttachment({
            layoutSchemeId: layoutContainer._layout_scheme_id,
            fileType: type,
            fileUrl: imageUrl
        });

    }

    // 下载图片
    private static _downloadImage = async (url: string) => {
        try {
            const response = await fetch(url);
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = `layout_${new Date().getTime()}.png`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(downloadUrl);
            document.body.removeChild(a);
        } catch (error) {
            console.error(LayoutAI_App.t('下载图片失败:'), error);
        }
    };
        
}
