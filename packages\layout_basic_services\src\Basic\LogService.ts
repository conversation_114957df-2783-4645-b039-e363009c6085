import { BasicRequest } from "@layoutai/basic_request";

export class LogService
{
    static async uploadLog(traceId:string, type:string, level:string, context:string, logs:string[]) : Promise<Boolean> {
        let postReqBody = {
            type: type,
            level: level,
            traceId: traceId,
            context: context,
            logs: logs
        }

        try {
            const res = await BasicRequest.magiccubeDpAiWebRequest({
            method: 'post',
            url: `/dp-ai-web/uploadLog`,
            data: {
                ...postReqBody,
            },
            timeout: 30000,
            });
            return res.data;
        } catch (error) {
            console.error(error);
            return false;
        }
    }
}