
import {I_SwjXmlScheme, staticHost, xmlParse2JsonApiUrl} from "@layoutai/basic_data"
import { BasicRequest } from "@layoutai/basic_request";
import { DesignXmlParser } from "@layoutai/layout_scheme";
import { GenDateUUid } from "z_polygon";
import { HouseSchemeRepositoryService } from "./HouseSchemeRepositoryService";
export class SchemeXmlParseService
{
    static async parseSchemeXml2Json(xmlContent:string, traceId:string) : Promise<I_SwjXmlScheme> {
        let postReqBody = {
            traceId: traceId,
            sysCode: "ai-desigin-plugin",
            xmlBase64: xmlContent
        }

        let xmlScheme:I_SwjXmlScheme = null;

        try {
            const res = await  BasicRequest.magiccubeRequest({
            method: 'post',
            url: xmlParse2JsonApiUrl,
            data: {
                ...postReqBody,
            },
            timeout: 30000,
            });
            xmlScheme = res?.data?.result as I_SwjXmlScheme;
        } catch (error) {
            console.error(error);
            return null;
        }

        SchemeXmlParseService.rectifyUids(xmlScheme);

        return xmlScheme;
    }

    static rectifyUids(xmlScheme:I_SwjXmlScheme) {
        if (xmlScheme == null || xmlScheme.room_list == null) return;

        let nextUid:number = 20;

        let roomUidMap:Map<string,string> = new Map();
        let wallUidMap:Map<string,string> = new Map();
        let innerwallUidMap:Map<string,string> = new Map();
        let doorUidMap:Map<string,string> = new Map();
        let windowUidMap:Map<string,string> = new Map();

        xmlScheme.room_list.forEach(room => {
            const oldUid = room.ind;
            const newUid:number = ++nextUid;
            room.ind = newUid;
            room.room_id = newUid;
            room.uid = newUid;
            if (oldUid != null) roomUidMap.set(oldUid.toString(), newUid.toString());
        });

        xmlScheme.room_list.forEach(room => {
            room.wall_list.forEach(wall => {
                const oldUid = wall.uid;
                let newUid : number;
                const strNewUid = wallUidMap.get(oldUid.toString());
                if (strNewUid != null) newUid = Number(strNewUid);
                else newUid = ++nextUid;
                wall.uid = newUid;
                if (oldUid != null && strNewUid == null) wallUidMap.set(oldUid.toString(), newUid.toString());
            });
        });
        
        xmlScheme.room_list.forEach(room => {
            room.inner_wall_list.forEach(innerwall => {
                const oldUid = innerwall.uid;
                let newUid : number;
                const strNewUid = innerwallUidMap.get(oldUid.toString());
                if (strNewUid != null) newUid = Number(strNewUid);
                else newUid = ++nextUid;
                innerwall.uid = newUid;
                if (oldUid != null && strNewUid == null) innerwallUidMap.set(oldUid.toString(), newUid.toString());

                if ((innerwall as any)["room_ind"] != null) {
                    const newRoomInd = roomUidMap.get((innerwall as any)["room_ind"].toString());
                    if (newRoomInd != null) {
                        (innerwall as any)["room_ind"] = Number(newRoomInd);
                    }
                }


            });
        });

        xmlScheme.room_list.forEach(room => {
            room.door_list.forEach(door => {
                const oldUid = door.uid;
                let newUid : number;
                const strNewUid = doorUidMap.get(oldUid.toString());
                if (strNewUid != null) newUid = Number(strNewUid);
                else newUid = ++nextUid;
                door.uid = newUid;
                if (oldUid != null && strNewUid == null) doorUidMap.set(oldUid.toString(), newUid.toString());

                if ((door as any)["wall_ind"] != null) {
                    const newWallInd = wallUidMap.get((door as any)["wall_ind"].toString());
                    if (newWallInd != null) {
                        (door as any)["wall_ind"] = Number(newWallInd);
                    }
                }
            });
        });

        xmlScheme.room_list.forEach(room => {
            room.window_list.forEach(window => {
                const oldUid = window.uid;
                let newUid : number;
                const strNewUid = windowUidMap.get(oldUid.toString());
                if (strNewUid != null) newUid = Number(strNewUid);
                else newUid = ++nextUid;
                window.uid = newUid;
                if (oldUid != null && strNewUid == null) windowUidMap.set(oldUid.toString(), newUid.toString());

                if ((window as any)["wall_ind"] != null) {
                    const newWallInd = wallUidMap.get((window as any)["wall_ind"].toString());
                    if (newWallInd != null) {
                        (window as any)["wall_ind"] = Number(newWallInd);
                    }
                }
            });
        });

        
        xmlScheme.room_list.forEach(room => {
            room.inner_wall_list.forEach(innerwall => {
                innerwall.door_list.forEach((door :any)=> {
                    const oldUid = door.uid;
                    let newUid : number;
                    const strNewUid = doorUidMap.get(oldUid.toString());
                    if (strNewUid != null) newUid = Number(strNewUid);
                    else newUid = ++nextUid;
                    door.uid = newUid;
                    if (oldUid != null && strNewUid == null) doorUidMap.set(oldUid.toString(), newUid.toString());

                    if ((door as any)["wall_ind"] != null) {
                        const newWallInd = wallUidMap.get((door as any)["wall_ind"].toString());
                        if (newWallInd != null) {
                            (door as any)["wall_ind"] = Number(newWallInd);
                        }
                    }
                });
                    
                innerwall.window_list.forEach((window: any) => {
                    const oldUid = window.uid;
                    let newUid : number;
                    const strNewUid = windowUidMap.get(oldUid.toString());
                    if (strNewUid != null) newUid = Number(strNewUid);
                    else newUid = ++nextUid;
                    window.uid = newUid;
                    if (oldUid != null && strNewUid == null) windowUidMap.set(oldUid.toString(), newUid.toString());

                    if ((window as any)["wall_ind"] != null) {
                        const newWallInd = wallUidMap.get((window as any)["wall_ind"].toString());
                        if (newWallInd != null) {
                            (window as any)["wall_ind"] = Number(newWallInd);
                        }
                    }
                });
            });
        });
    }

    static async getSchemeXmlJsonBySchemeId_withHouseService(schemeId:string,traceId:string)
    {
        let house = await HouseSchemeRepositoryService.getHouseSchemeDataBySchemeId(schemeId);
        if(!house?.xmlUrl) return null;
        let response = await fetch(house.xmlUrl).then(val=>val.text()).catch(e=>null);
        if(!response)
        {
            return null;
        }
        let xmlContent = response;
        let xmlSchemeJson:I_SwjXmlScheme = await SchemeXmlParseService.parseSchemeXml2Json(xmlContent,traceId);
        xmlSchemeJson.xml_str = xmlContent;

        // let xmlSchemeJson  = await DesignXmlParser.instance.parseXmlStr( inflate_from_base64_str( xmlContent,"utf-8"));

        return xmlSchemeJson;


    }
    static async getSchemeXmlBySchemeId(schemeId:string, authCode:string,channelcode:"code_room_scheme"|"code_own_scheme"|"code_debug"="code_room_scheme")
    {
        let url = "/api/sdapi/scheme/getbychannelcode";

        let res : any = await BasicRequest.magiccubeRequest({method: 'post',
                url: url,
                data: {
                  schemeId: schemeId,
                  authCode : authCode,
                  channelcode: channelcode
                },
                timeout: 3000})
                .catch((e:any)=> {
                    console.error(e)
                });

        if(res.success === true && res?.result?.scheme?.contentUrl)
        {
            let scheme = res?.result?.scheme;

            let content_url = scheme.contentUrl;

            let t_res = await fetch(staticHost+content_url).then(res=>res.text()).catch(e=>{
                console.log(e);
                return null;
            });
            if(!t_res)
            {
                await fetch( 'https://hws-static.3vjia.com/'+content_url).then(res=>res.text()).catch(e=>{
                    console.log(e);
                    return null;
                });
            }

            if(!t_res) return null;

            // console.log(await get_swj_xml_from_url(staticHost+content_url));
            
            
            // DesignXmlParser.instance.parseXmlUrl(staticHost+content_url);
            return await SchemeXmlParseService.parseSchemeXml2Json(t_res, GenDateUUid());
        }

        return null;
    }

    static async getSchemeXmlBySchemeId_WithLocalParser(schemeId:string, authCode:string,channelcode:"code_room_scheme"|"code_own_scheme"|"code_debug"="code_room_scheme")
    {
        let url = "/api/sdapi/scheme/getbychannelcode";

        let res : any = await  BasicRequest.magiccubeRequest({method: 'post',
                url: url,
                data: {
                  schemeId: schemeId,
                  authCode : authCode,
                  channelcode: channelcode
                },
                timeout: 3000})
                .catch((e:any)=> {
                    console.error(e)
                });

        if(res.success === true && res?.result?.scheme?.contentUrl)
        {
            let scheme = res?.result?.scheme;

            let content_url = scheme.contentUrl;

            let t_res = await fetch(staticHost+content_url).then(res=>res.text()).catch(e=>{
                console.log(e);
                return null;
            });
            if(!t_res)
            {
                await fetch( 'https://hws-static.3vjia.com/'+content_url).then(res=>res.text()).catch(e=>{
                    console.log(e);
                    return null;
                });
            }

            if(!t_res) return null;

            // console.log(await get_swj_xml_from_url(staticHost+content_url));
            let scheme_res  = await DesignXmlParser.instance.parseXmlUrl(staticHost+content_url);
            if(scheme_res)
            {
                scheme_res.scheme_id = schemeId;
                scheme_res.name = scheme.schemeName;
                return scheme_res;
            }
        }

        return null;
    }
}
