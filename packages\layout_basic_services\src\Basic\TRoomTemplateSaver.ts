import { BasicRequest } from "@layoutai/basic_request";
import { CadDrawingLayerType, LayoutAI_App, LayoutSchemeXmlJsonParser, Logger, TAppManagerBase, TFigureElement, TFurnitureEntity, TLayoutEntityContainer, TRoom, TRoomEntity, TRoomLayoutScheme, TSubSpaceAreaEntity, TWall } from "@layoutai/layout_scheme";
import { GenDateUUid, Vec3toMeta, ZRect, formatCurrentTime, saveBufferAs } from "z_polygon";
import { DrawingFigureMode, I_Room, I_SwjFurnitureData, I_SwjLineEdge, I_SwjRoom, I_SwjWall, I_SwjXmlScheme } from "@layoutai/basic_data";
import { uploadImageToOss } from "./basic_utils";
import { LayoutSchemeJsonSaver } from "@layoutai/layout_scheme";
import { Vector3 } from "three";

export interface IRoomTemplateSavingOptions
{
    /**
     *  3D方案ID, 或者是按日期计算的临时ID
     */
    schemeId?:string;
    /**
     *  户型ID
     */
    houseTypeId?:string;
    /**
     *  布局方案ID
     */
    layoutSchemeId?:string;
    /**
     *  标记项, 或者叫标签项
     */
    remark?:string;
    /**
     *  来源: 区分梦想家dreamer|图灵turing|编辑器editor 默认为空
     */
    source?:string;
    /**
     *  别名或昵称
     */
    nickName?:string;
    /**
     *  特征码A
     */
    codeA?: string;
    /**
     *  特征码B
     */
    codeB?: string;
    /**
     *  特征码C
     */
    codeC?: string;
    /**
     *  空间名称 
     */
    roomName ?: string,
    /**
     *  空间类型: 比 空间名称 更一般化， 空间类型 更具体
     */
    roomType?:string;
    /**
     *  房间ID
     */
    roomId?:string;
    /**
     *  房间uId
     */
    uId?:string;

    /**
     *  描述
     */
    description?:string,

    /**
     *  是否是平台
     */
    platform ?: number;

}
export interface IRoomTemplatesSavingResult{
    [key: string]: { room_name: string, area: number, room_id: number, success: boolean } 
}
export class TRoomTemplateSaver {
    static async saveRoomEntityTemplate(room_entity: TRoomEntity,
        resultMap:IRoomTemplatesSavingResult, options:IRoomTemplateSavingOptions={}): Promise<any> {

        let container = LayoutSchemeXmlJsonParser.Container;

        room_entity._room = room_entity.makeTRoom(container._furniture_entities,false);

        let furniture_element_list: TFigureElement[] = [];
        for (let ele of room_entity._room._furniture_list) {
            if (ele._is_decoration) continue;
            furniture_element_list.push(ele);
        }
        if (furniture_element_list.length == 0) return null;

        return await TRoomTemplateSaver.saveTRoomTemplate(room_entity._room, resultMap, furniture_element_list, options);
    }

    static async saveTRoomTemplate(room: TRoom, resultMap: IRoomTemplatesSavingResult,furniture_list: TFigureElement[] = null, options:IRoomTemplateSavingOptions={}): Promise<any> {
        let container = LayoutSchemeXmlJsonParser.Container;

        let scheme_id = options.schemeId || container._scheme_id;
        // if (container._saving_template_configs.needCheckSameLayout) {
        //     let has_same_scheme = TRoomLayoutScheme.checkHasSameScheme(room, furniture_list);

        //     if (has_same_scheme) {
        //         console.log("存在相似方案, 要覆盖需要删除原方案", has_same_scheme._scheme_name);
        //         return;
        //     }
        // }

        let template_json = JSON.stringify(TRoomTemplateSaver.saveTRoomToJson(room, furniture_list));
        if (!TLayoutEntityContainer.CanvasForSaving) {
            TLayoutEntityContainer.CanvasForSaving = document.createElement("canvas");
        }
        room.drawOnCanvas(container.painter, TLayoutEntityContainer.CanvasForSaving, furniture_list,{draw_wall:options.source!=="SubSpace",draw_text:true});

        let img_src = TLayoutEntityContainer.CanvasForSaving.toDataURL();

        let codeA = "";

        if (room.room_shape._feature_shape) {
            codeA = room.room_shape._feature_shape.level_shape_codes[0] || "";
        }
        let codeB = "";
        if (room.feature_shapes[0]) {
            codeB = room.feature_shapes[0].level_shape_codes[0] || "";
            if (codeA === codeB) {
                codeB = "";
            }
        }

        let main_rect = room?.max_R_shape?._rect || ZRect.computeMainRect(room.room_shape._poly);

        let room_save_data = {
            roomArea: Math.round(room.area * 100) / 100,
            roomLength: Math.round(main_rect.length),
            roomWidth: Math.round(main_rect.depth),
            roomHeight: 2800,
            schemeId: scheme_id,
            source: container._layout_source || "layout2d",
            roomName: room.name || room.roomname,
            remark:room.name,
            roomType: room.room_type,
            roomId: '' + room.room_id,
            uId: '' + room.uid,
            roomShape: "R",
            codeA: codeA,
            codeB: codeB,
            templateJson: template_json,
            roomImage: img_src,
            ...options // 直接用options替代
        }
        if (room_save_data.source === "dreamer") {
            (room_save_data as any).platform = 2;
        }
        else {
            if (container._is_platform) {
                (room_save_data as any).platform = container._is_platform;

            }
        }
        // console.log("   Save layout for: ",room_entity._room.toString());
        try {
            const res : any = await  BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/roomLayoutTemplate/saveTemplate`,
                data: room_save_data,
                timeout: 60000,
            });
            resultMap[room.uid] = { room_name: room.roomname, area: room.area, room_id: room.room_id, success: res && res.success };
        } catch (e) {
            console.error(e);
            return null;
        };
    }

    static async updateTRoomCodes(room: TRoom, scheme_id: string = null) {

        let container = LayoutSchemeXmlJsonParser.Container;
        scheme_id = scheme_id || container._scheme_id;
        if (!TLayoutEntityContainer.CanvasForSaving) {
            TLayoutEntityContainer.CanvasForSaving = document.createElement("canvas");
        }

        let img_src = TLayoutEntityContainer.CanvasForSaving.toDataURL();

        let codeA = "";
        if (room.room_shape._feature_shape) {
            codeA = room.room_shape._feature_shape.level_shape_codes[0] || "";
        }
        let codeB = "";
        if (room.feature_shapes[0]) {
            codeB = room.feature_shapes[0].level_shape_codes[0] || "";
            if (codeA === codeB) {
                codeB = "";
            }
        }

        let main_rect = room.max_R_shape._rect || ZRect.computeMainRect(room.room_shape._poly);

        let room_save_data = {
            roomArea: Math.round(room.area * 100) / 100,
            roomLength: Math.round(main_rect.length),
            roomWidth: Math.round(main_rect.depth),
            roomHeight: 2800,
            // source: container._layout_source,
            schemeId: scheme_id,
            roomName: room.roomname,
            roomId: '' + room.room_id,
            uId: '' + room.uid,
            codeA: codeA,
            codeB: codeB,
        }
        // console.log("   Save layout for: ",room_entity._room.toString());
        try {
            const res = await BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/roomLayoutTemplate/saveTemplate`,
                data: room_save_data,
                timeout: 60000,
            });
        } catch (e) {
            console.error(e);
            return false;
        };
        return true;
    }

    static async saveRoomTemplates(selected_room_only: boolean = true): Promise<any> {

        let container = LayoutSchemeXmlJsonParser.Container;
        if (!TLayoutEntityContainer.CanvasForSaving) {
            TLayoutEntityContainer.CanvasForSaving = document.createElement("canvas");
        }

        let resultMap: { [key: string]: { room_name: string, area: number, room_id: number, success: boolean } } = {};

        if (selected_room_only) {
            await TRoomTemplateSaver.saveTRoomTemplate(container._selected_room, resultMap);

        }
        else {
            for (let room_entity of container._room_entities) {
                // console.log(room_entity._room._furniture_list);

                await TRoomTemplateSaver.saveRoomEntityTemplate(room_entity, resultMap);
                // if(save_candidates)
                // {
                //     if (room_entity._room._candidate_layouts != null) {
                //         for (let candidate of room_entity._room._candidate_layouts) {
                //             room_entity._room._furniture_list = candidate;
                //             await container.saveRoomEntityTemplate(room_entity, resultMap);
                //         }
                //     }
                // }

            }
        }

        return resultMap;
    }

    static async loadRoomTemplate(scheme_id: string, room_id: string) {

        let container = LayoutSchemeXmlJsonParser.Container;
        // console.log(scheme_id,room_id);
        let fetch_func = async () => {
            const res = await BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/roomLayoutTemplate/templateInfo`,
                data: {
                    "id": scheme_id
                },
                timeout: 3000,
            }).catch((e: any) => {
                return null as any;
            });
            if (!res?.result) return null;
            return res.result;
        }
        let res = await fetch_func();

        if (!res) return false;

        try {
            let target_json = null;
            target_json = JSON.parse(res.templateJson);
            if (target_json) {
                TRoomTemplateSaver.loadRoomEntityFromJson(target_json);
            }
        } catch (error) {
        }

        return false;
    }


    static async saveSubAreaEntityTemplate(spaceAreaEntity: TSubSpaceAreaEntity, options:IRoomTemplateSavingOptions={})
    {
        if(!spaceAreaEntity._space_area_room)
        {
            spaceAreaEntity.updateSpaceAreaTRoom();
        }
        if(!spaceAreaEntity._space_area_room) return false;
        const container = (LayoutAI_App.instance as TAppManagerBase).layout_container;

        // spaceAreaEntity.clearFurnitureEntities();
        let furniture_element_list: TFigureElement[] = [];
        spaceAreaEntity.furniture_entities.forEach((entity)=>{
            furniture_element_list.push(...entity.disassembled_figure_elements);
        });
        options.remark = spaceAreaEntity.tags;
        options.schemeId = options.schemeId || container._scheme_id || formatCurrentTime({format_type:3});
        
        options.layoutSchemeId = options.layoutSchemeId || container._layout_scheme_id || "";
        options.roomId = ''+(spaceAreaEntity._space_area_room?.room_id||spaceAreaEntity._space_area_room.uid);
        options.source = "SubSpace";
        if(!spaceAreaEntity._space_area_room.room_shape._feature_shape)
        {
            spaceAreaEntity._space_area_room.updateFeatures();
        }
        let resultMap : IRoomTemplatesSavingResult = {};
        await TRoomTemplateSaver.saveTRoomTemplate(spaceAreaEntity._space_area_room,resultMap,furniture_element_list,options);
        if(resultMap[spaceAreaEntity._space_area_room.uid])
        {
            return true;
        }
        return false;
    }





    static loadRoomEntityFromJson(data: { room_data: I_Room, swj_room_data: I_SwjRoom}) {

        let container = LayoutSchemeXmlJsonParser.Container;
        let room = new TRoom();
        room.fromSwjRoom(data.swj_room_data);


        let wall_list: I_SwjWall[] = [];

        let poly = room.room_shape._poly.clone();
        let t_poly = poly.clone().expandPolygon(120);
        for (let i in poly.edges) {
            let edge = poly.edges[i];
            let t_edge = t_poly.edges[i];

            let t_points: Vector3[] = [
                edge.v0.pos, edge.center, edge.v1.pos,
                t_edge.v1.pos, t_edge.center, t_edge.v0.pos
            ]

            let boundary: I_SwjLineEdge[] = [];
            for (let i = 0; i < t_points.length; i++) {
                let p0 = t_points[i];
                let p1 = t_points[(i + 1) % t_points.length];

                boundary.push({
                    start: Vec3toMeta(p0),
                    end: Vec3toMeta(p1)
                })
            }
            let wall_entity = new TWall({ boundary: boundary });
            wall_entity.update();

            wall_list.push(wall_entity.exportData());

        }
        let swj_scheme_json: I_SwjXmlScheme = {
            scheme_id: data.swj_room_data.scheme_id,
            wall_list: wall_list,
            room_list: [data.swj_room_data],
        }

        container._scheme_id = "";

        container.fromXmlSchemeData(swj_scheme_json, false);
        container.painter._p_sc = 0.1;
        container.focusCenter();
        LayoutAI_App.instance.update();
    }

    static saveTRoomToJson(room: TRoom, furniture_list: TFigureElement[] = null, is_drawing_canvas: boolean = true) {
        if (!room.feature_shapes[0]) {
            room.updateFeatures();
        }

        let container = LayoutSchemeXmlJsonParser.Container;
        let room_data = room.exportSwjRoomData();
        room_data.furniture_list = [];
        furniture_list = furniture_list || room._furniture_list;
        for (let figure_ele of furniture_list) {

            let rect = figure_ele.rect;

            let entity: TFurnitureEntity = rect._attached_elements['Entity'] || new TFurnitureEntity(figure_ele);

            let t_data = entity.exportData() as I_SwjFurnitureData;
            t_data._figure_element = entity.figure_element.exportJson();
            room_data.furniture_list.push(t_data);
        }
        if (is_drawing_canvas) {
            room.drawOnCanvas(container.painter, TLayoutEntityContainer.CanvasForSaving);
        }
        room_data.scheme_id = container._scheme_id;
        return {
            room_data: room.exportRoomData(),
            swj_room_data: room_data
        };
    }

}