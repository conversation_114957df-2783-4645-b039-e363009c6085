import { Box3, Vector3, <PERSON>ector3<PERSON><PERSON> } from "three";
import { getSign } from "../user";
import { Uploader } from '@svg/oss-upload';
import { Logger, deflate_to_base64_str } from "@layoutai/layout_scheme";
import { base64ToFile } from "z_polygon";



/**
 * @description 上传图片到oss
 * @param img_base64
 * @param name
 */

export async function uploadImageToOss(img_base64: string, name: string = 'test.png') {
    const type: number = 12
    const res = await getSign(type, name);
    console.log('res', res);

    const { accessKeyId, expireAt, ossHost, policy, securityToken, keyPrefix, vendor, path, signature, contentType, readDomain } = res;
    let uploader: any;
    if (uploader) uploader.stop(true);
    uploader = new Uploader({
        contentType,
        policy,
        signature,
        accessKeyId,
        path: keyPrefix,
        securityToken,
        server: ossHost,
        vendor,
    } as any);
    const file = await base64ToFile(img_base64, name);

    return await uploader.upload(file, (file1: any, percent: number) => {
        // console.log(file1,percent,'up');
    })
        .then((data: any) => {
            const imageUrl = `${readDomain}${keyPrefix}`;
            console.log('返回图片的地址', `${readDomain}${keyPrefix}`);
            return imageUrl;
        }).catch((e: any): null => {
            return null;
        })
}


/**
 * @description 上传文件到oss
 * @param img_base64
 * @param name
 */

export async function uploadFileToOss(file: File, name: string = 'test.json') {
    const type: number = 12
    const res = await getSign(type, name);
    console.log('res', res);
    if (!res) return null;

    const { accessKeyId, expireAt, ossHost, policy, securityToken, keyPrefix, vendor, path, signature, contentType, readDomain } = res;
    let uploader: any;
    if (uploader) uploader.stop(true);
    uploader = new Uploader({
        contentType: 'application/json',
        policy,
        signature,
        accessKeyId,
        path: keyPrefix,
        securityToken,
        server: ossHost,
        vendor,
    } as any
    );

    return await uploader.upload(file, (file1: any, percent: number) => {
        // console.log(file1,percent,'up');
    })
        .then((data: any) => {
            const imageUrl = `${readDomain}${keyPrefix}`;
            console.log('返回文件的地址', `${readDomain}${keyPrefix}`);
            return imageUrl;
        }).catch((ev: any): any => {
            return null;
        })
}

export function getCookieValue(name: string): string | null {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
        return parts.pop().split(';').shift();
    }
    return null;
}

/**
 * @description 加黑加粗线框图
 * @param url
 */
export async function addBlackAndBoldLine(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        img.src = url;
        img.crossOrigin = "Anonymous";
        
        img.onload = function () {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const a = data[i + 3];

                if (a > 0 && (r < 255 || g < 255 || b < 255)) {
                    data[i] = Math.max(0, r - 180);
                    data[i + 1] = Math.max(0, g - 180);
                    data[i + 2] = Math.max(0, b - 180);
                }
            }
            ctx.putImageData(imageData, 0, 0);
            let img_url = canvas.toDataURL('image/png');
            resolve(img_url);
        };

        img.onerror = reject;
    });
}
