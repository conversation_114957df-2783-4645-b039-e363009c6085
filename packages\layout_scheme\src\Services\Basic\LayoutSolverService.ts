import { SolverMethods } from "@layoutai/basic_data";
import { EventName } from "../../EventSystem";
import { LayoutAI_App, LayoutAI_Commands } from "../../LayoutAI_App";
import { TLayoutEntityContainer } from "../../TLayoutEntities/TLayoutEntityContainter";
import { TRoomLayoutScheme } from "../../TLayoutScheme/TRoomLayoutScheme";
import { TWholeLayoutScheme } from "../../TLayoutScheme/TWholeLayoutScheme";
import { TRoomEntity } from "../../TLayoutEntities/TRoomEntity";
import { Logger } from "../../TLayoutEntities/utils/logger";
import { TAppManagerBase } from "../../AppManagerBase";
import { TRoom } from "../../TRoom/TRoom";
import { compareNames } from "z_polygon";
import { LayoutContainerUtils } from "../../TLayoutEntities/utils/LayoutContainerUtils";


export class LayoutSolverService
{
    private static _instance : LayoutSolverService = null;
    private _layout_container : TLayoutEntityContainer = null;
    private whole_layout_scheme_list:TWholeLayoutScheme[] = [];
    constructor()
    {
    }
    get layout_container()
    {
        return this._layout_container || TLayoutEntityContainer.instance;
    }
    set layout_container(t:TLayoutEntityContainer)
    {
        this._layout_container = t;
    }

    get layout_graph_solver()
    {
        return (LayoutAI_App.instance as TAppManagerBase).layout_graph_solver;
    }
    get room_list()
    {
        return this.layout_container._rooms;
    }
    public static get instance() : LayoutSolverService 
    {
        if(!LayoutSolverService._instance)
        {
            LayoutSolverService._instance = new LayoutSolverService();
        }
        return LayoutSolverService._instance;
    }
    async computeWholeLayoutSchemeList(append_furniture_entities: boolean = true) {
        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: true, title: "AI推荐计算中..." });


        
        await this.ai_layout_for_whole_house(append_furniture_entities);

        // 2025.04.25: 新增默认先query分区模板 用于测试
        LayoutAI_App.RunCommand(LayoutAI_Commands.QuerySpaceTemplates);

        LayoutAI_App.emit(EventName.OnPreparingHandle, { opening: false, title: null });
        this.upudateWholeLayoutSchemeList();
    }

    upudateWholeLayoutSchemeList() {
        let whole_layout_scheme_list = [];
        let scheme_ids_list: string[] = [];
        let room_schemes_list: TRoomLayoutScheme[][] = [];
        let room_list = this._layout_container._rooms;
        for (let room of room_list) {
            if (!room._layout_scheme_list) continue;

            let temp_scheme_list = [...room._layout_scheme_list];
            if (room._furniture_list.length > 0) {
                let diy_scheme = room.toLayoutScheme();
                temp_scheme_list = [diy_scheme, ...room._layout_scheme_list];
            }
            room_schemes_list.push(temp_scheme_list);
        }

        {
            let whole_layout_scheme0 = new TWholeLayoutScheme(this.layout_container);
            let scheme_ids = "";
            for (let room_schemes of room_schemes_list) {
                let scheme = room_schemes[0];
                scheme_ids += '0_';
                if (scheme && scheme.figure_list.figure_elements.length > 0) {
                    whole_layout_scheme0._room_scheme_list.push(scheme);
                }
            }
            scheme_ids_list.push(scheme_ids);
            whole_layout_scheme_list.push(whole_layout_scheme0);
        }

        {
            let whole_layout_scheme1 = new TWholeLayoutScheme(this.layout_container);
            let scheme_ids = "";
            for (let room_schemes of room_schemes_list) {
                let scheme: TRoomLayoutScheme = null;
                for (let j = 1; j >= 0; j--) {
                    scheme = room_schemes[j];
                    if (scheme) {
                        scheme_ids += j + '_';
                        break;
                    }
                }
                if (scheme && scheme.figure_list.figure_elements.length > 0) {
                    whole_layout_scheme1._room_scheme_list.push(scheme);
                }
            }
            scheme_ids_list.push(scheme_ids);
            whole_layout_scheme_list.push(whole_layout_scheme1);
        }

        let target_num = 5;
        // 依次类推模式
        for (let id = 2; id < 5; id++) {
            let scheme_ids = "";
            let whole_layout_scheme = new TWholeLayoutScheme(this.layout_container);
            for (let room_schemes of room_schemes_list) {
                let t_id = Math.min(id, room_schemes.length - 1);
                let scheme = room_schemes[t_id];
                scheme_ids += t_id + '_';
                if (scheme && scheme.figure_list.figure_elements.length > 0) {
                    whole_layout_scheme._room_scheme_list.push(scheme);
                }
            }
            if (!scheme_ids_list.includes(scheme_ids)) {
                scheme_ids_list.push(scheme_ids);
                whole_layout_scheme_list.push(whole_layout_scheme);
                if (whole_layout_scheme_list.length >= target_num) break;
            }
        }


        this.whole_layout_scheme_list = whole_layout_scheme_list.filter((scheme) => scheme._room_scheme_list.length > 0);

        this.postProcessWholeLayoutSchemeList();

        if (!this.layout_container._selected_room) {
            LayoutAI_App.emit_M(EventName.WholeLayoutSchemeList, { schemeList: this.whole_layout_scheme_list || [], index: 0 })
        }
        LayoutAI_App.instance.update();
    }
    protected postProcessWholeLayoutSchemeList() {
        this.whole_layout_scheme_list.forEach(wholeLayoutScheme => {
            wholeLayoutScheme.postProcess();
        });
    }
    async ai_layout_for_whole_house(append_furniture_entites: boolean = true) {
        // console.log("全屋推荐...");
        for (let room of this.room_list) {
            room._layout_scheme_list = [];
        }
        await this.applyRoomEntitiesWithSolvingMethods(null, ["BasicTransfer", "GroupTransfer", "SpacePartition"], {append_furniture_entites:append_furniture_entites,needs_make_group_templates:true},Logger.instance);

    }

    public async applyRoomWithSolvingMethods(room: TRoom, solver_methods: SolverMethods[] = ["BasicTransfer", "SpacePartition"], logger: Logger = null) {
        let result_scheme_list = await this.layout_graph_solver.applyRoomWithSolvingMethods(room, this?.layout_container._src_swj_layout_data?.xml_str || null, solver_methods, logger);

        if (result_scheme_list && result_scheme_list[0]) {
            result_scheme_list.forEach((val) => val.room = room);
            room._layout_scheme_list = result_scheme_list.filter((scheme) => {
                // 准出规则： 1. 每项规则的评分都大于-100
                let recommended = scheme.checkIsValidByLayoutScores();
                if (compareNames([room.roomname], ["卧室", "厨房", "卫生间", "客餐厅", "入户花园"])) {
                    return LayoutAI_App.IsDebug || recommended;
                } else {
                    return true;
                }

            });
            if (room._layout_scheme_list.length == 0) {
                if (result_scheme_list.length > 0) {
                    room._layout_scheme_list.push(result_scheme_list[0]);
                }
            }

            room._layout_scheme_list.forEach((layout_scheme) => {
                let totalScore = 0;
                if (layout_scheme._scheme_name.includes("相似")) {
                    totalScore += 20;
                }
                layout_scheme._layout_scores.forEach((item) => {
                    totalScore += item.score;
                });
                layout_scheme.totalScore = totalScore;
            });

            room._layout_scheme_list = room._layout_scheme_list.sort((a, b) => b.totalScore - a.totalScore);

            result_scheme_list = room._layout_scheme_list;

        }

        return result_scheme_list;

    }


    public async applyRoomEntitiesWithSolvingMethods(room_entities: TRoomEntity[] = null,
        solver_methods: SolverMethods[] = ["BasicTransfer", "SpacePartition"],
        options: { append_furniture_entites?: boolean, needs_make_group_templates?: boolean, force_auto_sub_area?: boolean }
            = { append_furniture_entites: true, needs_make_group_templates: true }, logger: Logger = null) {
        room_entities = room_entities || this.layout_container._room_entities;

        let queryRoomCount = 0;
        let apply_room = async (room_entity: TRoomEntity) => {
            if(queryRoomCount < 10)
            {
                await this.layout_graph_solver.queryModelRoomsFromServer([room_entity._room], false, false);
            }

            queryRoomCount++;
            // 在调用之前开始计时
            console.time('applyRoomWithSolvingMethods ' + room_entity.roomname + room_entity._uuid);
            await this.applyRoomWithSolvingMethods(room_entity._room, solver_methods, logger);
            // 在调用之后结束计时
            console.timeEnd('applyRoomWithSolvingMethods ' + room_entity.roomname + room_entity._uuid);
        }
        let promises = [];

        for (let room_entity of room_entities) {
            if (!room_entity._room) {
                room_entity.makeTRoom(this.layout_container._furniture_entities, false);
            }
            room_entity._room.roomname = room_entity.roomname;
            promises.push(apply_room(room_entity));
        }

        await Promise.allSettled(promises);

        for (let room_entity of room_entities) {
            let room = room_entity._room;
            let is_auto_sub_area = room_entity.is_auto_sub_area || (options.force_auto_sub_area || false);
            if (room._furniture_list.length == 0 && options.append_furniture_entites) {
                if (room._layout_scheme_list && room._layout_scheme_list.length > 0) {
                    room.addFurnitureElements(room._layout_scheme_list[0].figure_list.figure_elements);
                }
                this.layout_container.addFunitureEnitiesInRoom(room, false, options.needs_make_group_templates);
            }

            if (is_auto_sub_area) {
                LayoutContainerUtils.postAutoUpdateSubAreas(room_entity, this.layout_container, options);
            }
            else if (options.append_furniture_entites) {
                LayoutContainerUtils.postProcessInRoom(room_entity._room);
            }

        }

    }
}

// 导出单例实例
export const layoutSolverService = LayoutSolverService.instance;