import { saveFileAs } from "z_polygon";

export class IndexedDBService
{
    private static _instance : IndexedDBService = null;

    private _db : IDBDatabase = null;


    static BuildingSchemeDataTable = "BuildingSchemeDataTable";
    static TestingDatasetTable = "TestingDatasetTable";
    static TestingDatasetNamesTable = "TestingDatasetNamesTable";
   
    static TestingTaskTable = "TestingTaskTable";
    static TestingTaskConfigsTable = "TestingTaskConfigsTable";
    static TestingTaskDetailTable = "TestingTaskDetailTable";
    static TestingTaskMarkTable = "TestingTaskMarkTable";
    static DefaultTable = "default";
    static DefaultDbName = "layout_ai_indexed_db";
    private DefaultDbName = "layout_ai_indexed_db";

    private static _isReady : boolean = false;
    constructor()
    {

    }
    static get isReady()
    {
        return IndexedDBService._isReady;
    }
    static  get instance()
    {
        if(!IndexedDBService._instance)
        {
            IndexedDBService._instance = new IndexedDBService();
            // IndexedDBService._instance.openDB(IndexedDBService._instance.DefaultDbName);
        }
        return IndexedDBService._instance;
    }


    async openDB(dbname:string = IndexedDBService.DefaultDbName)
    {

        let scope = this;
        const tableNames = [IndexedDBService.DefaultTable,IndexedDBService.BuildingSchemeDataTable,
        IndexedDBService.TestingDatasetNamesTable,IndexedDBService.TestingDatasetTable,IndexedDBService.TestingTaskTable,
        IndexedDBService.TestingTaskDetailTable,IndexedDBService.TestingTaskConfigsTable,IndexedDBService.TestingTaskMarkTable];
        let current_db_version = 1;

        let databaseInfos = await window.indexedDB.databases();
        databaseInfos.forEach((databaseInfo)=>{
            if(databaseInfo.name === dbname)
            {
                current_db_version = databaseInfo.version || 1;
            }
        })
        const _tryToOpenDB =async (dbname:string,version:number)=>
            new Promise<boolean>((resolve,reject)=>{
                let request = window.indexedDB.open(dbname,version);
                request.onerror = function(e){
                    console.log(e);
                    resolve(false);
                };
                request.onsuccess = function(e){
                    let target = e.target as IDBOpenDBRequest;
                    let db = target.result;
                    scope._db = db;
                    current_db_version = db.version;
                    // console.log(db.objectStoreNames);
                    let checkTablesCreated = true;
                    for(let name of tableNames)
                    {
                        if(!db.objectStoreNames.contains(name))
                        {
                            checkTablesCreated = false;
                        }
                    }
                    if(!checkTablesCreated)
                    {
                        db.close();
                    }
                    resolve(checkTablesCreated);
                };
                request.onblocked = (ev)=>{
                    console.log(ev);
                    // resolve(false);
                }
                request.onupgradeneeded=function(e){
                    let target = e.target as IDBOpenDBRequest;
                    let db=target.result;
                    let transaction= target.transaction;
                    for(let tableName of tableNames)
                    {
                        if(!db.objectStoreNames.contains(tableName))
                        {
                            db.createObjectStore(tableName,{keyPath:'id'});
                        }
                    }
                    
                }        
         });

         let try_num = 2;
        //  current_db_version++;
         IndexedDBService._isReady = false;
         while(try_num--)
         {
            let success = await _tryToOpenDB(dbname,current_db_version);
            // console.log(success);
            if(success) {
                IndexedDBService._isReady = true;
                break;
            }
            current_db_version++;
         }   
    }

    async addData(data:any, tableName="default",dbObj:IDBDatabase=null)
    {
        if(!IndexedDBService._isReady) return null;
        dbObj = dbObj || this._db;
        return await new Promise<any>((resolve,reject)=>{


            var transaction = dbObj.transaction(tableName, 'readwrite');
            transaction.oncomplete = function () {
                // console.log("transaction complete");
            };
            transaction.onerror = function (event) {
                // console.dir(event)
            };
        
            var objectStore = transaction.objectStore(tableName);
            var request = objectStore.put(data);
        
            request.onsuccess = function (e) {
                resolve(data);
            };
            request.onerror = function (e) {
                resolve({
                    error: e
                })
            }
    
        })

    }

    async count(tableName="default",dbObj:IDBDatabase=null)
    {
        if(!IndexedDBService._isReady) return null;

        dbObj = dbObj || this._db;
        return await new Promise<number>((resolve,reject)=>{
            var transaction = dbObj.transaction(tableName, 'readwrite');
            transaction.oncomplete = function () {
                // console.log("transaction complete");
            };
            transaction.onerror = function (event) {
                // console.dir(event)
            };
            var objectStore = transaction.objectStore(tableName);
            var request = objectStore.count();
        
            request.onsuccess = function (e) {
                resolve(request.result);
            };
            request.onerror = function (e) {
                resolve(0)
            }
    
        })
    }

    async removeData(id:string, tableName="default",dbObj:IDBDatabase=null)
    {
        if(!IndexedDBService._isReady) return null;

        dbObj = dbObj || this._db;
        return await new Promise((resolve,reject)=>{


            var transaction = dbObj.transaction(tableName, 'readwrite');
            transaction.oncomplete = function () {
                // console.log("transaction complete");
            };
            transaction.onerror = function (event) {
                // console.dir(event)
            };
        
            var objectStore = transaction.objectStore(tableName);
            var request = objectStore.delete(id);
        
            request.onsuccess = function (e) {
                resolve({
                    success:true,
                    id : id
                });
            };
            request.onerror = function (e) {
                resolve({
                    error: e
                })
            }
    
        })
    }

    async addTestingDataset(data:{id:string,name:string, count?:number, [key:string]:any})
    {
        let name_data = {id:data.id,name:data.name,count:data?.buildingList?.length||0};
        await this.addData(name_data,IndexedDBService.TestingDatasetNamesTable);
        return await this.addData(data,IndexedDBService.TestingDatasetTable);
    }
    async removeTestingDataset(id:string)
    {
        await this.removeData(id,IndexedDBService.TestingDatasetNamesTable);
        return await this.removeData(id,IndexedDBService.TestingDatasetTable);

    }
    async exportAllTestingDataset(option:{download?:boolean,filename?:string}={download:true})
    {
        let data = await this.getAll(IndexedDBService.TestingDatasetTable);
        if(option.download)
        {
            saveFileAs(JSON.stringify(data),option.filename||"AllTestingDataset.json","text/json");
        }
    }
    async importTestingDataset(dataset_list:{id:string,name:string,[key:string]:any}[])
    {
        for(let data of dataset_list)
        {
            await this.addTestingDataset(data);
        }
    }

    async getTestingDatasetList()
    {
        let data = await this.getAll(IndexedDBService.TestingDatasetNamesTable);
        return data;
    }

    async getTestingDatasetById(id:string)
    {
        return (await IndexedDBService.instance.getDataById(id,IndexedDBService.TestingDatasetTable));
    }

    async getDataById(id:string,  tableName="default",dbObj:IDBDatabase=null)
    {
        if(!IndexedDBService._isReady) return null;

        dbObj = dbObj || this._db;

        return await new Promise<any>((resolve,reject)=>{
            var transaction = dbObj.transaction(tableName, 'readwrite');
            transaction.oncomplete = function () {
                // console.log("transaction complete");
            };
            transaction.onerror = function (event) {
                console.dir(event)
            };
         
            var objectStore = transaction.objectStore(tableName);
            var request = objectStore.get(id);
            request.onsuccess = function (e) {
                resolve((e.target as any).result);
            };
            request.onerror = function (e) {
                resolve(null)
            }
   
        })
    }

    async getKeys(tableName="default", dbObj : IDBDatabase=null)
    {
        if(!IndexedDBService._isReady) return null;

        dbObj = dbObj || this._db;

        return await new Promise<any>((resolve,reject)=>{
            var transaction = dbObj.transaction(tableName, 'readonly');
            transaction.oncomplete = function () {
                // console.log("transaction complete");
            };
            transaction.onerror = function (event) {
                console.dir(event)
            };
         
            var objectStore = transaction.objectStore(tableName);
            var request = objectStore.getAllKeys();
            request.onsuccess = function (e) {
                resolve((e.target as any).result);
            };
            request.onerror = function (e) {
                resolve(null)
            }
   
        })
    }
    async getAll(tableName="default", dbObj : IDBDatabase=null)
    {
        if(!IndexedDBService._isReady) return null;

        dbObj = dbObj || this._db;

        return await new Promise<any>((resolve,reject)=>{
            var transaction = dbObj.transaction(tableName, 'readonly');
            transaction.oncomplete = function () {
                // console.log("transaction complete");
            };
            transaction.onerror = function (event) {
                console.dir(event)
            };
         
            var objectStore = transaction.objectStore(tableName);
            var request = objectStore.getAll();
            request.onsuccess = function (e) {
                resolve((e.target as any).result);
            };
            request.onerror = function (e) {
                resolve(null)
            }
   
        })
    }
}