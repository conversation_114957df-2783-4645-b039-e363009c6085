import {
    AI_PolyTargetType,
    IRoomEntityRealType,
    IRoomEntityType
} from "@layoutai/basic_data";
import { Vector3 } from "three";
import { ZRect } from "z_polygon";
import { LayoutAI_App, LayoutAI_Commands } from "../../LayoutAI_App";
import { T_GroupOperationInfo } from "../../OperationInfos/Operations/T_GroupOpertaionInfo";
import { TGroupTemplate } from "../../TGroupTemplate/TGroupTemplate";
import { g_FigureImagePaths } from "../../TPainter/FigureImagePaths";
import { TPainter } from "../../TPainter/TPainter";
import { T_MoveWinDoorElement } from "../../TransformElements/T_MoveWinDoorElement";
import { T_TransformElement } from "../../TransformElements/T_TransformElement";
import { TBaseEntity } from "../TBaseEntity";
import { TBaseGroupEntity } from "../TBaseGroupEntity";
import { TFurnitureEntity } from "../TFurnitureEntity";
import { TGroupTemplateEntity } from "../TGroupTemplateEntity";
import {
    TLayoutEntityContainer
} from "../TLayoutEntityContainter";
import { TStructureEntity } from "../TStructureEntity";
import { TWindowDoorEntity } from "../TWinDoorEntity";
import { T_AddFurnitureOprationInfo } from "../../OperationInfos/Operations/T_AddFurnitureOprationInfo";

/**
 *  Entity创建(添加)工具
 *    --- 将素材级中的实体 添加进场景
 */
export class TEntityCreator {
    protected _container: TLayoutEntityContainer;

    // 待选实体集合
    protected _candidate_entities_map: Map<string, TBaseEntity>;

    protected static Key_OutOfRooms = "OutOfRooms";

    protected _transform_elements: T_TransformElement[];

    private _combination_edit: boolean = false;

    private _adding_figure_entity: TBaseEntity = null;

    constructor(container: TLayoutEntityContainer) {
        this._container = container;
        this._candidate_entities_map = new Map();
    }
    public get adding_figure_entity(): TBaseEntity {
        return this._adding_figure_entity;
    }
    public set adding_figure_entity(value: TBaseEntity) {
        this._adding_figure_entity = value;
    }

    get transform_elements() {
        return this._transform_elements;
    }

    get entity_combiner() {
        return this.container.entity_combiner;
    }

    get entity_selector() {
        return this.container.entity_selector;
    }

    get selected_target() {
        return this.container.entity_selector.selected_target;
    }
    get container() {
        return this._container;
    }

    clear() {
        this._candidate_entities_map.clear();
    }
    clearCandidates() {
        this._candidate_entities_map.clear();
    }

    get candidate_entities() {
        return Array.from(this._candidate_entities_map.values());
    }

    get painter() {
        return this.container.painter;
    }

    get manager()
    {
        return LayoutAI_App.instance;
    }
    drawCanvas(painter: TPainter) { }

    addStructureEntity(
        type: IRoomEntityType,
        realType: IRoomEntityRealType,
        label: string,
        furnitureTitle: string
    ) {
        let addedFurniture = g_FigureImagePaths[furnitureTitle];
        let I_SwjStructure = TBaseEntity.makeSimpleEntityData(type, realType);
        let entity = new TStructureEntity(I_SwjStructure, null);
        entity.length = 900;
        entity.width = 240;
        entity._rect._w = addedFurniture.length;
        entity._rect._h = addedFurniture.depth;
        entity._rect.ex_prop["label"] = realType;
        entity._rect._nor.set(0, -1, 0);
        if (!type) {
            entity.rect.rect_center = this.painter.p_center;
        } else {
            entity._rect.rect_center = new Vector3(-9999999, -9999999, 0);
        }
        TBaseEntity.set_polygon_type(entity._rect, type);
        LayoutAI_App.RunCommand(LayoutAI_Commands.AddFurniture); //结构件也是调用图元的添加handler
    }

    addWinDoorEntity(
        type: IRoomEntityType,
        realType: IRoomEntityRealType,
        label: string,
        furnitureTitle: string
    ) {
        let addedFurniture = g_FigureImagePaths[furnitureTitle];
        this.entity_selector.cleanSelection();
        let I_SwjDoor = TBaseEntity.makeSimpleEntityData(type, realType);
        let entity = new TWindowDoorEntity(I_SwjDoor, null);
        entity.length = 900;
        entity.width = 240;
        entity._rect._w = addedFurniture.length;
        entity._rect._h = addedFurniture.depth;
        entity._rect.rect_center = new Vector3(-10000, -10000, 0);
        this.adding_figure_entity = entity;

        this.selected_target.selected_rect = entity._rect;
        this.selected_target.selected_rect.ex_prop["label"] = label;
        TBaseEntity.set_polygon_type(this.selected_target.selected_rect, type);
        this.selected_target.selected_transform_element = new T_MoveWinDoorElement(
            this.selected_target,
            this.container
        );
        let _previous_rect = this.selected_target.selected_rect.clone();
        this.selected_target.selected_transform_element.recordOriginRect(
            _previous_rect
        );
        this.selected_target.selected_transform_element.bindTargetRect(
            entity._rect,
            []
        );
        LayoutAI_App.RunCommand(LayoutAI_Commands.Transform_MovingStruture);
    }

    addGroupTemplate(furnitureTitle: string, type: boolean = false) {
        const group_template_prefix = "GroupTemplate:";
        let group_code = furnitureTitle.substring(group_template_prefix.length);

        let group_template = new TGroupTemplate();
        group_template.group_code = group_code;

        let info = TGroupTemplate.GroupCodeUiInfo[group_code];

        group_template._target_rect = new ZRect(
            info?.default_length || 100000,
            info?.default_depth || 100000
        );
        group_template.updateByTargetRect();

        if (group_template.current_s_group) {
            let rect = new ZRect(1, 1);

            rect.ex_prop["label"] = furnitureTitle;
            rect.ex_prop["is_edit"] = "is_edit";
            rect._nor.set(0, -1, 0);
            rect._w = group_template.current_s_group.group_rect.w;
            rect._h = group_template.current_s_group.group_rect.h;
            rect.rect_center = new Vector3(-9999999, -9999999, 0);
            if (!type) {
                rect.rect_center = this.painter.p_center;
            } else {
                rect.rect_center = new Vector3(-9999999, -9999999, 0);
            }
            rect.updateRect();
            group_template._target_rect = rect;

            group_template.updateByTargetRect();
            rect._attached_elements[TGroupTemplate.EntityName] = group_template;
            TBaseEntity.set_polygon_type(rect, AI_PolyTargetType.Furniture);

            let entity = TGroupTemplateEntity.getOrMakeEntityOfCadRect(
                rect
            ) as TGroupTemplateEntity;
            return entity;
        }
        return null;
    }
    addBaseGroupEntity(event_param: string) {
        let group_rect = new ZRect(1, 1);
        let combination_rect = this.selected_target.selected_rect;
        group_rect._w = combination_rect._w;
        group_rect._h = combination_rect._h;
        group_rect._nor = combination_rect._nor.clone();
        group_rect.ex_prop['poly_target_type'] = 'BaseGroup';
        group_rect.ex_prop['GroupName'] = event_param;
        group_rect.rect_center = combination_rect.rect_center.clone();
        group_rect._attached_elements['combination_entitys'] = [
          ...this.selected_target.selected_combination_entitys
        ];
        group_rect.updateRect();
        let baseGroup_entity = TBaseGroupEntity.getOrMakeEntityOfCadRect(
          group_rect
        ) as TBaseGroupEntity;
        baseGroup_entity.combination_entitys = [...this.selected_target.selected_combination_entitys];
        baseGroup_entity.updateCategory(event_param);
        let info = new T_GroupOperationInfo(LayoutAI_App.instance, 'Group');
        info._group_base_entity = baseGroup_entity;
        info._furniture_entities = this.selected_target.selected_combination_entitys.map(
          (entity: TFurnitureEntity) => {
            TBaseGroupEntity.recordGroupRectData(entity, baseGroup_entity);
            return entity;
          }
        );
        info.redo();
    
        
        this.manager.appendOperationInfo(info);
      }
    
    onFigureSelected(furnitureTitle: string, needs_add_entity: boolean) {
        const WinDoorMapping: {
          [key: string]: { type: IRoomEntityType; name: IRoomEntityRealType; alias: string };
        } = {
          单开门: { type: 'Door', name: 'SingleDoor', alias: 'door' },
          推拉门: { type: 'Door', name: 'SlidingDoor', alias: 'slidedoor' },
          一字窗: { type: 'Window', name: 'OneWindow', alias: 'window' },
          飘窗: { type: 'Window', name: 'BayWindow', alias: 'baywindow' },
          双开门: { type: 'Door', name: 'DoubleDoor', alias: 'doubledoor' },
          子母门: { type: 'Door', name: 'SafetyDoor', alias: 'safetydoor' },
          门洞: { type: 'Door', name: 'DoorHole', alias: 'doorhole' },
          垭口: { type: 'Door', name: 'PassDoor', alias: 'passdoor' },
          栏杆: { type: 'Window', name: 'Railing', alias: 'railing' }
        };
        const StructureMapping: {
          [key: string]: { type: IRoomEntityType; name: IRoomEntityRealType; alias: string };
        } = {
          包管: { type: 'StructureEntity', name: 'Envelope_Pipe', alias: 'Envelope_Pipe' },
          地台: { type: 'StructureEntity', name: 'Platform', alias: 'Platform' },
          方柱: { type: 'StructureEntity', name: 'Pillar', alias: 'Pillar' },
          横梁: { type: 'StructureEntity', name: 'Beam', alias: 'Beam' },
          烟道: { type: 'StructureEntity', name: 'Flue', alias: 'Flue' }
        };
    
        let winDoorKey = Object.keys(WinDoorMapping).find(key => furnitureTitle.includes(key));
        let matchedKey = Object.keys(StructureMapping).find(key => furnitureTitle.includes(key));
        if (furnitureTitle.indexOf('GroupTemplate:') >= 0) {
          let entity = this.addGroupTemplate(furnitureTitle, needs_add_entity);
          this.adding_figure_entity = entity;
          if (needs_add_entity) {
            LayoutAI_App.RunCommand(LayoutAI_Commands.AddFurniture);
          }
          return;
        }
    
        if (winDoorKey) {
          let { type, name, alias } = WinDoorMapping[winDoorKey];
          this.addWinDoorEntity(type, name, alias, furnitureTitle);
        } else if (matchedKey) {
          let { type, name, alias } = StructureMapping[matchedKey];
          this.addStructureEntity(type, name, alias, furnitureTitle);
        } else {
          let furniture_toadd = g_FigureImagePaths[furnitureTitle];
          let rect = new ZRect(1, 1);
          if (furniture_toadd) {
            rect.reParaFromVertices(2);
            rect.ex_prop['label'] = furnitureTitle;
            rect.ex_prop['is_edit'] = 'is_edit';
            rect._nor.set(0, -1, 0);
            rect._w = furniture_toadd.length;
            rect._h = furniture_toadd.depth;
            if (!needs_add_entity) {
              rect.rect_center = this.painter.p_center;
            } else {
              rect.rect_center = new Vector3(-9999999, -9999999, 0);
            }
            TBaseEntity.set_polygon_type(rect, AI_PolyTargetType.Furniture);
            let entity = TFurnitureEntity.getOrMakeEntityOfCadRect(rect);
            this.adding_figure_entity = entity;
            if (needs_add_entity) {
              LayoutAI_App.RunCommand(LayoutAI_Commands.AddFurniture);
            }
          }
        }
    }
    addNewEntitiy() {
        if (!this._adding_figure_entity) return;
        let _adding_figure_rect = this._adding_figure_entity._rect;
        if (_adding_figure_rect) {
            _adding_figure_rect.ex_prop["is_added"] = "added";
            let info = new T_AddFurnitureOprationInfo(this.manager);
            let entity = TBaseEntity._FuncGetOrMakeEntityOfCadRect(_adding_figure_rect);
            if (_adding_figure_rect.ex_prop['label'].indexOf("GroupTemplate:") >= 0) {
                let _entity = entity as TGroupTemplateEntity;
                let group_entity = _entity.toBaseGroupEntity() as TBaseGroupEntity;
                this.selected_target.selected_combination_entitys = group_entity.combination_entitys;
                entity = group_entity;
            }

            info.target_rect = _adding_figure_rect;
            info._entity = entity as TFurnitureEntity | TWindowDoorEntity | TBaseGroupEntity | TStructureEntity;
            info._history_info.current_ploy = _adding_figure_rect.clone();
            info.isCopyGroup = false;
            info.redo(this.manager);
            this.manager.appendOperationInfo(info);
            if (info._entity instanceof TFurnitureEntity) {
                this.container.findAndBindRoomForFurnitures([info._entity]);
            }
            this.selected_target.selected_rect = entity.matched_rect || _adding_figure_rect;
            this.selected_target.hover_rect = null;
            entity.is_selected = true;

            this.container.updateEntityRelations();
            // this.cleanSelection();
        }
        // 更新素材到TRoom中
        this.container.updateRoomsFromEntities(false);
        // 离开当前子模式
        this._adding_figure_entity = null;
    }

 
}
