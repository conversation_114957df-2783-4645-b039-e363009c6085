

import { AI_PolyTargetType, I_SwjBaseGroup, I_SwjCabinetData, I_SwjEntityBase, I_SwjFurnitureData, I_SwjFurnitureGroup, I_SwjWall, I_SwjWindow, I_SwjXmlScheme, LayoutSchemeData, SchemeSourceType } from "@layoutai/basic_data";
import { TLayoutEntityContainer } from "../TLayoutEntityContainter";
import { TBoard3D, ZPolygon, ZRect, compareNames } from "z_polygon";
import { TBaseEntity } from "../TBaseEntity";
import { TWall } from "../TWall";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { Vector3 } from "three";
import { TRoomEntity } from "../TRoomEntity";
import { TWindowDoorEntity } from "../TWinDoorEntity";
import { TStructureEntity } from "../TStructureEntity";
import { determineFigureLabel } from "../../TPainter/FigureImagePaths";
import { ModelLocPublicCategoryMap } from "../../TFigureElements/ModelLocPubliccategoryMap";
import { TBaseGroupEntity } from "../TBaseGroupEntity";
import { TFurnitureEntity } from "../TFurnitureEntity";
import { TGroupTemplate } from "../../TGroupTemplate/TGroupTemplate";
import { TGroupTemplateEntity } from "../TGroupTemplateEntity";
import { TExtDrawingParser } from "../TExtDrawingElements/TExtDrawingParser";
import { TSubSpaceAreaEntity } from "../TSubSpaceAreaEntity";
import { LayoutContainerUtils } from "../utils/LayoutContainerUtils";
import { LayoutAI_Version } from "../../LayoutAI_Version";
import { base64ToUtf8 } from "../utils/xml_utils";
import { DesignXmlMaker } from "../utils/DesignXmlMaker";
import { refineWalls } from "../utils/SwjDataBasicFuncs";
import { LayoutAI_App } from "../../LayoutAI_App";
import { TAppManagerBase } from "../../AppManagerBase";
import { CadBatchDrawingLayerType } from "../../TDrawingLayer/TDrawingLayer";
import { TRoom } from "../../TRoom/TRoom";

export class LayoutSchemeJsonSaver {

    static saveLayoutSchemeImage(width: number = 1200, height: number = 1200, fixed_scale: number = 0, save_room_name: boolean = false, selected_room: TRoom = null) {

        let container = TLayoutEntityContainer.instance;
        if(!container) return null;
        let canvas = document.createElement("canvas") as HTMLCanvasElement;

        canvas.setAttribute("crossorigin", "anonymous");


        canvas.width = width;
        canvas.height = height;

        let origin_canvas = container.painter._canvas;
        let ts = container.painter.exportTransformData();
        container.painter.bindCanvas(canvas);

        let s_pc = container.painter._p_sc;
        container.painter._p_sc = 0.1;

        container.painter._p_sc = height / (origin_canvas.height) * s_pc;
        container.focusCenter();

        if (fixed_scale > 0) {
            
            container.updateWholeBox();
            let size = container._whole_bbox.getSize(new Vector3());
            let center = container._whole_bbox.getCenter(new Vector3());
            let sc_0 = width / size.x;
            let sc_1 = height / size.y;
            container.painter.p_center = center;
            container.painter._p_sc = Math.min(sc_0, sc_1) * fixed_scale;
        }
        else {
            container.painter._p_sc = height / (origin_canvas.height) * s_pc;

        }

        container.painter.enter_drawpoly();

        let manager = LayoutAI_App.instance as TAppManagerBase;

        if (manager._batch_drawing_layers[CadBatchDrawingLayerType.AICadDefaultBatch]) {
            let batchLayer = manager._batch_drawing_layers[CadBatchDrawingLayerType.AICadDefaultBatch];
            batchLayer.onDraw();
        }
        // if (manager.layer_CadFurnitureLayer) {
        //     manager.layer_CadFurnitureLayer.onDraw();
        // }
        // if (manager.layer_CadCabinetLayer) {
        //     manager.layer_CadCabinetLayer.onDraw();
        // }
        // if (manager.layer_CadRoomFrameLayer) {
        //     manager.layer_CadRoomFrameLayer.onDraw();
        // }
        // if (manager.layer_OutLineLayer) {
        //     manager.layer_OutLineLayer.onDraw();
        // }
        // if (save_room_name) {
        //     manager.layer_CadRoomNameLayer.onDraw();
        // }

        if (selected_room) {
            container.painter.strokeStyle = "#147ffA";
            container.painter.fillStyle = "#ff3";

            container.painter.fillPolygons([selected_room.room_shape._poly], 0.1);
            container.painter._context.lineWidth = 2;
            container.painter.strokePolygons([selected_room.room_shape._poly]);
        }

        container.painter.leave_drawpoly();


        container.painter.bindCanvas(origin_canvas);
        container.painter.importTransformData(ts, false);

        let img_url = canvas.toDataURL();
        // document.body.removeChild(canvas);
        return img_url;

    }


}