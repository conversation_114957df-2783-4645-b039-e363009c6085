import { SolverMethods, TRoomNameDict } from "@layoutai/basic_data";
import { Logger } from "../TLayoutEntities/utils/logger";
import { TRoomLayoutScheme } from "../TLayoutScheme/TRoomLayoutScheme";
import { I_ExtRoom, TRoom } from "../TRoom/TRoom";
import { TSubSpaceAreaEntity } from "../TLayoutEntities/TSubSpaceAreaEntity";

export const SolverMethodsDict: { [key: string]: string } = {
    BasicTransfer: "相似迁移",
    SpacePartition: "分区逻辑",
};
export interface I_SolverQueryConfig {
    series_ids?: string[]; // 指定套系Id
    model_room_ids?: string[]; // 指定样板间Id
}
export interface I_RoomTemplateResult {
    codeA?: string;
    codeB?: string;
    codeC?: string;
    nickName?: string;
    createDate?: string;
    createUser?: string;
    updateDate?: string;
    updateUser?: string;
    id?: string;
    isDelete?: number;
    platform?: number;
    templateJson?: string;
    roomImage?: string;
    roomLength?: number;
    roomWidth?: number;
    roomType?: string;
    roomName?: string;
    schemeId?: string;
    roomId?: string;
    source?: string;
    state?: number;
}

export interface I_RoomTemplateQuery {
    pageIndex?: number;
    pageSize?: number;
    // orderBy?:"update_date desc";
    lengthMin?: number;
    lengthMax?: number;
    codeA?: string;
    codeB?: string;
    roomType?: string;
    id?: string;
    schemeId?: string;
    remark?: string;
    source?: "editor" | "dreamer" | "SubSpace";
}
export enum I_ModelRoomSourceType {
    Default,
    LocalStorage,
    QueryServer,
    PreQueryServer,
}

export class TLayoutSolverBase {
    static instance: TLayoutSolverBase = null;

    /**
     *  是否向后端请求
     */
    protected _query_backend: boolean = false;

    /**
     *   加载过样板间的标记
     */
    protected _is_model_rooms_loaded: boolean = false;

    protected _is_query_server_model_rooms: boolean = true;

    protected _is_init_graph: boolean = false;

    constructor() {
        TLayoutSolverBase.instance = this;
     }
    public get is_init_graph(): boolean {
        return this._is_init_graph;
    }
    public set is_init_graph(value: boolean) {
        this._is_init_graph = value;
    }
    public get is_query_server_model_rooms(): boolean {
        return this._is_query_server_model_rooms;
    }
    public set is_query_server_model_rooms(value: boolean) {
        this._is_query_server_model_rooms = value;
    }
    public get is_model_rooms_loaded(): boolean {
        return this._is_model_rooms_loaded;
    }
    public set is_model_rooms_loaded(value: boolean) {
        this._is_model_rooms_loaded = value;
    }
    public get query_backend(): boolean {
        return this._query_backend;
    }
    public set query_backend(value: boolean) {
        this._query_backend = value;
    }
    /**
     * 添加样板间
     * @param rooms
     */
    addModelRooms(
        rooms: I_ExtRoom[],
        source_type: I_ModelRoomSourceType = I_ModelRoomSourceType.Default
    ) { }

    addDreamRooms(
        rooms: I_ExtRoom[],
        source_type: I_ModelRoomSourceType = I_ModelRoomSourceType.Default,
        clean_old_data: boolean = false
    ) { }

    /**
     *  添加用于组合迁移的样板间, 两个可以不一样
     */
    addGroupTransferModelRooms(
        rooms: I_ExtRoom[],
        src_type: I_ModelRoomSourceType = I_ModelRoomSourceType.PreQueryServer
    ) { }

    addSubSpaceTransferModelRooms(
        rooms: I_ExtRoom[],
        src_type: I_ModelRoomSourceType = I_ModelRoomSourceType.PreQueryServer
    ) { }

    /**
     * 初始化 布局模板-图谱
     */
    initGraphs() {
        this._is_init_graph = true;
    }

    /**
     *  清空样板间
     */
    cleanAllModelRooms() {
        this.initGraphs();
    }

    async applyRoom(
        room_data: I_ExtRoom,
        query_config: I_SolverQueryConfig = null
    ) {
        if (this._query_backend) {
            return await this.applyRoom_backend(room_data);
        } else {
            return this.applyRoom_Local(room_data);
        }
    }

    async applyRoomWithSolvingMethods(
        room: TRoom,
        xml_str: string = "",
        solver_methods: SolverMethods[] = [
            "BasicTransfer",
            "SpacePartition",
        ],
        logger: Logger = null
    ) {
        // 有一些计算逻辑的问题, 所以用一个room的副本比较好
        let t_room: TRoom = new TRoom(room.exportRoomData());
        // 布局中用_room_entity会比room_shape更准确
        t_room._room_entity = room._room_entity;
        t_room.area = room.area;
        t_room.updateFeatures();
        let result_scheme_list =
            this.applyRoom_Directly(t_room, solver_methods) || [];
        return result_scheme_list;
    }
    /**
     *
     * 向后端请求
     * @param room_data
     */
    async applyRoom_backend(
        room_data: I_ExtRoom,
        query_config: I_SolverQueryConfig = null
    ) {
        let result_scheme_list: TRoomLayoutScheme[] = [];

        return result_scheme_list;
    }
    /**
     *  对目标房间使用
     *   --- 返回布局方案
     */
    applyRoom_Local(
        room_data: I_ExtRoom,
        query_config: I_SolverQueryConfig = null
    ) {
        let room_name =
            TRoomNameDict[
            room_data?.swj_room?.name ||
            room_data?.room?.room_type ||
            room_data?.room?.roomname ||
            ""
            ] || null;
        if (!room_name) return null;

        let room: TRoom = null;

        if (room_data.swj_room) {
            room = new TRoom({}).fromSwjRoom(room_data.swj_room);
        } else if (room_data.room) {
            if (room_data.room) {
                room = new TRoom(room_data.room);
            }
        }
        if (!room) return null;
        room.updateFeatures();
        return this.applyRoom_Directly(room);
    }

    applyRoom_Directly(
        room: TRoom,
        solver_methods: SolverMethods[] = [
            "BasicTransfer",
            "GroupTransfer",
            "SpacePartition",
            "SubSpaceTransfer",
        ]
    ) {
        if (!room) return null;
        let result_scheme_list: TRoomLayoutScheme[] = [];
        return result_scheme_list;
    }

    async preQueryModelRoomsFromServer(page_size:number=300)
    {
        return;

    }

    async cleanModelRoomsWithSrcType(src_type:I_ModelRoomSourceType = I_ModelRoomSourceType.QueryServer)
    {
               
    }
    async _queryModelTemplates(options:I_RoomTemplateQuery = {}) :Promise<I_RoomTemplateResult[]>
    {
        return null;

    }
    async queryModelRoomsFromServer(rooms:TRoom[],allow_no_code_query:boolean = true,clean_old_data:boolean=true)
    {

    }
    async queryModelSubSpacesFromServer(spaceEntity:TSubSpaceAreaEntity=null,options:I_RoomTemplateQuery={})
    {

    }
}
