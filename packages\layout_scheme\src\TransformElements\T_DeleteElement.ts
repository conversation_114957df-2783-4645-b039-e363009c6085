
import { Vector3, Vector3<PERSON><PERSON> } from "three";
import { <PERSON><PERSON><PERSON>, ZRect } from "z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState } from "../LayoutAI_App";
import { I_SelectedTarget } from "../TLayoutEntities/TEntitySelector/TEntitySelector";
import { TLayoutEntityContainer } from "../TLayoutEntities/TLayoutEntityContainter";
import { TRoomEntity } from "../TLayoutEntities/TRoomEntity";
import { LayoutAI_Configs } from "../TLayoutEntities/configures/LayoutAIConfigs";
import { TPainter } from "../TPainter/TPainter";
import { T_TransformElement } from "./T_TransformElement";


export class T_DeleteElement extends T_TransformElement {
    _dir_val: number;
    _nor_val: number;
    _fixed_pos: Vector3;
    _container: TLayoutEntityContainer;
    current_alignment_Edges: ZEdge[] = [];
    protected _selected_target: I_SelectedTarget;
    t_pos: Vector3;
    constructor(dir_val: number, nor_val: number, _selected_target: I_SelectedTarget, container: TLayoutEntityContainer) {
        super();
        this._dir_val = dir_val;
        this._nor_val = nor_val;

        this._element_name = "Scale-(" + this._dir_val + "," + this._nor_val + ")";
        this._container = container;
        this._fixed_pos = null;
        this._origin_shape_rect = null;
        this._selected_target = _selected_target;
    }

    get room_entities(): TRoomEntity[] {
        return this._container._room_entities;
    }
    get alignment_rects(): ZRect[] {
        return this._container.getCandidateRects(["Furniture"]);
    }

    checkEditRect(pos: Vector3Like, _p_sc: number) {
        const is_moblie = LayoutAI_Configs.isMobile || false;
        if (is_moblie && this._selected_target.selected_entity && this._selected_target.selected_entity.checkHaveMatchRect()) return false;
        if (!is_moblie && this._container._drawing_layer_mode === 'AIMatching') return false;
        if (this.isTargetRectTypeValid()) {
            let t = this._element_rect.rect_center.distanceTo(pos) < this.radius / _p_sc;
            return t;
        } else {
            return false;
        }
    }

    updateElement(): void {
        if (!this._target_rect) return;
        this.pos = this._target_rect.unproject({ x: this._target_rect._w / 2 * this._dir_val, y: this._target_rect._h / 2 * this._nor_val });

        let dv = this.pos.clone().sub(this._target_rect.rect_center);
        dv.normalize();

        this._cursor_state = LayoutAI_CursorState.Pointer;
    }

    onhover(): void {

    }

    onselect(): void {

        LayoutAI_App.RunCommand(LayoutAI_Commands.DeleteFurniture);
    }



    applyTransformByMovement(movement: Vector3, adsorb_rects: ZRect[] = []): void {
        if (!this._origin_shape_rect) return;
        if (!this._target_rect) return;
        this._align_line = true;
        this._fixed_pos = this._origin_shape_rect.unproject({ x: this._origin_shape_rect._w / 2 * -this._dir_val, y: this._origin_shape_rect._h / 2 * -this._nor_val });
    }

    drawCanvas(painter: TPainter): void {

        if (!this.visible) return;
        if (this._target_rect.ex_prop.label === "相机" || this._target_rect.ex_prop.label === "人物") return;
        const is_mobile = LayoutAI_Configs.isMobile;
        if (is_mobile && this._selected_target.selected_entity && this._selected_target.selected_entity.checkHaveMatchRect()) return;
        if (!is_mobile && this._container._drawing_layer_mode === 'AIMatching') return;
        painter.fillStyle = "#fff";
        painter.strokeStyle = "#147FFA";

        if (this.isTargetRectTypeValid() && !this.is_moving) {
            // 绘制点
            const arrowRect = new ZRect(1, 1);
            arrowRect._w = 30 / painter._p_sc;
            arrowRect._h = 30 / painter._p_sc;
            painter._context.lineWidth = 5;
            arrowRect.rect_center = this.pos.clone();
            const radius = !is_mobile ? 17 : 10;
            painter.strokeStyle = "#ccc";
            painter.fillStyle = "#fff";
            painter.drawPointCircle(this.pos, radius / painter._p_sc);
            painter.fillPointCircle(this.pos, radius / painter._p_sc);
            painter.drawFigureRect(arrowRect, '删除', ['删除']);
        }

    }

}