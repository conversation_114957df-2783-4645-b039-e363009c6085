
import { AI_PolyTargetType, IRoomEntityType } from "@layoutai/basic_data";
import { Vector3, Vector3Like } from "three";
import { ZDistanceDimension, ZEdge, ZRect } from "z_polygon";
import { EventName } from "../EventSystem";
import { LayoutAI_App, LayoutAI_Events } from "../LayoutAI_App";
import { TBaseEntity } from "../TLayoutEntities/TBaseEntity";
import { TLayoutEntityContainer } from "../TLayoutEntities/TLayoutEntityContainter";
import { LayoutAI_Configs } from "../TLayoutEntities/configures/LayoutAIConfigs";
import { TPainter } from "../TPainter/TPainter";
import { T_TransformElement } from "./T_TransformElement";

export class T_DimensionElement extends T_TransformElement
{
    _wall_rects:ZRect[];
    _figure_rects:ZRect[];
    _container:TLayoutEntityContainer;
    _target_wall_edge:ZEdge;

    _existingInput: HTMLInputElement;
    _fixed_edit_pos : Vector3;   //中间编辑的标尺点
    dir: number;
    openEdit: boolean;
    constructor(dir: number,container:TLayoutEntityContainer,entity_types:IRoomEntityType[]=["Furniture","BaseGroup","Group", "StructureEntity"])
    {
        super(); 
        this._container = container;   
        this.dir = dir;
        this._element_name = "T_DimensionElement"
        this._wall_rects = [];
        this._figure_rects = [];
        this._target_wall_edge = null;
        this._fixed_edit_pos = null;
        this._allow_entity_types = entity_types;
        this.openEdit = false;
        this._isMovingVisible = true;
        this.IsDimensionElement = true;
    }

    get painter(): TPainter {
        return this._container.painter;
    }

    checkEditRect(pos: Vector3Like, _p_sc: number): boolean {
        return false;
    }
    startTransform(adsorb_rects: ZRect[] = []) {
        if(!this._target_rect) return;

        let target_rect = this._target_rect;
        this._dir_id = this.dir;

        this._wall_rects = this._container._wall_entities.map((entity)=>entity.rect);
        this._figure_rects = this._container._furniture_entities.map((entity)=>entity.rect);

        let target_room_entity = this._container._room_entities.find((room_entity)=>{
            return room_entity._room_poly.containsPoint(this._target_rect.rect_center);
        });

        if(target_room_entity)
        {
            this._wall_rects = this._wall_rects.filter((rect)=>{
                let res = target_room_entity._room_poly.comparePolyDistance(rect,120);
                if(res.overlap_length > 10)
                {
                    return true;
                }
                return false;
            })
            this._figure_rects = this._figure_rects.filter((rect)=>{
                return target_room_entity._room_poly.containsPoint(rect.rect_center,240);
            })
        }
    }
    applyTransformByMovement(movement: Vector3, adsorb_rects:ZRect[] = [], figure_rects: ZRect[] = []): void {
        if(!this._origin_shape_rect) return;
        if(!this._target_rect) return;
        this._wall_rects = adsorb_rects;
        this._figure_rects = figure_rects;
    }

    updateElement(): void {
        if(!this._target_rect) return;

        let target_rect = this._target_rect;
        let t_type = TBaseEntity.get_polygon_type(target_rect);
        let is_struture = false;
        let structures_types:IRoomEntityType[] = ["Window","Door"];
        is_struture = structures_types.includes(t_type);
        if(is_struture )
        {
            // 门窗标尺使用了T_DimensionDWElement
            return ;
        }

        this._dir_id = this.dir;
        let is_wall = t_type ==="Wall";





        let edge1 = null;
        let center: Vector3 = null;
        let nor: Vector3 = null;
        if(is_struture || Math.abs(target_rect._nor.x) > 0.9 && Math.abs(target_rect._nor.y) < 0.1 || Math.abs(target_rect._nor.y) > 0.9 && Math.abs(target_rect._nor.x) < 0.1)
        {
            edge1 = target_rect.edges[this._dir_id];
            center = edge1.center;
            nor = edge1.nor;
            this._nor_v3 = nor;
        } 
        else 
        {
            center = target_rect.vertices[this._dir_id].pos;
            
            let vertex = target_rect.vertices.find(vertex => vertex.pos.equals(center));
            if (vertex.pos.x === Math.min(...target_rect.vertices.map(v => v.pos.x))) {
                vertex.normal = new Vector3(-1, 0, 0);  // 最左边的顶点
            } else if (vertex.pos.x === Math.max(...target_rect.vertices.map(v => v.pos.x))) {
                vertex.normal = new Vector3(1, 0, 0); // 最右边的顶点
            } else if (vertex.pos.y === Math.min(...target_rect.vertices.map(v => v.pos.y))) {
                vertex.normal = new Vector3(0, -1, 0); // 最下面的点
            } else if (vertex.pos.y === Math.max(...target_rect.vertices.map(v => v.pos.y))) {
                vertex.normal = new Vector3(0, 1, 0); // 最上面的点
            }
    
            nor = vertex.normal;
            this._nor_v3 = nor;
            
        }

        
        let m_dist = 999999999;
        let m_x_dist = 9999999;
        this._target_wall_edge = null; 
        const updateTargetWallEdge = (rects: ZRect[]) => {
            for(let wall of rects)
            {
                if(TBaseEntity.is_deleted(wall)) continue;
                for(let w_edge of wall.edges)
                {
                    if((w_edge.nor.dot(nor) > -0.1)) continue;
                    let pp = w_edge.projectEdge2d(center);
                    let extendedPoint = center.clone().add(nor.clone().multiplyScalar(10000));
                    let edge = new ZEdge({pos: center},{pos:extendedPoint});
                    edge.computeNormal();
                    let ans =  edge.getIntersection(w_edge);
                    let pp_ = w_edge.projectEdge2d(ans);
                    if(pp_.x < -350 || pp_.x > w_edge.length + 350) continue;
                    if(pp.y <-0.1) continue;
                    if(!this._target_wall_edge || pp.y < m_dist)
                    {
                        this._target_wall_edge = w_edge;
                        m_dist = pp.y;
                    }

                }

            }
        }
        if(is_struture)
        {
            updateTargetWallEdge(this._wall_rects);
        } else 
        {
            updateTargetWallEdge(this._wall_rects);
            if(!is_wall)
            {
                updateTargetWallEdge(this._figure_rects);

            }
        }
        
        if(this._target_wall_edge)
        {
            this._pos0 = center.clone().setZ(0);
            let extendedPoint = center.clone().add(nor.clone().multiplyScalar(10000));
            let edge = new ZEdge({pos: center},{pos:extendedPoint});
            edge.computeNormal();
            let ans =  edge.getIntersection(this._target_wall_edge);
            this._pos1 = ans;
            
            // let pp = this._target_wall_edge.projectEdge2d(this._pos0);
            // this._pos1 = this._target_wall_edge.unprojectEdge2d({x:pp.x,y:0});
            let length = this._pos0.distanceTo(this._pos1);
            // 将文字的位置赋值给this.pos
            this.pos = this._pos0.clone().add(this._pos1.clone()).multiplyScalar(0.5);
            this._edit_num = Math.abs(length);
            this._fixed_edit_pos = this._pos0.clone().add(this._pos1.clone()).multiplyScalar(0.5);
            this._existingInput = document.querySelector(`.edit_input${this._dir_id}`);

            if(!this._existingInput)
            {
                // let div = this.painter._canvas.parentElement || document.getElementById("body_container");
                let div = document.getElementById('Canvascontent');
                this._existingInput = document.createElement("input") as HTMLInputElement;
                div.appendChild(this._existingInput);
                this.setInputStyle(this._existingInput);
                this.setAttribute();
            }
            // 编辑的时候才去赋值，不然会重新刷新输入框的值
            if(!this.openEdit)
            {
                this.setAttribute();
            }

            if( this._existingInput && (this.is_moving || this._edit_num < 30 ) )
            {
                this._existingInput.style.display = 'none';
            } else 
            {
                this._existingInput.style.display = 'block';
            }

        }
    
        
    }

    setAttribute(): void {
        this._existingInput.value = Math.round(this._edit_num).toString();
        this._existingInput.setAttribute('origin_num',String(this._edit_num));
        this._existingInput.setAttribute('_nor_v3', JSON.stringify(this._nor_v3));
        this._existingInput.style.width = ((this._existingInput.value.length + 1) * 7) + 'px';
    }
    // 触发编辑框确认逻辑
    updateEditNum(target_rect?: ZRect) {
        if (!this._existingInput || !this._existingInput.value) return;
        if(TBaseEntity.get_polygon_type(this._target_rect) === AI_PolyTargetType.Wall){
            LayoutAI_App.DispatchEvent(LayoutAI_Events.DimensionWall, this._existingInput);
        } else 
        {
            LayoutAI_App.DispatchEvent(LayoutAI_Events.DimensionInput, this._existingInput);

        }

    }
    setInputStyle(input: HTMLInputElement) {
        input.className = `edit_input${this._dir_id}`;
        input.id = `edit_input${this._dir_id}`;
        input.autocomplete = "off";
        input.style.display = 'block';
        if(LayoutAI_Configs.isMobile)
        {
            input.readOnly = true;
        }
        input.onfocus = () => {
            input.select();    
            this._existingInput = input;
            // let index = this._figure_rects.indexOf(this._target_rect);
            // this._existingInput.setAttribute('rectIndex',String(index));
            // LayoutAI_App.RunCommand(LayoutAI_Commands.Transform_Dimension);
            this.openEdit = true;
            if(LayoutAI_Configs.isMobile)
            {
                LayoutAI_App.emit(EventName.showCustomKeyboard, {visible:true, input:input});
            }
        };
        input.onblur = () => {
            if(input.value === "")
            {
                input.setAttribute('origin_num',String(this._edit_num));
                input.value = String(this._edit_num);
            }
            this.openEdit = false;
        }
        input.onkeydown = (event) => {
            // 在这里处理键盘按键事件
            if (event.key === 'Enter') {
                this.updateEditNum(this._target_rect);
                input.blur();
            }
          };
        input.oninput = function() {
            input.value = input.value.replace(/[^0-9]/g, '');
            input.style.width = ((input.value.length + 1) * 7) + 'px';
        };
        input.onmouseup = () => {
        };
    }
    updateInputPosition(input: HTMLInputElement, pp: { x: number, y: number }) {
        input.style.left = (pp.x - input.clientWidth/2) + "px";
        input.style.top = (pp.y - input.clientHeight/2) + "px";
    }

    drawCanvas(painter: TPainter): void {

        
        if(!this.visible || this._openRuler || !this._target_wall_edge
            || this.isWallAdsorpt === false || Math.abs(this._edit_num) < 1
            || (!this._pos0 || !this._pos1)) 
        {
            if(this._existingInput)
            {
                this._existingInput.style.display = 'none';
            }
            if(!this.is_moving)
            {
                return;
            }
        }
        if(!this._target_wall_edge) return;

        if(!this._pos0 || !this._pos1) return;

        if(this._existingInput.style.display !== "none")
        {

            let pp = this.painter.worldToScreen(this._fixed_edit_pos);

            if(this._existingInput.parentElement)
            {
                let t_rect = this._existingInput.parentElement.getBoundingClientRect();
                pp.x -= t_rect.left;
                pp.y -= t_rect.top;
            }
            this.updateInputPosition(this._existingInput, pp);
        }

        painter.strokeStyle = "#282828";
        painter.fillStyle = "#282828";
        painter._context.lineWidth = 0.5;
        painter._context.beginPath();
        painter.drawSegment(this._pos0.clone(),this._pos1.clone());
        painter._context.stroke();

        if(this.is_moving)
        {
            let dim = new ZDistanceDimension(this._pos0.clone(),this._pos1.clone());
            dim.nor.set(0,1,0);
            dim.offset_len = 0;
            dim._font_size = 2.4 / painter._p_sc;
            dim.text_offset_len = 90;
            painter.drawDimension(dim);
        }

    }
    get isWallAdsorpt() {
        return this._isWallAdsorpt;
    }

    /**
     *  默认设置是无效的: 只有在T_DimensionElement等才有效
     */
    set isWallAdsorpt(t: boolean) {
        this._isWallAdsorpt = t;
    }

}