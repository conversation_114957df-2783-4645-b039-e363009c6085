
import { Vector3 } from "three";
import { ZRect } from "z_polygon";
import { LayoutAI_App, LayoutAI_CursorState } from "../LayoutAI_App";
import { I_SelectedTarget } from "../TLayoutEntities/TEntitySelector/TEntitySelector";
import { TLayoutEntityContainer } from "../TLayoutEntities/TLayoutEntityContainter";
import { TRoomEntity } from "../TLayoutEntities/TRoomEntity";
import { TPainter } from "../TPainter/TPainter";
import { T_TransformElement } from "./T_TransformElement";


export class T_MoveSubAreaElement extends T_TransformElement {

    protected _selected_target: I_SelectedTarget;
    _container: TLayoutEntityContainer;

    constructor(_selected_target: I_SelectedTarget, container: TLayoutEntityContainer) {
        super();
        this._element_name = "TransformMoveSubAreaElement"
        this._cursor_state = LayoutAI_CursorState.Default;

        this._selected_target = _selected_target;
        this._allow_entity_types = ["SubArea"];
        this.IsMovingElement = true;

    }

    get alignment_rects(): ZRect[] {
        return this._container.getCandidateRects(["Furniture"]);
    }
    get room_entities(): TRoomEntity[] {
        return this._container._room_entities;
    }


    onselect(): void {
        LayoutAI_App.RunCommand("EditSpaceAreaSubHandler");
    }

    startTransform(adsorb_rects: ZRect[] = []) {

    }

    applyTransformByMovement(movement: Vector3, adsorb_rects: ZRect[] = []): void {

    }





    /**
     * 吸附处理
     * @param movement 
     * @param adsorb_rects 
     * @param adsorb_dist 
     * @returns 
     */
    adsorption(movement: Vector3, adsorb_rects: ZRect[] = [], adsorb_dist: number = 70, adsorb_candidate_dist: number = 200) {


    }



    drawCanvas(painter: TPainter): void {

    }
}