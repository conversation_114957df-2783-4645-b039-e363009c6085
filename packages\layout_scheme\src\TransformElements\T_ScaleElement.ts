
import { DrawingFigureMode } from "@layoutai/basic_data";
import { Vector3, Vector3Like } from "three";
import { ZEdge, ZRect } from "z_polygon";
import { LayoutAI_App, LayoutAI_Commands, LayoutAI_CursorState } from "../LayoutAI_App";
import { TBaseEntity } from "../TLayoutEntities/TBaseEntity";
import { TBaseGroupEntity } from "../TLayoutEntities/TBaseGroupEntity";
import { I_SelectedTarget } from "../TLayoutEntities/TEntitySelector/TEntitySelector";
import { TLayoutEntityContainer } from "../TLayoutEntities/TLayoutEntityContainter";
import { TRoomEntity } from "../TLayoutEntities/TRoomEntity";
import { LayoutAI_Configs } from "../TLayoutEntities/configures/LayoutAIConfigs";
import { TPainter } from "../TPainter/TPainter";
import { T_TransformElement } from "./T_TransformElement";



export class T_ScaleElement extends T_TransformElement {
    _dir_val: number;
    _nor_val: number;

    _fixed_pos: Vector3;
    _container: TLayoutEntityContainer;
    current_alignment_Edges: ZEdge[] = [];
    protected _selected_target: I_SelectedTarget;
    t_pos: Vector3;
    scale_pos0: Vector3;
    scale_pos1: Vector3;
    _currentLine: ZEdge;
    constructor(dir_val: number, nor_val: number, _selected_target: I_SelectedTarget, container: TLayoutEntityContainer) {
        super();
        this._dir_val = dir_val;
        this._nor_val = nor_val;

        this._element_name = "Scale-(" + this._dir_val + "," + this._nor_val + ")";
        this._container = container;
        this._fixed_pos = null;
        this._origin_shape_rect = null;
        this._selected_target = _selected_target;
        this.current_alignment_Edges = [];
        this.scale_pos0 = null;
        this.scale_pos1 = null;
        this._align_line = false;
        this._currentLine = null;
    }

    get room_entities(): TRoomEntity[] {
        return this._container._room_entities;
    }
    get alignment_rects(): ZRect[] {
        return this._container.getCandidateRects(["Furniture"]);
    }

    checkEditRect(pos: Vector3Like, _p_sc: number) {
        const is_moblie = LayoutAI_Configs.isMobile || false;
        if (this._target_rect.ex_prop.label === "相机" || this._target_rect.ex_prop.label === "人物") return false;
        if (is_moblie && this._selected_target.selected_entity && this._selected_target.selected_entity.checkHaveMatchRect()) return false;
        if (!is_moblie && this._container._drawing_layer_mode === 'AIMatching') return false;
        if (this.isTargetRectTypeValid()) {
            let t = this._element_rect.rect_center.distanceTo(pos) < this.radius / _p_sc;
            return t;
        } else {
            return false;
        }
    }

    updateElement(): void {
        if (!this._target_rect) return;
        this.pos = this._target_rect.unproject({ x: this._target_rect._w / 2 * this._dir_val, y: this._target_rect._h / 2 * this._nor_val });

        let dv = this.pos.clone().sub(this._target_rect.rect_center);
        dv.normalize();

        if (Math.abs(this._target_rect._nor.x) > 0.9 && Math.abs(this._target_rect._nor.y) < 0.1 || Math.abs(this._target_rect._nor.y) > 0.9 && Math.abs(this._target_rect._nor.x) < 0.1) {
            // 偏向于90度垂直鼠标样式判断
            this.vertical(dv);
        }
        else {
            // 有角度的鼠标样式判断
            this.horizontal(dv);
        }
        this.currentLine();
    }

    vertical(dv: Vector3) {
        if (Math.abs(dv.x) > 0.9 && Math.abs(dv.y) < 0.01) {
            this._cursor_state = LayoutAI_CursorState.Acrossmove;
        }
        else if (Math.abs(dv.y) > 0.9 && Math.abs(dv.x) < 0.01) {
            this._cursor_state = LayoutAI_CursorState.verticalmove;
        }
        else {
            if (dv.x > 0) {
                if (dv.y > 0) {
                    this._cursor_state = LayoutAI_CursorState.Rightmove;

                }
                else {
                    this._cursor_state = LayoutAI_CursorState.Leftmove;

                }
            }
            else {
                if (dv.y > 0) {
                    this._cursor_state = LayoutAI_CursorState.Leftmove;

                }
                else {
                    this._cursor_state = LayoutAI_CursorState.Rightmove;

                }
            }

        }
    }
    horizontal(dv: Vector3) {
        const epsilon = 0.00001; // 容差值
        let minX = Math.min(...this._target_rect.vertices.map(v => v.pos.x));
        let maxX = Math.max(...this._target_rect.vertices.map(v => v.pos.x));
        let minY = Math.min(...this._target_rect.vertices.map(v => v.pos.y));
        let maxY = Math.max(...this._target_rect.vertices.map(v => v.pos.y));

        if (Math.abs(this.pos.x - minX) < epsilon || Math.abs(this.pos.x - maxX) < epsilon) {
            this._cursor_state = LayoutAI_CursorState.Acrossmove;
            return;
        }
        else if (Math.abs(this.pos.y - minY) < epsilon || Math.abs(this.pos.y - maxY) < epsilon) {
            this._cursor_state = LayoutAI_CursorState.verticalmove;
            return;
        }
        if (dv.x > 0) {
            if (dv.y > 0) {
                this._cursor_state = LayoutAI_CursorState.Rightmove;

            }
            else {
                this._cursor_state = LayoutAI_CursorState.Leftmove;

            }
        }
        else {
            if (dv.y > 0) {
                this._cursor_state = LayoutAI_CursorState.Leftmove;

            }
            else {
                this._cursor_state = LayoutAI_CursorState.Rightmove;

            }
        }
    }
    onhover(): void {

    }

    onselect(): void {

        LayoutAI_App.RunCommand(LayoutAI_Commands.Transform_Scaling);
    }



    applyTransformByMovement(movement: Vector3, adsorb_rects: ZRect[] = []): void {
        if (!this._origin_shape_rect) return;
        if (!this._target_rect) return;
        this._align_line = true;
        this._fixed_pos = this._origin_shape_rect.unproject({ x: this._origin_shape_rect._w / 2 * -this._dir_val, y: this._origin_shape_rect._h / 2 * -this._nor_val });
        let pos = this._origin_shape_rect.unproject({ x: this._origin_shape_rect._w / 2 * this._dir_val, y: this._origin_shape_rect._h / 2 * this._nor_val });
        let dir_dv = this._origin_shape_rect.dv.clone().multiplyScalar(this._dir_val);
        let nor_dv = this._origin_shape_rect.nor.clone().multiplyScalar(this._nor_val);
        let ww_to_add = movement.dot(dir_dv);
        let hh_to_add = movement.dot(nor_dv);

        if ((this._dir_val * this._nor_val) !== 0) {
            // 计算原始的宽高比
            let aspect_ratio = this._origin_shape_rect._w / this._origin_shape_rect._h;

            // 根据原始的宽高比来调整新的宽度和高度
            if (aspect_ratio > 1) {
                hh_to_add = ww_to_add / aspect_ratio;
            } else {
                ww_to_add = hh_to_add * aspect_ratio;
            }
        }


        let ww = this._origin_shape_rect.w + ww_to_add;
        let hh = this._origin_shape_rect.h + hh_to_add;

        if (ww < 0) ww = -ww;
        if (hh < 0) hh = -hh;
        ww_to_add = ww - this._origin_shape_rect.w;
        hh_to_add = hh - this._origin_shape_rect.h;
        let t_movement = dir_dv.clone().multiplyScalar(ww_to_add).add(nor_dv.multiplyScalar(hh_to_add));
        let t_pos = pos.add(t_movement);
        let t_center = (t_pos.add(this._fixed_pos)).multiplyScalar(0.5);


        this._target_rect._w = ww;
        this._target_rect._h = hh;
        this._target_rect.rect_center = t_center;

        if (!(Math.abs(this._dir_val) === 1 && Math.abs(this._nor_val) === 1)) {
            let new_scale_pos = this._target_rect.unproject({ x: this._target_rect._w / 2 * this._dir_val, y: this._target_rect._h / 2 * this._nor_val });
            // 拉伸的吸附        
            this.alignScale(new_scale_pos);
        }

        // 组合的拉伸
        if (this._selected_target.selected_rect.ex_prop['poly_target_type'] == 'Group' || this._selected_target.selected_rect.ex_prop['poly_target_type'] == 'BaseGroup') {
            this.groupScale(ww_to_add, hh_to_add);
        }
        let entity = TBaseEntity.getEntityOfRect(this._target_rect);
        if (entity) {
            entity._scale_fixed_point = this._fixed_pos.clone();
            entity._need_update = true;
            entity.update();
        }
    }

    currentLine() {
        if (!(Math.abs(this._dir_val) === 1 && Math.abs(this._nor_val) === 1)) {
            for (let edge of this._target_rect.edges) {
                if (edge.center.distanceTo(this.pos) > 1.) continue;
                this._currentLine = edge;
            }
        }
    }

    computeAlignRect() {
        // 要重新计算对齐的Edge，只有墙和图元才能对齐
        this.current_alignment_Edges = [];
        let t_room: TRoomEntity = null;
        for (let room of this.room_entities) {
            if (room._room_poly.containsPoint(this._target_rect.rect_center)) {
                t_room = room;
                continue;
            }
        }

        if (t_room) {
            for (let rect of this.alignment_rects) {
                if (!t_room._room_poly.containsPoint(rect.rect_center)) continue;
                if (rect.ex_prop['is_deleted'] == '1') continue;
                if (rect == this._target_rect) continue;
                this.current_alignment_Edges.push(...rect.edges);
            }
            this.current_alignment_Edges.push(...t_room._room_poly.edges);
        } else {
            for (let rect of this.alignment_rects) {
                if (rect == this._target_rect) continue;
                let isInAnyRoom = false;
                for (let room of this.room_entities) {
                    if (room._room_poly.containsPoint(rect.rect_center)) {
                        isInAnyRoom = true;
                        break;
                    }
                }
                if (!isInAnyRoom) {
                    this.current_alignment_Edges.push(...rect.edges);
                }
            }
        }
        // console.log('current_alignment_Edges:',this.current_alignment_Edges);

    }


    /**
     *   拉伸的吸附功能
     */
    alignScale(new_scale_pos: Vector3) {
        let target_rect = this._target_rect;
        if (!target_rect) return;
        this.computeAlignRect();
        let scale_pos = null;
        this.scale_pos0 = null;
        this.scale_pos1 = null;
        let t_edge = null;

        for (let edge of this._target_rect.edges) {
            if (new_scale_pos.distanceTo(edge.center) < 10) {
                t_edge = edge;
            }
        }


        for (let edge of this.current_alignment_Edges) {
            let pp = edge.projectEdge2d(new_scale_pos);
            if (Math.abs(pp.y) < 80) {
                if (Math.abs(t_edge.nor.dot(edge.nor)) < 0.1) continue;
                scale_pos = edge.unprojectEdge2d({ x: pp.x, y: 0 });
                this.scale_pos0 = edge.center;
                this.scale_pos1 = scale_pos.clone();
            }
        }
        if (scale_pos) {
            let movement = scale_pos.clone().sub(new_scale_pos);
            let dir_dv = target_rect.dv.clone().multiplyScalar(this._dir_val);
            let nor_dv = target_rect.nor.clone().multiplyScalar(this._nor_val);
            let ww_to_scale = movement.dot(dir_dv);
            let hh_to_scale = movement.dot(nor_dv);
            let ww = target_rect.w + ww_to_scale;
            let hh = target_rect.h + hh_to_scale;
            target_rect._w = ww;
            target_rect._h = hh;
            let t_center = (scale_pos.add(this._fixed_pos)).multiplyScalar(0.5);
            target_rect.rect_center = t_center;
        }
    }


    groupScale(ww_to_add: number, hh_to_add: number) {

        let target_rect = this._selected_target.selected_rect;
        let entity = TBaseEntity.getEntityOfRect(target_rect) as TBaseGroupEntity;
        if (entity) {
            entity.updateGroup();
        }
    }
    drawCanvas(painter: TPainter): void {

        if (!this.visible) return;
        if (this._target_rect.ex_prop.label === "相机" || this._target_rect.ex_prop.label === "人物") return;
        const is_mobile = LayoutAI_Configs.isMobile;
        if (is_mobile && this._selected_target.selected_entity &&  this._container.drawing_figure_mode == DrawingFigureMode.Texture) return;
        if (!is_mobile && this._container._drawing_layer_mode === 'AIMatching') return;

        const p_scale_h = this._target_rect._h * painter._p_sc;
        const radius = !is_mobile ? 10 : 5;
        const lineWidth = 1; // 边框线宽
        painter.fillStyle = "#fff";
        painter.strokeStyle = "#147FFA";

        if (this.isTargetRectTypeValid() && !this.is_moving) {
            // 绘制点
            const drawPoint = () => {
                painter._context.lineWidth = 1;
                painter.drawPointCircle(this.pos, radius / painter._p_sc);
                painter.fillPointCircle(this.pos, radius / painter._p_sc);
            };

            if ((Math.abs(this._dir_val) === 1 && Math.abs(this._nor_val) === 1 && p_scale_h > 35) ||
                (this._dir_val === -1 && this._nor_val === 1 && p_scale_h < 35)) {
                drawPoint();
            }

            // 绘制椭圆
            const drawEllipseIfNeeded = () => {
                if (this._currentLine) {
                    const center = this.pos; // 椭圆中心
                    painter.drawFlatEllipse(center, !is_mobile ? 20 : 10, !is_mobile ? 12 : 6, this._currentLine._nor, lineWidth);
                }
            };

            if ((!(Math.abs(this._dir_val) === 1 && Math.abs(this._nor_val) === 1) && p_scale_h > 35) ||
                ((this._dir_val === 0 && this._nor_val === 1) || (this._dir_val === -1 && this._nor_val === 0) && p_scale_h < 35)) {
                drawEllipseIfNeeded();
            }

            painter._context.closePath();
        }
        if (!this._align_line) return;
        if (this.scale_pos0 && this.scale_pos1) {
            painter._context.lineWidth = 2;
            painter.drawLineSegment(this.scale_pos0, this.scale_pos1, '#f23dd1');
        }

    }

}