import { getImgDomain } from "@svg/request";
import { Box3, BoxGeometry, BufferGeometry, Float32BufferAttribute, Group, Matrix4, Mesh, MeshStandardMaterial, Object3D, RepeatWrapping, SRGBColorSpace, Texture, TextureLoader, Vector3 } from "three";
import { compareNames } from "z_polygon";
import { I_DesignMaterialInfo } from "@layoutai/basic_data";
import { MeshName, UserDataKey } from "./NodeName";
import { Model3dApi } from "./model3d_loader";
import { DesignMaterialService } from "./designMaterialInfo";

export type BoardCategory = "柜体" | "掩门" | "翻门" | "成品" | "抽面" | "拉手" | "五金" | "电器" | "其它" | "";
export interface I_RawGltfExtras {
    name?: string;
    displayName?: string;
    materialId?: string;
    materialMapVoId?: string;
    width?: number;
    length?: number;
    height?: number;
}
export interface I_RawGltfNode {
    nodeIndex?: number;
    name?: string;
    matrix: number[];
    mesh?: number;
    children?: number[];
    extras?: I_RawGltfExtras;
    extensions?: I_RawGltfExtras;
}
export interface I_RawGltfJson {
    scenes: { nodes: number[] }[];
    nodes: I_RawGltfNode[];
}

export class RawGltfNode extends Group {
    private _rawData: I_RawGltfNode;
    private _isSolid: boolean;

    private _solidMesh?: Object3D;
    constructor(data: I_RawGltfNode) {
        super();
        this._rawData = data || { matrix: [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1] };
        this.name = this._rawData.name || "";
        if (this._rawData.extensions) {
            if (!this._rawData.extras) {
                this._rawData.extras = { ...this._rawData.extensions };
            }
        }
        this._rawData.extras = this._rawData.extras || {};
        this.userData.extras = this._rawData.extras;

        if (this?._rawData?.extras?.displayName) {
            this._rawData.extras.name = this._rawData.extras.displayName;
        }
        this._isSolid = false;
        if (this._rawData.mesh !== undefined) {
            this.name += "mesh" + this._rawData.mesh;
        }
    }

    get materialId() {
        return this.rawData?.extras?.materialId || "";
    }

    get materialMapVoId() {
        return this.rawData?.extras?.materialMapVoId || "";
    }
    public get category(): string {
        return this.boardCategory;
    }
    public set category(value: string) {
        this.boardCategory = value as any;
    }

    get boardCategory(): BoardCategory {
        return this.userData.boradCategory || "";
    }

    set boardCategory(t: BoardCategory) {
        this.userData.boradCategory = t;
    }

    reInitMatrix() {
        let matrix = new Matrix4();
        if (this._rawData.matrix) {
            matrix.fromArray(this._rawData.matrix);
        }
        this.position.set(0, 0, 0);
        this.rotation.set(0, 0, 0);
        this.scale.set(1, 1, 1);
        this.applyMatrix4(matrix);
    }
    get rawData() {
        return this._rawData;
    }

    get childrenIds() {
        return this._rawData.children || [];
    }
    get isSolid() {
        return this._isSolid;
    }
    set isSolid(t: boolean) {
        this._isSolid = t;
    }

    get solidMesh() {
        return this._solidMesh;
    }

    set solidMesh(obj: Object3D) {
        if (this._solidMesh) {
            this._solidMesh.removeFromParent();
            this._solidMesh = null;
        }
        this._solidMesh = obj;

        if (this._solidMesh) {

            if (this.rawNodeSize) {
                this.reInitMatrix();
                let length = this.rawNodeSize.length;
                let height = this.rawNodeSize.height;
                let width = this.rawNodeSize.width;
                let bbox = new Box3();
                bbox.setFromObject(this._solidMesh);
                let b_size = bbox.getSize(new Vector3());
                this.scale.set(length / b_size.x, width / b_size.y, height / b_size.z);
            }
        }
        this.add(this._solidMesh);
    }

    get rawNodeSize() {
        if (!this._rawData.extras.length) {
            return null;
        }
        let width = this._rawData?.extras?.width || 10;
        let length = this._rawData?.extras?.length || 10;
        let height = this._rawData?.extras?.height || 10;
        return { length: length, width: width, height: height }
    }
    updateBoxMesh_IfSolid() {
        if (this.isSolid) {
            if (!this._solidMesh) {
                this._solidMesh = new Mesh(new BoxGeometry(1, 1, 1), new MeshStandardMaterial({ color: 0xe5e5e5, emissive: 0xe5e5e5, emissiveIntensity: 1., roughness: 1., metalness: 1., transparent: true }));
            }
            this.remove(...this.children);
            this._solidMesh.name = "Mesh-" + this.name;
            let rawNodeSize = this.rawNodeSize;

            if (rawNodeSize) {
                let width = rawNodeSize.width;
                let length = rawNodeSize.length;
                let height = rawNodeSize.height;
                if (this.name === "CRealFurnitureEntity") {
                    this._solidMesh.scale.set(length, width, height);
                    this._solidMesh.position.set(0, 0, height / 2);

                    this.scale.set(1, 1, 1);
                }
                else {
                    this._solidMesh.scale.set(length, width, height);
                    this._solidMesh.position.set(length / 2, -width / 2, height / 2);
                    this.scale.set(1, 1, 1);

                }
            }

            this.add(this._solidMesh);
        }
        else {
            this.remove(this._solidMesh);
            this._solidMesh = null;
        }
    }

    checkBoardCategory() {
        if (this.isSolid) {
            let iter = 4;
            let t_node = this as RawGltfNode;

            let isVisitBoardPartEntity = false;
            let isVisitSwingDoorLeafEntity = false;
            let isVisitHandlePartEntity = false;
            let isVisitCMetalPartEntity = false;
            let isVisitWhModelEntity = false;
            let isVisitDoorBoardEntityBase = false;
            let isDrawerBasket = false;
            let isDrawerDefault = false;
            while (iter-- && t_node) {
                let name = t_node.name;
                let ex_name = t_node?.rawData?.extras?.name || "---";
                if (name === "CRealFurnitureEntity") {
                    this.boardCategory = "成品";
                    return;
                }
                if (compareNames([ex_name], ["左侧板", "右侧板", "顶板", "底板", "层板", "平板护墙", "背板", "踢脚板"])) {
                    this.boardCategory = "柜体";
                    return;
                }
                if (compareNames([ex_name], ["翻门"])) {
                    this.boardCategory = "翻门";
                    return;
                }
                if (compareNames([ex_name], ["掩门"])) {
                    this.boardCategory = "掩门";
                    return;
                }
                if (compareNames([ex_name], ["平板抽面"])) {
                    this.boardCategory = "抽面";
                    return;
                }
                if (compareNames([ex_name], ["拉篮"])) {
                    isDrawerBasket = true;
                }
                if (compareNames([ex_name], ["抽屉"])) {
                    isDrawerDefault = true;
                }
                if (name === "CApplianceModelEntity") {
                    this.boardCategory = "电器";
                    return;
                }
                if (name === "CBoardPartEntity") {
                    isVisitBoardPartEntity = true;
                }
                if (name === "CSwingdoorLeafEntity") {
                    isVisitSwingDoorLeafEntity = true;
                }
                if (name === "CHandlePartEntity") {
                    isVisitHandlePartEntity = true;
                    this.boardCategory = "拉手";
                    return;
                }
                if (name === "CMetalPartEntity") {
                    isVisitCMetalPartEntity = true;
                }
                if (name === "CWhModelEntity") {
                    isVisitWhModelEntity = true;
                }
                if (name === "CDoorBoardEntityBase") {
                    isVisitDoorBoardEntityBase = true;
                }

                t_node = t_node.parent as RawGltfNode;
            }
            if (isVisitHandlePartEntity) {
                this.boardCategory = "拉手";
            }
            else if (isVisitCMetalPartEntity) {
                this.boardCategory = "五金";
            }
            else if (isVisitSwingDoorLeafEntity) {
                this.boardCategory = "掩门";
            }
            else if (isVisitWhModelEntity) {
                this.boardCategory = "成品";
            }
            else if (isVisitDoorBoardEntityBase) {
                this.boardCategory = "掩门";
            }
            else if (isVisitBoardPartEntity) {
                this.boardCategory = "柜体";
            }

            else {
                this.boardCategory = "其它";
            }


        }
        else {
            this.boardCategory = "";
        }

    }
    get bbox3() {
        return this.userData.bbox3 || null;
    }
    set bbox3(bbox: Box3) {
        this.userData.bbox3 = bbox;
    }
    public updateBox3(force: boolean = false) {
        if (!this.bbox3 || force) {
            let bbox = new Box3();
            bbox.setFromObject(this);
            this.bbox3 = bbox;
        }
        return this.bbox3;
    }
}
export class SvgGltfNode extends Group {
    /**
     *  合批类型
     */
    protected _category: string;

    protected _materialId: string;

    /**
     *  贴图素材Id
     */
    protected _materialMapVoId: string;

    protected _texture: Texture;

    protected _originMaterialVoId: string;

    protected _designMaterialInfo: I_DesignMaterialInfo;

    /**
     * 这种函数组件式设计, 让其可扩展
     * @param material_id  
     * @param url 
     * @param options 
     * @param onload 
     * @returns 
     */
    static getTexture = (material_id: string, url: string, options?: any, onload?: (t: Texture) => void) => {
        let texture: Texture = null;
        if (!texture) {
            texture = new TextureLoader().load(url, tex => {
                texture = tex;
                texture.wrapS = RepeatWrapping;
                texture.wrapT = RepeatWrapping;
                texture.colorSpace = SRGBColorSpace;
                texture.needsUpdate = true;
                texture.flipY = false;
                if (onload) {
                    onload(texture);
                }
            });
        }
        return texture;
    }

    static async getTextureAsync(material_id: string, url: string, options?: any) {
        return new Promise<Texture>((resolve, reject) => {
            SvgGltfNode.getTexture(material_id, url, options, resolve);
        })
    }
    static async updateMeshTextureWithImg(mesh: Mesh, url: string, materialId: string, options: { category?: string, cabinetStyleId?: string } = {}) {
        let texture = await SvgGltfNode.getTextureAsync(materialId, url);
        // console.log(mesh,materialId,url);
        if (mesh.material) {
            (mesh.material as MeshStandardMaterial).map = texture;
            (mesh.material as MeshStandardMaterial).emissiveMap = texture;

            (mesh.material as MeshStandardMaterial).needsUpdate = true;
        }
    }
    constructor() {
        super();
    }


    public get category(): string {
        return this._category;
    }
    public set category(value: string) {
        this._category = value;
    }

    public get boardCategory() {
        return this.category as BoardCategory;
    }

    public set boardCategory(b: BoardCategory) {
        this.category = b;
    }
    public get materialId(): string {
        return this._materialId;
    }
    public set materialId(value: string) {
        this._materialId = value;
    }
    public get materialMapVoId(): string {
        return this._materialMapVoId;
    }
    public set materialMapVoId(value: string) {
        this._materialMapVoId = value;
    }

    public get originMaterialVoId(): string {
        return this._originMaterialVoId;
    }
    public set originMaterialVoId(value: string) {
        this._originMaterialVoId = value;
    }

    public get designMaterialInfo() {
        return this._designMaterialInfo;
    }

    public set designMaterialInfo(t: I_DesignMaterialInfo) {
        this._designMaterialInfo = t;
    }

    public async loadAndUpdateSolidMesh() {

    }

    get bbox3() {
        return this.userData.bbox3 || null;
    }
    set bbox3(bbox: Box3) {
        this.userData.bbox3 = bbox;
    }
    public updateBox3(force: boolean = false) {
        if (!this.bbox3 || force) {
            let bbox = new Box3();
            bbox.setFromObject(this);
            this.bbox3 = bbox;
        }
        return this.bbox3;
    }
}

/**
 *  板件节点合批
 */
export class SvgGltfBoardBatchNode extends SvgGltfNode {


    /**
     *   原始的GltfNode, 合批后---会去掉原始的geometry
     */
    protected _srcGltfNodes: RawGltfNode[];

    /**
     *  合批后的网格
     */
    protected _batchMesh: Mesh;

    /**
     *  柜体的根节点
     */
    protected _root: SvgGltfCabinetNode;


    public readonly isSvgGltfBoardBatchNode = true;
    constructor(root: SvgGltfCabinetNode, gltfNodes: RawGltfNode[]) {
        super();
        this.name = "SvgGltfBoardBatchNode";
        this.mergeBatch(root, gltfNodes);
    }
    /**
     * 合批后，会去掉原始的geometry
     * @param root  
     * @param gltfNodes 
     */
    mergeBatch(root: SvgGltfCabinetNode, gltfNodes: RawGltfNode[]) {
        this._root = root;
        this._srcGltfNodes = gltfNodes;
        if (!gltfNodes[0]) {
            this._batchMesh = null;
        }

        let mesh_list: Mesh[] = [];
        gltfNodes.forEach((node) => {
            if (node.isSolid && node.solidMesh) {
                if ((node.solidMesh as Mesh).isMesh) {
                    mesh_list.push(node.solidMesh as Mesh);
                }
            }
        });

        mesh_list.forEach(mesh => {
            mesh.geometry.applyMatrix4(mesh.matrixWorld);
            mesh.position.set(0, 0, 0);
            mesh.rotation.set(0, 0, 0);
            mesh.scale.set(1, 1, 1);
            mesh.updateMatrix();
            mesh.removeFromParent();
        })

        this._batchMesh = this._mergeMeshList(mesh_list);

        mesh_list.forEach((mesh) => {
            // mesh.geometry.dispose();
            // (mesh.material as MeshStandardMaterial).dispose();
        });

        this._materialId = gltfNodes[0].materialId; // 作为代表元素
        this._materialMapVoId = gltfNodes[0].materialMapVoId;
        this._originMaterialVoId = this._materialMapVoId;
        this.category = gltfNodes[0].boardCategory;

        this._batchMesh.name = "Mesh-Merged-" + this.category;

        this._batchMesh.userData[UserDataKey.MaterialId] = this._materialMapVoId;
        this._batchMesh.userData[UserDataKey.FurnitureType] = MeshName.CustomModel;

        this.remove(...this.children);
        this.add(this._batchMesh);

    }

    get materialMapVoId() {
        return this._materialMapVoId;
    }
    set materialMapVoId(s: string) {
        this._materialMapVoId = s;
        if (this._batchMesh) {
            this._batchMesh.userData[UserDataKey.MaterialId] = this._materialMapVoId;
        }
    }

    protected _mergeMeshList(meshes: Mesh[]) {
        let geometries = meshes.map((mesh) => mesh.geometry);
        let position: number[] = [];
        let normal: number[] = [];
        let uv: number[] = [];
        let indices: number[] = [];
        let indexCount = 0;
        geometries.forEach((geometry) => {
            position.push(... (geometry.attributes.position as Float32BufferAttribute).toJSON().array);
            normal.push(... (geometry.attributes.normal as Float32BufferAttribute).toJSON().array);
            uv.push(...(geometry.attributes.uv as Float32BufferAttribute).toJSON().array);
            if (geometry.index) {
                let array = [...(geometry.index.toJSON()).array].map((a) => a + indexCount);
                indices.push(...array);
            }
            indexCount = position.length / 3;
        });

        let merged_geometry = new BufferGeometry();
        merged_geometry.setAttribute("position", new Float32BufferAttribute(position, 3));
        merged_geometry.setAttribute("normal", new Float32BufferAttribute(normal, 3));
        merged_geometry.setAttribute("uv", new Float32BufferAttribute(uv, 2));
        merged_geometry.setIndex(indices);

        let mesh = new Mesh(merged_geometry, meshes[0].material);
        if (meshes[0]?.material) {
            mesh.material = (meshes[0]?.material as MeshStandardMaterial).clone()
        }
        mesh.name = meshes[0].name;


        return mesh;
    }

    public async loadAndUpdateSolidMesh() {
        if (!this.materialMapVoId) return;
        if (!this.designMaterialInfo) return;

        if (this._batchMesh) {
            if (this._root) {
                this._root.updateMeshTextureWithImg(this._batchMesh, getImgDomain() + this.designMaterialInfo?.ImagePath, this.materialMapVoId);
            }
            else {
                await SvgGltfNode.updateMeshTextureWithImg(this._batchMesh, getImgDomain() + this.designMaterialInfo?.ImagePath, this.materialMapVoId);

            }
        }


    }
}

/**
 *  成品节点
 */
export class SvgGltfFurnitureNode extends SvgGltfNode {
    protected _rawGltfNode: RawGltfNode;
    protected _solidNode: Group;
    public readonly isSvgGltfFurnitureNode = true;

    constructor() {
        super();
        this.name = "SvgGltfFurnitureNode";
        this._solidNode = new Group();
        this._solidNode.name = "SvgGltfSolidNode";
        this.add(this._solidNode);

    }
    get solidMesh() {
        return this._rawGltfNode.solidMesh;
    }
    set solidMesh(obj: Object3D) {
        if (this._rawGltfNode.solidMesh !== obj) {
            this._rawGltfNode.solidMesh = obj;

            this._solidNode.scale.copy(this._rawGltfNode.scale);
            this._solidNode.add(obj);
        }
    }


    fromRawNode(rawGltfNode: RawGltfNode) {
        this._rawGltfNode = rawGltfNode;
        this._category = this._rawGltfNode.boardCategory;
        this._materialId = this._rawGltfNode.materialId;
        this._materialMapVoId = this._rawGltfNode.materialMapVoId || null;

        let matrixWorld = this._rawGltfNode.matrixWorld.clone();
        this.position.set(0, 0, 0);
        this.rotation.set(0, 0, 0);
        this.scale.set(1, 1, 1);
        this.updateMatrix();
        this.applyMatrix4(matrixWorld);

        if (this._rawGltfNode) {
            this.add(this._rawGltfNode.solidMesh);
        }
        this.userData[UserDataKey.MaterialId] = this._rawGltfNode.materialId;
        this.userData[UserDataKey.FurnitureType] = MeshName.Model;
        this.userData[UserDataKey.IsModel] = true;

        this.category = this._rawGltfNode.boardCategory;

        this.userData.extras = this._rawGltfNode.userData.extras;
        // this.add(this._rawGltfNode);
        return this;
    }
    public async loadAndUpdateSolidMesh() {
        if (!this.designMaterialInfo) return;

        let a3dSource = this.designMaterialInfo.A3dSource;
        if (a3dSource) {
            let group_node = await Model3dApi.MakeMesh3D_WithA3dSource(this.materialId, a3dSource, true, false);
            if (group_node) {
                let box3 = new Box3();
                box3.setFromObject(group_node);
                let center = box3.getCenter(new Vector3());
                // if(group_node  && !["成品","电器"].includes(this.boardCategory))
                // {

                //     let alignCenterGroup = new Group();
                //     alignCenterGroup.position.copy(center.clone().negate());
                //     alignCenterGroup.add(group_node);
                //     group_node = alignCenterGroup;

                // }
                this.solidMesh = group_node;
                this.solidMesh.name = "Model" + this._designMaterialInfo.MaterialName;


            }
        }
    }
}
export class SvgGltfCabinetNode extends Group {
    protected _root_data: I_RawGltfJson;

    protected _origin_nodeList: RawGltfNode[];
    protected _origin_solidNodes: RawGltfNode[];

    protected categoryMaterialIdsDict: { [key: string]: { materialIds: { [key: string]: number } } };

    protected _svgEntityNodes: SvgGltfNode[];

    protected _texture_dict: { [key: string]: { materialId: string, texture: Texture } };

    protected _transform_node: Group;
    constructor(root_data: I_RawGltfJson) {
        super();
        this.name = "SvgCabinet";
        this._transform_node = new Group();
        this._transform_node.name = "SvgTransformNode";
        this.add(this._transform_node);
        this.loadFromJson(root_data);
        this._texture_dict = {};
    }
    protected getTexture(material_id: string, url: string, options?: any, onload?: (t: Texture) => void) {
        let texture: Texture = null;
        if (this._texture_dict[material_id]) {
            if (onload) {
                onload(this._texture_dict[material_id].texture);
            }
            return this._texture_dict[material_id].texture;
        }
        if (!texture) {
            texture = new TextureLoader().load(url, tex => {
                texture = tex;
                texture.wrapS = RepeatWrapping;
                texture.wrapT = RepeatWrapping;
                texture.colorSpace = SRGBColorSpace;
                texture.needsUpdate = true;
                texture.flipY = false;
                if (onload) {
                    onload(texture);
                }
            });

            this._texture_dict[material_id] = { texture: texture, materialId: material_id };
        }
        return texture;
    }

    protected getTextureAsync(material_id: string, url: string, options?: any) {
        return new Promise<Texture>((resolve, reject) => {
            SvgGltfNode.getTexture(material_id, url, options, resolve);
        })
    }
    protected cleanTextures() {
        for (let key in this._texture_dict) {
            let data = this._texture_dict[key];
            data.texture.dispose();
            data.texture = null;
        }
        this._texture_dict = {};
    }
    async updateMeshTextureWithImg(mesh: Mesh, url: string, materialId: string, options: { category?: string, cabinetStyleId?: string } = {}) {
        let texture = await SvgGltfNode.getTextureAsync(materialId, url);
        if (mesh.material) {
            (mesh.material as MeshStandardMaterial).map = texture;
            (mesh.material as MeshStandardMaterial).emissiveMap = texture;

            (mesh.material as MeshStandardMaterial).needsUpdate = true;
        }
    }
    public loadFromJson(root_data: I_RawGltfJson) {
        this._svgEntityNodes = [];
        this._root_data = root_data;
        const nodeList: RawGltfNode[] = [];
        this._root_data.nodes.forEach((node, index) => {
            node.nodeIndex = index;
            nodeList.push(new RawGltfNode(node));
        });
        nodeList.forEach((node, index) => {
            let ids = node.childrenIds;
            ids.forEach((id) => {
                let childNode = nodeList[id];
                if (childNode) {
                    node.add(childNode);
                }
            });
        });
        let meshNodes = nodeList.filter((node) => {
            return node.rawData.mesh !== undefined;
        });
        nodeList.forEach((node) => node.isSolid = false);

        meshNodes.forEach(node => {
            let t_node: RawGltfNode = node.parent as RawGltfNode;
            let iter = 5;
            while (iter--) {
                if (t_node.rawData.extras?.materialId || t_node.name === "CBoardPartEntity" || t_node.name === "CDoorBoardEntityBase") {
                    t_node.isSolid = true;
                    break;
                }
                t_node = t_node.parent as RawGltfNode;
                if (!t_node) break;
            }
        });
        nodeList.forEach((node) => {
            node.reInitMatrix();
            node.updateBoxMesh_IfSolid();
            node.checkBoardCategory();
        });

        let solidNodes: RawGltfNode[] = nodeList.filter(node => node.isSolid);
        let materialDict: {
            [key: string]: {
                materialId: string, solid_nodes?: RawGltfNode[],
                group_node?: Group, designMaterialInfo?: I_DesignMaterialInfo, isMaterial?: boolean
            }
        } = {};

        let categoryMaterialIdsDict: { [key: string]: { materialIds: { [key: string]: number } } } = {};


        solidNodes.forEach((node) => {

            if (node.materialMapVoId) {
                if (!materialDict[node.materialMapVoId]) {
                    materialDict[node.materialMapVoId] = { materialId: node.materialMapVoId, solid_nodes: [], isMaterial: true, designMaterialInfo: null, group_node: null };
                }

                materialDict[node.materialMapVoId].solid_nodes.push(node);

                let boardCategory = node.boardCategory;
                if (boardCategory && boardCategory.length > 0) {
                    if (!categoryMaterialIdsDict[boardCategory]) {
                        categoryMaterialIdsDict[boardCategory] = { materialIds: {} };
                    }
                    categoryMaterialIdsDict[boardCategory].materialIds[node.materialMapVoId] = 1;
                }
            }
            else {

            }
            if (!node.materialId) return;
            if (!materialDict[node.materialId]) {
                materialDict[node.materialId] = { materialId: node.materialId, solid_nodes: [], group_node: null, designMaterialInfo: null };
            }

            materialDict[node.materialId].solid_nodes.push(node);


        });
        nodeList.splice(0, 1);
        this._origin_nodeList = nodeList;
        this._origin_solidNodes = solidNodes;

        this.categoryMaterialIdsDict = categoryMaterialIdsDict;


        this.userData.extras = this._origin_nodeList[0].rawData.extras || {};

        this._transform_node.add(this._origin_nodeList[0]);

        this.adjustPoseOfFurnitures();
    }

    /**
     *  直接微调成品的位姿
     */
    public adjustPoseOfFurnitures() {
        let extras = this.userData.extras as I_RawGltfExtras;
        let length = extras.length;
        let width = extras.width;
        let height = extras.height;
        if (!length || !width || !height) {
            console.log("No Size in Extras Found!", this);
            return;
        }
        let models = this._origin_solidNodes.filter((node) => node.boardCategory === "成品" || node.boardCategory === "电器");

        if (compareNames([this.userData.extras?.name], ["烟机吊柜"])) {

            let target_model = models.find((model) => compareNames([model.rawData.extras?.name || "---"], ["烟机"]));
            if (target_model) {
                target_model.rawData.extras.length = Math.max(length, target_model.rawData.extras.length || 0);
                target_model.updateBoxMesh_IfSolid();

                let t_length = target_model.rawData.extras.length;
                target_model.position.copy({ x: -(t_length - length) / 2, y: 0, z: 0 });
                // console.log(target_model.position,target_model.rotation,target_model.scale);
            }

        }
    }
    public mergeBatchedNodes() {
        this._svgEntityNodes = [];

        let batchNodes: { category: BoardCategory, materialMapId: string, nodes: RawGltfNode[] }[] = [];

        const needsToMerged: BoardCategory[] = ["柜体", "掩门", "翻门", "抽面", "其它", "五金"];
        this._origin_solidNodes.forEach((node) => {
            let category = node.boardCategory;
            let materialMapId = node.materialMapVoId || "";
            if (needsToMerged.includes(category)) {
                let target_batch = batchNodes.find((b_node) => b_node.category === category && b_node.materialMapId === materialMapId);
                if (!target_batch) {
                    target_batch = { category: category, materialMapId: materialMapId, nodes: [] };
                    batchNodes.push(target_batch);
                }
                target_batch.nodes.push(node);
            }
            else {
                batchNodes.push({ category: category, materialMapId: "", nodes: [node] });
            }
        });

        this._origin_nodeList[0].updateMatrixWorld(true);
        let res_list = batchNodes.map((batch) => {

            if (batch.category === "成品") {
                return new SvgGltfFurnitureNode().fromRawNode(batch.nodes[0]);

            }
            else if (batch.materialMapId) {
                return new SvgGltfBoardBatchNode(this, batch.nodes);
            }
            else {
                return new SvgGltfFurnitureNode().fromRawNode(batch.nodes[0]);
            }
        })

        this._transform_node.remove(...this._transform_node.children);
        res_list.forEach((node) => {
            this._transform_node.add(node);
        });
        this._svgEntityNodes = res_list;

        this.updateCabinetsBox();

        if (this.mainCabinetBox3) {
            this._transform_node.position.z = -this.mainCabinetBox3.min.z;
        }
    }

    public async updateDesignMaterialInfos() {
        let material_design_infos: { [key: string]: { materialId: string, info?: I_DesignMaterialInfo, nodes: SvgGltfNode[], isMaterial: boolean } } = {};
        this._svgEntityNodes.forEach(node => {
            if ((node as SvgGltfFurnitureNode).isSvgGltfFurnitureNode) {
                if (node.designMaterialInfo) {
                    if (node.designMaterialInfo.MaterialId === node.materialId) {
                        return;
                    }
                    else {
                        node.designMaterialInfo = null;
                    }
                }
                if (!node.materialId) return;
                let materialId = node.materialId;
                if (!material_design_infos[materialId]) {
                    material_design_infos[materialId] = { materialId: materialId, nodes: [], isMaterial: false, info: null };
                }
                material_design_infos[materialId].nodes.push(node);
            }
            else {
                let materialId = node.materialMapVoId;
                if (!materialId) return;
                if (!material_design_infos[materialId]) {
                    material_design_infos[materialId] = { materialId: materialId, nodes: [], isMaterial: true, info: null };
                }
                material_design_infos[materialId].nodes.push(node);

            }
        });
        let materialIds = Object.keys(material_design_infos);
        let res: I_DesignMaterialInfo[] = [];
        if (materialIds.length > 0) {
            res = await DesignMaterialService.getDesignMaterialInfoByIds(materialIds);
        }
        res.forEach((info: I_DesignMaterialInfo) => {
            let t_data = material_design_infos[info.MaterialId || "---"];
            if (!t_data) return;
            t_data.info = info;

            t_data.nodes.forEach((node) => {
                node.designMaterialInfo = info;

            })
        })
        return;
    }
    protected async _updateRawDesignMaterialInfos() {
        let material_design_infos: { [key: string]: { materialId: string, info?: I_DesignMaterialInfo, nodes: RawGltfNode[], isMaterial: boolean, group?: Group, texture?: Texture } } = {};
        this._origin_solidNodes.forEach(node => {
            if (["成品", "拉手"].includes(node.boardCategory)) {
                if (!node.materialId) return;
                let materialId = node.materialId;
                if (!material_design_infos[materialId]) {
                    material_design_infos[materialId] = { materialId: materialId, nodes: [], isMaterial: false, info: null };
                }
                material_design_infos[materialId].nodes.push(node);
            }
            else {
                let materialId = node.materialMapVoId;
                if (!materialId) return;
                if (!material_design_infos[materialId]) {
                    material_design_infos[materialId] = { materialId: materialId, nodes: [], isMaterial: true, info: null };
                }
                material_design_infos[materialId].nodes.push(node);

            }
        });
        let materialIds = Object.keys(material_design_infos);
        let res: I_DesignMaterialInfo[] = [];
        if (materialIds.length > 0) {
            res = await DesignMaterialService.getDesignMaterialInfoByIds(materialIds);
        }

        console.log(material_design_infos, res);
        let promiseList: any[] = [];
        res.forEach((info: I_DesignMaterialInfo) => {
            let t_data = material_design_infos[info.MaterialId || "---"];
            if (!t_data) return;
            t_data.info = info;
            if (t_data.isMaterial) {
                promiseList.push(this.getTextureAsync(info.MaterialId, getImgDomain() + info.ImagePath).then((texutre) => {
                    t_data.texture = texutre;
                }));
            }
            else {
                if (info.A3dSource && info.A3dSource.length > 0) {
                    promiseList.push(Model3dApi.MakeMesh3D_WithA3dSource(info.MaterialId, info.A3dSource, false, false).then((group) => {
                        t_data.group = group;
                    }));
                }
            }
        });
        await Promise.allSettled(promiseList);

        let promiseList1: any[] = [];
        res.forEach((info: I_DesignMaterialInfo) => {
            let t_data = material_design_infos[info.MaterialId || "---"];
            if (!t_data || !t_data.info) return;
            if (t_data.isMaterial) {
                t_data.nodes.forEach((node) => {
                    if (node.solidMesh) {
                        promiseList1.push(this.updateMeshTextureWithImg(node.solidMesh as Mesh, getImgDomain() + t_data.info.ImagePath, t_data.info.MaterialId));
                    }
                });
            } else {
                t_data.nodes.forEach((node) => {
                    if (t_data.group) {
                        node.solidMesh = t_data.group.clone(true);
                    }
                })
            }
        });

        await Promise.allSettled(promiseList1);

    }
    public async updateSolidModels() {
        if (this._svgEntityNodes && this._svgEntityNodes.length > 0) {
            await this.updateDesignMaterialInfos();

            let promiseList: any[] = [];
            this._svgEntityNodes.forEach((node) => {
                promiseList.push(node.loadAndUpdateSolidMesh());
            })
            await Promise.allSettled(promiseList);
        }
        else {
            await this._updateRawDesignMaterialInfos();
        }

    }

    public get mainCabinetBox3() {
        return this.userData.mainCabinetBox3 || null;
    }

    public set mainCabinetBox3(box: Box3) {
        this.userData.mainCabinetBox3 = box;
    }
    /**
     *  计算并更新柜体box
     */
    public updateCabinetsBox() {
        let bbox = new Box3();
        this.traverseVisible((obj) => {
            let gltfNode = (obj as SvgGltfNode);
            let category = (obj as SvgGltfNode).category;
            if (["柜体", "掩门", "抽面", "翻门"].includes(category)) {
                gltfNode.updateBox3();
                bbox.union(gltfNode.bbox3);
            }
        });
        this.mainCabinetBox3 = bbox;
    }


}