import { Box3, Group, Material, Matrix4, <PERSON>sh, MeshStandardMaterial, Object3D, Vector3 } from "three";
import { GenDateUUid, Sleep, ZRect, compareNames } from "z_polygon";
import { GlbCabinetLoader } from "./parsers/GlbCabinetLoader";
import { GLTFExporter } from "three/examples/jsm/Addons.js";
import { I_LayoutAI_MsgData, LayoutAI_MsgServer } from "@layoutai/msg_center";
import { Model3dApiConfigs } from "./model3d_api_config";
import { IRequestConfig, createOpenRequest, getImgDomain } from "@svg/request";
import { DesignMaterialService } from "./designMaterialInfo";
import { I_DesignMaterialInfo } from "@layoutai/basic_data";
import { get_swj_xml_from_url } from "./xml/xml_utils";
import { DesignXmlParser } from "./xml/DesignXmlParser";
import { DOMParserImpl } from "xmldom-ts";
import { generateUUID } from "three/src/math/MathUtils.js";
import { AxiosInstance } from "axios";
import { Cabinet3DApi} from "./CabinetApi";
import { I_SwjCabinetData } from "@layoutai/basic_data";
import { Model3dSimplifier } from "./Model3dSimplifier";
import {SimpleGlbParser} from "./parsers/SimpleGlbParser";
import {SimpleSVJParser} from "./parsers/SimpleSVJParser";
import {UserDataKey} from "./NodeName"

type MaterialInfoOptions = {
    target_rect?: ZRect, category?: string,
    target_size?: { length: number, width: number, height: number },
    cabinet_board_texture_img?: HTMLImageElement,
    onLoadEnd?: (node3d: Group) => void,
    isWhite?: boolean,
    isGlb ?: boolean,
    isSubModel?: boolean, // 是否是子模型，子模型不添加代理box，只有最外层的模型添加代理box
    accurateGlbUrl?: String,
    similarGlbUrl?: String
    design_material_info ?: I_DesignMaterialInfo;
}

type I_SdkMsgType = "Command" | "Event" | "Async" | "Connected";

export interface I_Model3d_ExFunc
{
    /**
     *  函数名称
     */
    name : string;
    
    /**
     *  注释
     */
    comment ?: string;
    /**
     *  类型
     */
    type : I_SdkMsgType;

    async_func ?: (...args:any)=>Promise<any>;
}
export async function ExportGroupAsBuffer(group_node:Group)
{
    if(!group_node) return null;
    
    let gltf_exporter = new GLTFExporter();
    let buffer : ArrayBuffer= await gltf_exporter.parseAsync(group_node,{trs:true,binary:true,includeCustomExtensions:true}) as ArrayBuffer;
    let array = new Uint8Array(buffer);
    return array;
}
export const Model3dExFuncs :  {
    setHeaders : I_Model3d_ExFunc,
    MakeMesh3D_WithContentUrl3 : I_Model3d_ExFunc,
    MakeMesh3D_WithA3dSource : I_Model3d_ExFunc,
    MakeMesh3D_WithMaterialId : I_Model3d_ExFunc
    [key:string]:I_Model3d_ExFunc} = {
    setHeaders : {
        name : "setHeaders",
        comment : "",
        type : "Async",
        async_func : async (input : {[key:string]:string}):Promise<void>=>{
            Model3dApiConfigs.headers = {...Model3dApiConfigs.headers,...input}
            console.log(Model3dApiConfigs.headers);
            return;
        }
    },
    MakeMesh3D_WithContentUrl3 : {
        name : "MakeMesh3D_WithContentUrl3",
        comment :"",
        type : "Async",
        async_func:async (input: { materialId: string, accurateGlbUrl: String, similarGlbUrl: String, isWhite?: boolean,design_material_info?:I_DesignMaterialInfo})=>{
            let materialId = input.materialId || "";
            let accurateGlbUrl = input.accurateGlbUrl || null;
            let similarGlbUrl = input.similarGlbUrl || null;
            let isWhite = input.isWhite || false;

            let group_node = await Model3dApi.MakeMesh3D_WithContentUrl3(materialId,accurateGlbUrl,similarGlbUrl,isWhite);
            
            group_node && Model3dSimplifier.instance.simplifyCabinet(group_node);
           
            return await ExportGroupAsBuffer(group_node);
        }
    },
    MakeMesh3D_WithA3dSource : {
        name : "MakeMesh3D_WithA3dSource",
        comment : "",
        type :"Async",
        async_func : async (input :{materialId: string, A3dSource: string, isGlb: boolean, isWhite?: boolean})=>{
            let materialId = input.materialId || "";
            let A3dSource = input.A3dSource || null;
            let isGlb = input.isGlb || null;
            let isWhite = input.isWhite || false;
            let group_node = await Model3dApi.MakeMesh3D_WithA3dSource(materialId,materialId,isGlb,isWhite);
            return await ExportGroupAsBuffer(group_node);


        }
    },
    MakeMesh3D_WithMaterialId : {
        name : "MakeMesh3D_WithMaterialId",
        comment :"",
        type : "Async",
        async_func : async (input : {materialId : string,options?:MaterialInfoOptions})=>{
            let group_node = await Model3dApi.MakeMesh3D_WithMaterialId(input.materialId,input.options||{});
            return await ExportGroupAsBuffer(group_node);
        }
    }
}

export class Model3dApi {
    // 控制是否使用 glb 模型的定制柜
    public static isGlbCabinet = true;
    // 控制是否使用预存柜体
    public static isPreSaveCabinet = true;

    private static _instance : Model3dApi = null;

    private _ex_funcs :  {[key:string]:I_Model3d_ExFunc} = {};

    private _msg_queue : LayoutAI_MsgServer;
    private constructor()
    {
        this.initMsgQueue();
    }

    public initMsgQueue()
    {
        if(!this._msg_queue)
        {
            this._msg_queue = new LayoutAI_MsgServer();
        }
        for(let key in Model3dExFuncs)
        {
            let data = Model3dExFuncs[key];
            this._msg_queue.addAsyncProcessor(key,data.async_func);
        }
    }
    public static get instance()
    {
        if(!Model3dApi._instance)
        {
            Model3dApi._instance = new Model3dApi();
        }
        return Model3dApi._instance;
    }
    get msg_queue()
    {
        return this._msg_queue;
    }
    
    static setGroupWhiteStandardMaterial(group: Group) {
        group.traverse((object) => {
            if ((object as Mesh).isMesh) {
                let mesh = object as Mesh;
                if (Array.isArray(mesh.material)) {
                    mesh.material.forEach((material) => {
                        if (material instanceof MeshStandardMaterial) {
                            Model3dApi.setWhiteStandardMaterial(material);
                        }
                    });
                }
                else {
                    if (mesh.material instanceof MeshStandardMaterial) {
                        Model3dApi.setWhiteStandardMaterial(mesh.material);
                    }
                }
            }
        });
    }

    static setWhiteStandardMaterial(material: MeshStandardMaterial) {
        material.map = null;
        material.color.setRGB(1, 1, 1);
        material.emissive.setRGB(1, 1, 1);
        material.emissiveMap = null;
        material.needsUpdate = true;
    }
    static async MakeMesh3D_WithMaterialId(materialId:string, options: MaterialInfoOptions = {}) : Promise<Group>{



        if(options.similarGlbUrl || options.accurateGlbUrl)
        {
            let group_node = await Model3dApi.MakeMesh3D_WithContentUrl3(materialId,options.accurateGlbUrl,options.similarGlbUrl,options.isWhite||false);
            group_node && Model3dSimplifier.instance.simplifyCabinet(group_node);
            return group_node;

        }

        let design_material_info = options.design_material_info || (await DesignMaterialService.getDesignMaterialInfoByIds([materialId]))[0];

        if(!design_material_info) return null;
        if (design_material_info.A3dSource) {
            let isGlb =options.isGlb || false;  // !!design_material_info.isGlb;
            let group_node = await Model3dApi.MakeMesh3D_WithA3dSource(
                design_material_info.MaterialId,
                design_material_info.A3dSource,
                isGlb,
                options.isWhite || false,
            );
            return group_node || null;

        }
        else if (design_material_info.ContentUrl) {
            // 控制是否使用 glb 模型的定制柜
            let group_node= await Model3dApi.MakeMesh3D_WithContentUrl(
                design_material_info.MaterialId,
                design_material_info.ContentUrl,
                options.category, null,
                options.isWhite || false);
            return group_node;
        }

        return null;
    }

    static async MakeMesh3D_WithContentUrl3(materialId: string, accurateGlbUrl: String, similarGlbUrl: String, isWhite: boolean): Promise<Group> {
        let group = await GlbCabinetLoader.requestGLB(materialId, accurateGlbUrl, similarGlbUrl);
        if (group) {
            if (isWhite) {
                Model3dApi.setGroupWhiteStandardMaterial(group);
            }
        }
        return group;
    }
    static async MakeMesh3D_WithA3dSource(materialId: string, A3dSource: string, isGlb: boolean, isWhite: boolean) {
        if (A3dSource) {
            if (A3dSource.includes(".svj") || A3dSource.includes(".a3d") || A3dSource.includes(".A3D")) {

                let group_node : Group = null;
                let wholeUrl = A3dSource + `&id=${materialId}`;
                if(!wholeUrl.startsWith("http"))
                {
                    wholeUrl = getImgDomain()+"/" + wholeUrl;
                }
                if(isGlb)
                {
                    let glbUrl = A3dSource.replace(/.svj|.a3d|.A3D/, ".glb");
                    if(!glbUrl.startsWith("http"))
                    {
                        glbUrl = glbUrl.replace("//","/");
                        glbUrl = "https://3vj-designmaterial.3vjia.com" +glbUrl;
                    }
                    let res = await fetch(glbUrl,{method:"HEAD"}).then(val=>val.ok).catch(e=>false);
                    if(!res)
                    {
                        isGlb = false;
                    }
                    wholeUrl = glbUrl;
                }
                if (isGlb) {
                    group_node = await SimpleGlbParser.loadGLBParser(wholeUrl, isWhite);
                }
                else {
                    group_node = await SimpleSVJParser.loadSVJParser(wholeUrl, isWhite);
                }
                return group_node;
            }
        }
        return null;
    }
    
    static async MakeMesh3D_WithContentUrl(materialId: string, contentUrl: string, category: string, onLoadEnd: (groupNode: Group) => void, isWhite: boolean) {
        let xml_data = await get_swj_xml_from_url(getImgDomain() + contentUrl);
        if (!xml_data) return null;
        const parser = new DOMParserImpl();
        let id = xml_data.indexOf("<");
        xml_data = xml_data.substring(id);
        let doc = parser.parseFromString(xml_data, "application/xml");
        let cabinet_data = (DesignXmlParser.instance).parseCabinet(doc.childNodes[0] as any);
        let node3d: Group = null;
        if (cabinet_data._is_parts_cabinet === true) {
            node3d = await Cabinet3DApi.computeMesh3DFromSwjCupboardPart(cabinet_data, null, category, isWhite);
        }
        else {
            node3d = await Cabinet3DApi.computeMesh3DFromSwjCabinet(cabinet_data, null, 0, 2, isWhite);

            let sub_meshes: Mesh[] = [];
            const visit_node = (node: Object3D, parent_matrix: Matrix4 = null) => {
                if (node.userData.type && node.userData.type.includes("Furniture") && node.type === "Mesh") {
                    sub_meshes.push(node as Mesh);
                }

                if (node.type === "Mesh") {
                    if (node.userData.type && !node.userData.type.includes("swingDoor")) return;
                    sub_meshes.push(node as Mesh);
                }
                if (node.children) {
                    node.children.forEach(ele => {
                        visit_node(ele);
                    })
                }
            }
            visit_node(node3d);

            const visit_door_data = (data: I_SwjCabinetData, leaf_list: I_SwjCabinetData[], parent_matrix: Matrix4 = null) => {
                if (data.material_id && !data._children) {
                    data._parent_matrix = parent_matrix;
                    leaf_list.push(data);
                }
                if (data._children) {
                    let matrix = Cabinet3DApi.computeCabinetBoardMatrix(data, parent_matrix);
                    data._children.forEach((ele) => visit_door_data(ele, leaf_list, matrix));
                }
            }

            let replacing_solid_list: { target_mesh: Mesh, sub_data: I_SwjCabinetData }[] = [];
            sub_meshes.forEach((door_mesh) => {
                let cabinet_data = door_mesh.userData.cabinet_data;
                if (!cabinet_data) return;
                let sub_data_list: I_SwjCabinetData[] = [];
                visit_door_data(cabinet_data, sub_data_list);
                sub_data_list.forEach(async (sub_data) => {
                    // if(sub_data.type as any=== "door_board") return;
                    if (sub_data.type as any === "buckle") return;
                    replacing_solid_list.push({ target_mesh: door_mesh, sub_data: sub_data });
                });
            });
            let done_replacing_list: { target_mesh: Mesh, sub_data: I_SwjCabinetData }[] = [];
            const DoneReplacing = (data: any) => {
                done_replacing_list.push(data);
                if (done_replacing_list.length == replacing_solid_list.length) {
                    if (onLoadEnd) {
                        onLoadEnd(node3d);
                    }
                }
            }

            replacing_solid_list.forEach(async (data) => {
                let data_node = await Cabinet3DApi.computeMesh3DFromSwjCabinet(data.sub_data, data.sub_data._parent_matrix, 0, 0, isWhite);
                if (data_node && data_node.children && data_node.children.length > 0) {
                    data.target_mesh.add(data_node);
                    data.target_mesh.scale.set(1, 1, 1);
                }
                DoneReplacing(data);
            });
        }

        return node3d;
    }


}



if(typeof window === 'undefined') // 可能是worker
{
    Model3dApiConfigs.IsWorker = true;
    if(self !== undefined)
    {
        const OpenRequestQueue:{msgData:I_LayoutAI_MsgData,cb:(input?:any)=>void}[] = [];
        let msg_queue = Model3dApi.instance.msg_queue;
        msg_queue.postMessage = (data)=>{
            (self as any as Worker).postMessage(data);
        }
        self.addEventListener("message",async (event)=>{
            if(event.data.msgType === "Connected")
            {
                console.log("已链接")
            }
            else if(event.data.msgType === "Async")
            {
                if(event.data.headers) // 如果存在headers
                {
                    await Model3dExFuncs.setHeaders.async_func(event.data.headers);
                }
                msg_queue.processAsyncMsg(event.data);                
            }
            else if(event.data.msgType === "OpenRequest")
            {
               let t_id = OpenRequestQueue.findIndex((data)=>data.msgData.msgId == event.data.msgId);
               if(t_id >= 0)
               {
                    OpenRequestQueue[t_id].cb(event.data.asyncResult);
                    OpenRequestQueue.splice(t_id,1);
               }
            }
        });

        Model3dApiConfigs.openRequest = async (config:{
                method: 'post'|'get',
                url: string,
                data: any,
                timeout?: number})=>{
            let msgData : I_LayoutAI_MsgData = {
                msgId : generateUUID(),
                msgType : "OpenRequest",
                eventData : {
                    ...config
                }
            };
            let promise = new Promise<any>((resolve,reject)=>{
                OpenRequestQueue.push({msgData:msgData,cb:resolve});
                self.postMessage(msgData);
                Sleep(3000).then(e=>reject(null));
            });
            return await promise;

 
        }
        // console.log("载入Model3dApi---worker!");
    }
}
else{
    let openRequest = createOpenRequest(
        {
            env:"prod",
            sysCode : Model3dApiConfigs.APP_ID,
            headers : Model3dApiConfigs.headers
        }
    );
    (openRequest as AxiosInstance).defaults.withCredentials = true;
    Model3dApiConfigs.openRequest = openRequest;
}