import { I_DesignMaterialInfo, I_FigureElement, I_KgMaterialItem, I_MaterialMatchingItem, StyleBrush, isGroupMaterialMatchingItem } from "@layoutai/basic_data";
import { BasicRequest,RequestHosts,createProRequest} from "@layoutai/basic_request";
import { Vec3toMeta, compareNames, isNumericString } from "z_polygon";

    /**
     * 尺寸
     */
    type Size = {
        /** 长度 */
        length: number;
        /** 宽度 */
        width: number;
        /** 高度 */
        height: number;
    };
    /**
     * 线条
     */
    type LineEdge = {
        /** 线起点 */
        start: Coordinate;
        /** 线终点 */
        end: Coordinate;
    };
    /**
     * 坐标
     */
    type Coordinate = {
        /** X轴坐标 */
        x: number;
        /** Y轴坐标 */
        y: number;
        /** Z轴坐标 */
        z?: number;
    };
    /**
     * 成品素材
     */
    export type ProductModel = {
        /** 素材ID */
        materialId: string;
        /** 素材尺寸 */
        size: Size;
        /** 旋转坐标 */
        rotation: Coordinate;
        /** 位置坐标 */
        position: Coordinate;
        /** Model Flag */
        modelFlag?: string;
        /** 素材类型(模型位或公共分类) */
        type?: string;
        /** 是否需要做镜像处理 */
        mirror?: boolean;
    };
    /**
     * 定制柜素材
     */
    export type CustomCabinetModel = {
        /** 素材ID */
        materialId: string;
        /** 素材尺寸 */
        size: Size;
        /** 旋转坐标 */
        rotation: Coordinate;
        /** 位置坐标 */
        position: Coordinate;
        /** Model Flag */
        modelFlag?: string;
        /** 素材类型(模型位或公共分类) */
        type?: string;
        /** 素材xml地址 */
        xmlUrl?: string;
        /** 左进深 */
        leftDepth?: number;
        /** 右进深 */
        rightDepth?: number;
        /** 定制柜风格ID */
        cabinetStyleId?: string;
    };
    /**
     * 厨房素材
     */
    export type KitchenModels = {
        /** 定制柜素材列表 */
        customs: Array<CustomCabinetModel>;
        /** 定制柜素材列表 */
        products: Array<CustomCabinetModel>;
        /** 是否生成台面 */
        generateTable: boolean;
        /** 台面素材ID */
        tableMaterialId: string;
        /** 前挡水素材ID */
        frontSectionId: string;
        /** 后挡水素材ID */
        backSectionId: string;
    };
    /**
     * 卫生间素材
     */
    type BathRoomModels = {
        /** 定制柜素材列表 */
        customs: Array<CustomCabinetModel>;
        /** 素材所在房间ID */
        roomId: string;
        /** 是否生成台面 */
        generateTable: boolean;
        /** 台面素材ID */
        tableMaterialId: string;
        /** 前挡水素材ID */
        frontSectionId: string;
        /** 后挡水素材ID */
        backSectionId: string;
    };
    /**
     * 贴图素材
     */
    type PaintModel = {
        /** 素材所在房间的UID */
        roomUid: string;
        /** 素材所在房间ID */
        roomId: string;
        /** 素材ID */
        materialId: string;
        /** 墙的UID */
        wallUid: number;
    };
    /**
     * 瓷砖素材
     */
    type TileModel = {
        /** 素材所在房间ID */
        roomId: string;
        /** 铺贴目标是墙面还是地面(:墙面，0:地面) */
        floorWall: number;
        /** 瓷砖素材ID */
        brickMaterialId: string;
        /** 瓷砖旋转角度 */
        brickRotation: number;
        /** 瓷砖宽度 */
        brickWidth: number;
        /** 瓷砖高度 */
        brickHeight: number;
        /** 铺贴方案ID */
        planId?: string;
        /** 铺贴类型 */
        cls: number;
        /** 地板实体的UID */
        floorUid?: string;
        /** 锚点坐标 */
        anchorPos?: Coordinate;
        /** 墙实体的UID */
        wallUid?: string;
    };
    /**
     * 吊顶素材
     */
    type CeilingModel = {
        /** 素材ID */
        materialId: string;
        /** 素材所在房间的ID */
        roomId: string;
        /** 吊顶区域轮廓坐标 */
        areaOutline: string;
    };
    /**
     * 地脚线素材
     */
    type FootLineModel = {
        /** 素材ID */
        materialId: string;
        /** 素材所在房间的UID */
        roomUid: string;
        /** 参数ID */
        paramsId: string;
    };
    /**
     * 门槛石素材
     */
    type DoorSillModel = {
        /** 门的UID */
        doorUid: string;
        /** 素材ID */
        materialId: string;
    };
    /**
     * 房间
     */
    type Room = {
        /** 房间名称 */
        name?: string;
        /** 房间UID */
        uid: number;
        /** 房间面积 */
        area?: number;
        /** 房间的边界线段列表 */
        boundary?: Array<LineEdge>;
        /** 房间包含的所有内墙 */
        inner_wall_list?: Array<Wall>;
        /** 房间包含的所有中线墙 */
        wall_list?: Array<Wall>;
        /** 房间包含的窗 */
        window_list?: Array<Window>;
        /** 房间包含的门 */
        door_list?: Array<Door>;
        /** 房间包含的家具素材 */
        furniture_list?: Array<Furniture>;
        /** 房间包含的家具组合 */
        furniture_group_list?: Array<FurnitureGroup>;
        /** 房间包含的定制柜 */
        cabinet_list?: Array<Furniture>;
    };
    /**
     * 家具素材信息
     */
    type Furniture = {
        /** uid */
        uid: number;
        /** room_ind */
        room_ind: number;
        /** X坐标值 */
        pos_x: number;
        /** Y坐标值 */
        pos_y: number;
        /** Z坐标值 */
        pos_z: number;
        /** 长度 */
        length: number;
        /** 宽度 */
        width: number;
        /** 高度 */
        height: number;
        /** 旋转Z坐标值 */
        rotate_z: number;
        /** 名称 */
        name?: string;
        /** 素材ID */
        material_id: string;
    };
    /**
     * 家具组合
     */
    type FurnitureGroup = {
        /** uid */
        uid: number;
        /** room_ind */
        room_ind: number;
        /** X坐标值 */
        pos_x: number;
        /** Y坐标值 */
        pos_y: number;
        /** Z坐标值 */
        pos_z: number;
        /** 长度 */
        length: number;
        /** 宽度 */
        width: number;
        /** 高度 */
        height: number;
        /** 旋转Z坐标值 */
        rotate_z: number;
        /** 名称 */
        name?: string;
        /** 素材ID */
        material_id: string;
        /** 家具素材列表 */
        furniture_list: Array<Furniture>;
        /** 家具组合列表 */
        sub_group_list?: Array<FurnitureGroup>;
    };
    /**
     * 墙
     */
    type Wall = {
        /** uid */
        uid: number;
        /** 墙的边界线段列表 */
        boundary?: Array<LineEdge>;
        /** 起始点X坐标值 */
        start_x: number;
        /** 起始点Y坐标值 */
        start_y: number;
        /** 结束点X坐标值 */
        end_x: number;
        /** 结束点Y坐标值 */
        end_y: number;
        /** 墙的厚度 */
        thick: number;
    };
    /**
     * 窗
     */
    type Window = {
        /** 窗的uid */
        uid: number;
        /** 窗所在墙的id */
        wall_uid?: number;
        /** 窗位置的X坐标值 */
        pos_x: number;
        /** 窗位置的Y坐标值 */
        pos_y: number;
        /** 窗位置的Z坐标值 */
        pos_z: number;
        /** 窗的长度 */
        length: number;
        /** 窗的宽度 */
        width: number;
        /** 旋转Z坐标值 */
        rotate_z: number;
        /** 窗的素材id */
        material_id: string;
    };
    /**
     * 门
     */
    type Door = {
        /** 窗的uid */
        uid: number;
        /** 窗所在墙的id */
        wall_uid?: number;
        /** 窗位置的X坐标值 */
        pos_x: number;
        /** 窗位置的Y坐标值 */
        pos_y: number;
        /** 窗位置的Z坐标值 */
        pos_z: number;
        /** 窗的长度 */
        length: number;
        /** 窗的宽度 */
        width: number;
        /** 旋转Z坐标值 */
        rotate_z: number;
        /** 窗的素材id */
        material_id: string;
    };
    /**
     * 3D方案的房型结构和素材的数据
     */
    type House = {
        /** 房间列表 */
        room_list: Array<Room>;
        /** 所有墙的信息 */
        wall_list: Array<Wall>;
        /** 所有内墙的信息 */
        inner_wall_list: Array<Wall>;
        /** 所有内墙的信息 */
        window_list?: Array<Window>;
        /** 所有窗的列表 */
        door_list?: Array<Door>;
        /** 所有窗的列表 */
        furniture_list?: Array<ProductModel>;
        /** 所有窗的列表 */
        furniture_group_list?: Door;
        /** 所有窗的列表 */
        cabinet_list?: Door;
    };

export interface I_MaterialInfo {
    id: string;
    publicCategoryId: string;
    publicCategoryName: string;
    shape: string;
    name: string;
}

export interface I_MaterialSearchInfo {
    traceId: string;
    modelLocation: string;
    materialId: string;
    materialName: string;
    imageUrl: string;
    materialSource: string;
    size: any;
    tenantId?: string;
    num?: number;
}

export interface I_MaterialSearchResult {
    status: string;
    msg: string;
    data: any
}

export interface I_GlobalSearchInfo {
    name: string;
    index: number;
    size: number;
}


const customModelFlags = new Set(["10", "11", "12", "13", "22", "23"]);

export function isCustomCabinet(material: I_MaterialMatchingItem): boolean {
    if (material == null) {
        return false;
    }
    let modelFlag: string = material["modelFlag"];
    if (customModelFlags.has(modelFlag)) {
        return true;
    } else {
        return false;
    }
}

function cloneMaterialMatchingItem(mm: I_MaterialMatchingItem): I_MaterialMatchingItem {
    return {
        modelLoc: mm.modelLoc,
        length: mm.length,
        width: mm.width,
        height: mm.height,
        modelId: mm.modelId,
        imageUrl: mm.imageUrl,
        originalLength: mm.originalLength,
        originalWidth: mm.originalWidth,
        originalHeight: mm.originalHeight,
        targetSize: { width: mm.width, length: mm.length, height: mm.height },
        targetRotation: { x: 0, y: 0, z: mm.targetRotation.z },
        targetPosition: { x: mm.targetPosition.x, y: mm.targetPosition.y, z: mm.targetPosition.z },
        topViewImage: mm.topViewImage != null ? mm.topViewImage : MaterialService.materialTopViewImageMap.get(mm.modelId)
    } as I_MaterialMatchingItem;
}

export function toStringForMaterialMatchingItem(item: I_MaterialMatchingItem): string {
    if (!item) return "null";

    let targetPostLog = "";
    let targetSize = "";
    if (item.targetPosition && item.figureElement != null) {
        if (item.targetPosition.x != item.figureElement.rect.rect_center.x
            || item.targetPosition.y != item.figureElement.rect.rect_center.y
            || item.targetPosition.z != item.figureElement.rect.rect_center.z) {
            let deltaX = item.targetPosition.x - item.figureElement.rect.rect_center.x;
            let deltaY = item.targetPosition.y - item.figureElement.rect.rect_center.y;
            let deltaZ = item.targetPosition.z - item.figureElement.rect.rect_center.z;
            let deltaXLog = deltaX != 0 ? ("[" + Number(deltaX.toFixed(2)) + "]") : "";
            let deltaYLog = deltaY != 0 ? ("[" + Number(deltaY.toFixed(2)) + "]") : "";
            let deltaZLog = deltaZ != 0 ? ("[" + Number(deltaZ.toFixed(2)) + "]") : "";
            targetPostLog = ",Pos=(" + Number(item.targetPosition.x.toFixed(2)) + deltaXLog
                + "," + Number(item.targetPosition.y.toFixed(2)) + deltaYLog
                + "," + Number(item.targetPosition.z.toFixed(2)) + deltaZLog + ")";
        }
    }
    if (item.targetSize && item.figureElement != null) {
        if (item.targetSize.length != item.length ||
            item.targetSize.width != item.width ||
            item.targetSize.height != item.height) {
            let deltaL = item.targetSize.height - item.figureElement.rect.w;
            let deltaW = item.targetSize.width - item.figureElement.rect.h;
            let deltaH = item.targetSize.height - item.figureElement.params.height;
            let deltaLLog = deltaL != 0 ? ("[" + Number(deltaL.toFixed(2)) + "]") : "";
            let deltaWLog = deltaW != 0 ? ("[" + Number(deltaW.toFixed(2)) + "]") : "";
            let deltaHLog = deltaH != 0 ? ("[" + Number(deltaH.toFixed(2)) + "]") : "";
            targetSize = ",Size=(" + Number(item.targetSize.length.toFixed(2)) + deltaLLog
                + "," + Number(item.targetSize.width.toFixed(2)) + deltaWLog
                + (item.targetSize.height ? "," + Number(item.targetSize.height.toFixed(2)) + deltaHLog : "") + ")";
        }
    }

    return "MaterialMatchingItem  " + item.modelLoc
        + ":" + item.modelId
        + (item.name ? ("," + item.name) : "")
        + ",l=" + Number(item.length.toFixed(2))
        + ",w=" + Number(item.width.toFixed(2))
        + ",h=" + Number(item.height.toFixed(2))
        + (item.roomUid ? (",roomUid=" + item.roomUid) : "")
        + (item.ruleId ? (",ruleId=" + item.ruleId) : "")
        + (item.seriesKgId ? (",seriesKgId=" + item.seriesKgId) : "")
        + (item.seedSchemeId ? (",seedSchemeId=" + item.seedSchemeId) : "")
        + (item.type ? (",type=" + item.type) : "")
        + (item.color ? (",color=" + item.color) : "")
        + (item.shape ? (",shape=" + item.shape) : "")
        + targetPostLog
        + targetSize
        + (item.publicCategoryName ? (",publicCategory=" + item.publicCategoryName) : "")
        + (item.closeDirection ? (",closeDirection=" + item.closeDirection) : "")
        + (item.offLandRule ? (",offLandRule=" + item.offLandRule) : "")
        + (item.modelFlag ? (",modelFlag=" + item.modelFlag + (isCustomCabinet(item) ? "(定制柜)" : "")) : "")
        + (item.organizationId ? ",organizationId=" + item.organizationId : "");
};

export function toProductModel(mm: I_MaterialMatchingItem): ProductModel {
    let product: ProductModel = {
        materialId: mm.modelId,
        modelFlag: mm["modelFlag"],
        type: mm["modelLoc"],
        position: mm.targetPosition,
        size: mm.targetSize,
        rotation: mm.targetRotation,
        mirror: ((mm.figureElement)?.rect?.u_dv_flag || 1) > 0 ? false : true
    };
    (product as any).seriesId = mm.seriesId;
    return product;
}

export function toCustomCabinetModel(mm: I_MaterialMatchingItem, roomUid: string, styleBrushId: string): CustomCabinetModel {
    let customCabinetModel: CustomCabinetModel = {
        materialId: mm.modelId,
        size: mm.targetSize,
        rotation: mm.targetRotation,
        position: mm.targetPosition,
        type: mm.modelLoc,
        modelFlag: mm.modelFlag,
        leftDepth: 0,
        rightDepth: 0,
        name: mm.modelLoc,
        roomUid: roomUid || mm.roomUid,
        cabinetStyleId: styleBrushId
    } as CustomCabinetModel;
    (customCabinetModel as any).serialId = mm.seriesId;
    (customCabinetModel as any)["applyStyleState"] = 1;
    return customCabinetModel;
}

export function getStyleBrushIdBySeriesKgId(nSeriesKgId: number) {
    let seriesKgId: string = nSeriesKgId.toString();
    let styleBrushId = null;
    if (seriesKgId) {
        styleBrushId = MaterialService.cabinetStyleIdMap.get(seriesKgId);
        if (styleBrushId) {
            if (!isNumericString(styleBrushId)) {
                styleBrushId = null;
            }
        }
    }
    return styleBrushId;
}

export class MaterialService {
    static cabinetStyleIdMap: Map<string, string> = new Map<string, string>();
    static cabinetStyleTextureMap: Map<string, string> = new Map<string, string>();
    static groupMaterialDetailMap: Map<String, I_MaterialMatchingItem[]> = new Map<string, I_MaterialMatchingItem[]>();
    static materialTopViewImageMap: Map<string, string> = new Map<string, string>();
    private static _materialCache: Map<string, I_DesignMaterialInfo> = new Map<string, I_DesignMaterialInfo>();
    static styleBrushMap: Map<string, StyleBrush> = new Map<string, StyleBrush>();

    static async getStyleBrush(styleBrushId: string): Promise<StyleBrush> {
        let styleBrush: StyleBrush = null;
        if (MaterialService.styleBrushMap.has(styleBrushId)) {
            styleBrush = MaterialService.styleBrushMap.get(styleBrushId);
        } else {
            try {
                let postReqBody = {
                    styleBrushId: styleBrushId
                };

                const res : any = await BasicRequest.magiccubeDpAiWebRequest({
                    method: 'post',
                    url: `/dp-ai-web/getStyleBrush`,
                    data: {
                        ...postReqBody,
                    },
                    timeout: 60000,
                });

                if (res.success == false || res.data == null) {
                    return null;
                }

                if (res.data) {
                    styleBrush = res.data as StyleBrush;
                }
                if(styleBrush)
                {
                    if(styleBrush.items && styleBrush.items.length > 0)
                    {
                        MaterialService.styleBrushMap.set(styleBrushId, styleBrush);
                    }
                }
            } catch (e) {
            }
        }
        return styleBrush;
    }

    static async saveStyleBrush(styleBrushId: string, styleBrush:StyleBrush): Promise<boolean> {
        MaterialService.styleBrushMap.set(styleBrushId, styleBrush);
        try {
            let postReqBody = {
                styleBrushId: styleBrushId,
                styleBrush: styleBrush
            };

            const res = await BasicRequest.magiccubeDpAiWebRequest({
                method: 'post',
                url: `/dp-ai-web/saveStyleBrush`,
                data: {
                    ...postReqBody,
                },
                timeout: 60000,
            });

            if (res.data == true) {
                return true;
            } else {
                return false;
            }
        } catch (e) {
            return false;
        }
    }

    static async getGroupMaterialDetail(materialId: string): Promise<I_MaterialMatchingItem[]> {
        let memberMaterials: I_MaterialMatchingItem[] = [];

        if (MaterialService.groupMaterialDetailMap.has(materialId)) {
            memberMaterials = MaterialService.groupMaterialDetailMap.get(materialId);
        } else {
            try {
                let postReqBody = {
                    modelId: materialId
                };

                const res : any= await BasicRequest.magiccubeDpAiWebRequest({
                    method: 'post',
                    url: `/dp-ai-web/getGroupDetail`,
                    data: {
                        ...postReqBody,
                    },
                    timeout: 60000,
                });

                if (res.success == false || res.data == null) {
                    return [];
                }

                if (res.data.records != null) {
                    for (let record of res.data.records) {
                        let matchingItem: I_MaterialMatchingItem = this.getMatchItemFromRecord(record);
                        memberMaterials.push(matchingItem);
                    }
                }
                MaterialService.groupMaterialDetailMap.set(materialId, memberMaterials);
            } catch (e) {
            }
        }
        let memberMaterialsCopy: I_MaterialMatchingItem[] = [];
        if (memberMaterials.length > 0) {
            memberMaterials.forEach((mm) => {
                memberMaterialsCopy.push(cloneMaterialMatchingItem(mm));
            });
        }

        return memberMaterialsCopy;
    }

    static async getBatchGroupMaterialDetail(materialIds: string[]): Promise<Map<string, I_MaterialMatchingItem[]>> {
        let waitingGroupMaterialIds: string[] = [];
        let groupDetailList: Map<string, I_MaterialMatchingItem[]> = new Map();
        for (let materialId of materialIds) {
            if (!MaterialService.groupMaterialDetailMap.has(materialId)) {
                waitingGroupMaterialIds.push(materialId);
            } else {
                let memberMaterials = MaterialService.groupMaterialDetailMap.get(materialId);
                let memberMaterialsCopy: I_MaterialMatchingItem[] = [];
                memberMaterials.forEach((mm) => {
                    memberMaterialsCopy.push(cloneMaterialMatchingItem(mm));
                });
                groupDetailList.set(materialId, memberMaterialsCopy);
            }
        }

        if (waitingGroupMaterialIds.length > 0) {
            try {
                let postReqBody = {
                    modelIds: waitingGroupMaterialIds
                };

                const res : any= await BasicRequest.magiccubeDpAiWebRequest({
                    method: 'post',
                    url: `/dp-ai-web/getBatchGroupDetail`,
                    data: {
                        ...postReqBody,
                    },
                    timeout: 60000,
                });

                if (res.success && res.data != null) {
                    for (let groupDetail of res.data) {
                        let groupMaterialId = groupDetail.groupId;
                        let memberMaterials: I_MaterialMatchingItem[] = [];
                        for (let memberMaterial of groupDetail.records) {
                            let matchingItem: I_MaterialMatchingItem = this.getMatchItemFromRecord(memberMaterial);
                            memberMaterials.push(matchingItem);
                        }

                        let memberMaterialsCopy: I_MaterialMatchingItem[] = [];
                        memberMaterials.forEach((mm) => {
                            memberMaterialsCopy.push(cloneMaterialMatchingItem(mm));
                        });
                        groupDetailList.set(groupMaterialId, memberMaterialsCopy);
                        MaterialService.groupMaterialDetailMap.set(groupMaterialId, memberMaterials);
                    }
                }
            } catch (e) {
            }
        }

        return groupDetailList;
    }

    private static getMatchItemFromRecord(record: any): I_MaterialMatchingItem {
        let matchingItem = {
            name: record.name,
            type: record.type,
            modelLoc: record.modelLoc,
            length: record.length,
            width: record.width,
            height: record.height,
            originalLength: record.originalLength,
            originalWidth: record.originalWidth,
            originalHeight: record.originalHeight,
            modelId: record.modelId,
            imageUrl: record.imageUrl,
            targetRotation: { x: 0, y: 0, z: record.rotateZ },
            targetPosition: { x: record.x, y: record.y, z: record.z },
            topViewImage: record.topViewImage
        } as I_MaterialMatchingItem;
        return matchingItem;
    }

    static cacheGroupMaterialTopViewImage(materialId: string, topViewImage: string) {
        if (topViewImage == null || topViewImage.length == 0 || materialId == null || materialId.length == 0) {
            return;
        }

        MaterialService.materialTopViewImageMap.set(materialId, topViewImage);
    }

    static async match3dPreviewMaterials(figureElementList: I_FigureElement[],
        serialKgId: number, seriesName: string, seedSchemeId: string, enterpriseId: string, roomName: string, roomArea: number, traceId: string): Promise<I_MaterialMatchingItem[]> {

        let layoutList = [];
        for (let listIndex = 0; listIndex < figureElementList.length; listIndex++) {
            let figureElement = figureElementList[listIndex];
            let feShape = (figureElement as any).figure_shape || figureElement.shape;
            let modelLoc = figureElement.modelLoc;
            if (modelLoc == "Default") {
                continue;
            }
            let publicCategoryName = figureElement.public_category;
            if (publicCategoryName === modelLoc) {
                publicCategoryName = null;
            }
            let layoutParam = {
                length: figureElement.rect.w,
                width: figureElement.rect.h,
                height: figureElement.params.height,
                index: listIndex,
                layoutRule: figureElement.params.off_land_rule,
                modelLoc: modelLoc,
                publicCategoryName: publicCategoryName,
                shape: feShape,
                closeDirection: figureElement.params.close_direction
            };
            if (figureElement.rect.cornerWidth != null) {
                (layoutParam as any)["cornerWidth"] = figureElement.rect.cornerWidth;
            }
            if (figureElement.rect.cornerDepth != null) {
                (layoutParam as any)["cornerDepth"] = figureElement.rect.cornerDepth;
            }
            layoutList.push(layoutParam)
        }

        let postReqBody = {
            seedSchemeId: seedSchemeId,
            traceId: traceId,
            roomArea: roomArea,
            seriesKgId: serialKgId,
            appId: 1,
            seriesName: seriesName,
            enterpriseId: enterpriseId,
            roomName: roomName,
            layouts: layoutList
        }

        try {
            const res : any= await BasicRequest.magiccubeDpAiWebRequest({
                method: 'post',
                url: `/dp-ai-web/materialMatching`,
                data: {
                    ...postReqBody,
                },
                timeout: 60000,
            });

            if (res.success == false || res.data == null) {
                return [];
            }


            const matchedMaterials = res.data.materials as I_MaterialMatchingItem[];
            const resultMaterials: I_MaterialMatchingItem[] = [];

            const supplement = (material: I_MaterialMatchingItem, figureElement: I_FigureElement, feIndex: number) => {
                material.figureElement = figureElement;
                material.seriesKgId = serialKgId;
                material.index = feIndex;
                if (!material.applySpace) {
                    material.applySpace = [roomName];
                }
                if (figureElement.params.topOffSet && figureElement.params.topOffSet > 0) {
                    material.topOffset = figureElement.params.topOffSet;
                }
                return material;
            };

            for (let material of matchedMaterials) {
                let feIndex: number = material["index"];
                let fe: I_FigureElement = figureElementList[feIndex];
                if (fe == null) {
                    console.error("Error: invalid index value for allFigureElements.")
                    continue;
                }
                (fe as any)._3dpreview_matched_material = material;
                supplement(material, fe, feIndex);

                if ((fe as any)._3dpreview_matched_material == null) {
                    continue;
                }

            }

            return resultMaterials;
        } catch (error) {
            console.log(error);
            return [];
        }
    }

    static async materialMatching(figureElementList: I_FigureElement[],
        serialKgId: number, seriesName: string, seedSchemeId: string, enterpriseId: string, roomName: string, roomArea: number, traceId: string): Promise<I_MaterialMatchingItem[]> {
        let layoutList = [];
        for (let listIndex = 0; listIndex < figureElementList.length; listIndex++) {
            let figureElement = figureElementList[listIndex];
            let feShape = (figureElement as any).figure_shape || figureElement.shape;
            let modelLoc = figureElement.modelLoc;
            if (modelLoc == "Default") {
                continue;
            }
            let publicCategoryName = figureElement.public_category;
            if (publicCategoryName === modelLoc) {
                publicCategoryName = null;
            }
            let layoutParam = {
                length: figureElement.rect.w,
                width: figureElement.rect.h,
                height: figureElement.params.height,
                index: listIndex,
                layoutRule: figureElement.params.off_land_rule,
                modelLoc: modelLoc,
                publicCategoryName: publicCategoryName,
                shape: feShape,
                closeDirection: figureElement.params.close_direction
            };
            if (figureElement.rect.cornerWidth != null) {
                (layoutParam as any)["cornerWidth"] = figureElement.rect.cornerWidth;
            }
            if (figureElement.rect.cornerDepth != null) {
                (layoutParam as any)["cornerDepth"] = figureElement.rect.cornerDepth;
            }
            layoutList.push(layoutParam)
        }

        let postReqBody = {
            seedSchemeId: seedSchemeId,
            traceId: traceId,
            roomArea: roomArea,
            seriesKgId: serialKgId,
            appId: 1,
            seriesName: seriesName,
            enterpriseId: enterpriseId,
            roomName: roomName,
            layouts: layoutList
        }

        try {
            const res : any= await BasicRequest.magiccubeDpAiWebRequest({
                method: 'post',
                url: `/dp-ai-web/materialMatching`,
                data: {
                    ...postReqBody,
                },
                timeout: 60000,
            });
            const ceilingConfig = await MaterialService._getCeilingMatConfig();

            if (res.success == false || res.data == null) {
                return [];
            }

            const cabinetStyleId = res.data.cabinetStyleId;
            if (cabinetStyleId != null && cabinetStyleId.length > 0) {
                MaterialService.cabinetStyleIdMap.set(serialKgId.toString(), cabinetStyleId);
            }

            const matchedMaterials = res.data.materials as I_MaterialMatchingItem[];
            const resultMaterials: I_MaterialMatchingItem[] = [];

            const supplement = (material: I_MaterialMatchingItem, figureElement: I_FigureElement, feIndex: number) => {
                material.figureElement = figureElement;
                material.seriesKgId = serialKgId;
                material.index = feIndex;
                if (!material.applySpace) {
                    material.applySpace = [roomName];
                }
                if (figureElement.params.topOffSet && figureElement.params.topOffSet > 0) {
                    material.topOffset = figureElement.params.topOffSet;
                }
                return material;
            };

            let groupMaterialIdSet = new Set<string>();
            for (let material of matchedMaterials) {
                if (isGroupMaterialMatchingItem(material)) {
                    groupMaterialIdSet.add(material.modelId);
                }
                if (material.candidate) {
                    for (let candidate of material.candidate) {
                        if (isGroupMaterialMatchingItem(candidate)) {
                            groupMaterialIdSet.add(candidate.modelId);
                        }
                    }
                }
            }
            let groupMaterialDetailMap = await MaterialService.getBatchGroupMaterialDetail(Array.from(groupMaterialIdSet));

            for (let material of matchedMaterials) {
                let feIndex: number = material["index"];
                let fe = figureElementList[feIndex];
                if (fe == null) {
                    console.error("Error: invalid index value for allFigureElements.")
                    continue;
                }
                fe._matched_material = material;
                supplement(material, fe, feIndex);
                fe._candidate_materials = [];
                if (material.candidate && material.candidate.length > 0) {
                    if (material.modelLoc === "吊顶" && material.candidate.length === 1) {
                        supplement(material.candidate[0], fe, feIndex);
                        await this._insertCeilingCandidateMat(fe, (fe as any)?._room?.standardName || (fe as any)?._room?.roomname, material.candidate[0], ceilingConfig);
                    } else {
                        for (let candidate of material.candidate) {
                            supplement(candidate, fe, feIndex);
                            fe._candidate_materials.push(candidate);
                        }
                    }
                } else {
                    fe._candidate_materials = [material];
                }

                if (fe._matched_material == null) {
                    continue;
                }

                const shouldSortAgain = !["衣柜", "电视柜", "餐边柜", "玄关柜", "书柜", "床", "背景墙", "沙发背景墙", "电视背景墙", "地面"].includes(fe.modelLoc);
                if (shouldSortAgain) {
                    if (fe.modelLoc.indexOf("组合") >= 0) {
                        let currentGroupDetailMap = new Map<I_MaterialMatchingItem, I_MaterialMatchingItem[]>();
                        if (isGroupMaterialMatchingItem(fe._matched_material)) {
                            let matchedMaterialId = fe._matched_material.modelId;
                            if (groupMaterialDetailMap.has(matchedMaterialId)) {
                                currentGroupDetailMap.set(fe._matched_material, groupMaterialDetailMap.get(matchedMaterialId));
                            }
                            fe._candidate_materials.forEach((candidate) => {
                                if (isGroupMaterialMatchingItem(candidate) && candidate.modelId != matchedMaterialId) {
                                    currentGroupDetailMap.set(candidate, groupMaterialDetailMap.get(candidate.modelId));
                                }
                            });
                        }
                        if (currentGroupDetailMap.size > 0) {
                            // TMatchingOrdering.instance.sortGroupMaterials(fe, currentGroupDetailMap, roomName);
                        }
                    } else {
                        // TMatchingOrdering.instance.sortMaterialsOfFigureElement(fe, fe._candidate_materials, roomName);
                        // fe._matched_material = fe._candidate_materials[0];
                    }
                }

                if (fe._matched_material) {
                    fe._matched_material.candidate = fe._candidate_materials;
                    resultMaterials.push(fe._matched_material);
                }

            }

            for (let listIndex = 0; listIndex < figureElementList.length; listIndex++) {
                let figureElement = figureElementList[listIndex];
                if (figureElement.modelLoc == "地面" && !figureElement._matched_material) {
                    let modelId: string = null;
                    let modelFlag: string = null;
                    let modelWidth: number = 0;
                    let modelHeight: number = 0;
                    let imageUrl: string = null;

                    if (roomName.indexOf("厅") > -1 || roomName.indexOf("阳台") > -1) {
                        modelId = "185638302";
                        modelFlag = "2";
                        modelWidth = 800;
                        modelHeight = 800;
                        imageUrl = "https://img3.admin.3vjia.com/UpFile/C00000022/PMC/DesignMaterial/202302/a3d645241ba241e4838b6a6f2dc5d75e/无缝金意陶浅灰色连纹大理石（客厅）.jpeg";
                    } else if (roomName.indexOf("厨房") > -1 || roomName.indexOf("卫") > -1) {
                        modelId = "185640845";
                        modelFlag = "2";
                        modelWidth = 800;
                        modelHeight = 800;
                        imageUrl = "https://img3.admin.3vjia.com/UpFile/C00000022/PMC/DesignMaterial/202302/a92e6543cce34593ac272bdce0f109a0/无缝深灰色大理石（厨卫）.jpg";
                    } else {
                        modelId = "185639178";
                        modelFlag = "2";
                        modelWidth = 800;
                        modelHeight = 800;
                        imageUrl = "https://img3.admin.3vjia.com/UpFile/C00000022/PMC/DesignMaterial/202302/505de24f284b4eada7afbd8fc6d9461f/无缝原木木地板-220.png";
                    }

                    figureElement._matched_material = {
                        modelId: modelId,
                        modelLoc: figureElement.modelLoc,
                        publicCategoryName: figureElement.modelLoc,
                        length: (figureElement as any)._room?._room_entity?.floor_thickness || 0,
                        width: modelWidth,
                        height: modelHeight,
                        imageUrl: imageUrl,
                        targetPosition: Vec3toMeta(figureElement.rect.rect_center_3d),
                        targetSize: { length: 0, width: modelWidth, height: modelWidth },
                        roomUid: "",
                        originalLength: 0,
                        originalWidth: modelWidth,
                        originalHeight: modelHeight,
                        modelFlag: modelFlag,
                        figureElement: figureElement,
                        index: listIndex
                    } as any as I_MaterialMatchingItem;
                }
            }

            return resultMaterials;
        } catch (error) {
            console.log(error);
            return [];
        }
    }
    private static async _insertCeilingCandidateMat(fe: I_FigureElement, stdRoomName: string, selectedMaterial: I_MaterialMatchingItem, ceilingConfig: any): Promise<void> {
        if (ceilingConfig == null) return;

        let ceilingMatList = null;
        for (let i = 0; i < ceilingConfig.length; i++) {
            let configItem = ceilingConfig[i];
            if (stdRoomName === configItem.roomName) {
                ceilingMatList = configItem.defceling;
            }
        }
        if (ceilingMatList == null) return;

        const newMaterialList: I_MaterialMatchingItem[] = [];
        for (let i = 0; i < ceilingMatList.length; i++) {
            let configItem = ceilingMatList[i];
            // 创建新的素材项，基于选中的素材
            let newMatItem: I_MaterialMatchingItem = {
                ...selectedMaterial,
                imageUrl: configItem.imagePath,
                modelId: configItem.materialId,
                name: configItem.materialName,
                organizationId: configItem.organId,
            };
            newMaterialList.push(newMatItem);
        }

        // 检查选中的材质是否在新材质列表中
        const selectedIndex = newMaterialList.findIndex(mat => mat.modelId === selectedMaterial.modelId);

        if (selectedIndex === -1) {
            // 如果选中的材质不在列表中，找到默认材质并移到最前面
            // todo建议后面这个默认材质都在后端配置好
            let defaultMaterialId = "162169383";
            if (compareNames([(fe as any)._room.roomname || "---"], ["阳台", "厨房", "卫生间"])) {
                defaultMaterialId = "131827360";
            }
            const defaultIndex = newMaterialList.findIndex(mat => mat.modelId === defaultMaterialId);
            if (defaultIndex !== -1) {
                const defaultMaterial = newMaterialList[defaultIndex];
                newMaterialList.splice(defaultIndex, 1);
                newMaterialList.unshift(defaultMaterial);
                // 设置为匹配的材质
                fe._matched_material = defaultMaterial;
            }
        } else {
            // 如果在列表中，将该材质移到列表开头
            const matchingMaterial = newMaterialList[selectedIndex];
            newMaterialList.splice(selectedIndex, 1);
            newMaterialList.unshift(matchingMaterial);
        }
        // 将材质列表设置为候选材质
        fe._candidate_materials = [...newMaterialList];
    }

    /**
 * 获取吊顶素材配置
 * @returns Promise<any> 返回吊顶素材配置信息
 */
    private static async _getCeilingMatConfig(): Promise<any> {
        try {
            const ceilingConfigApiRequest = createProRequest();
            ceilingConfigApiRequest.defaults.withCredentials = false;
            ceilingConfigApiRequest.defaults.baseURL = RequestHosts.panoHost;
            ceilingConfigApiRequest.defaults.headers = {
                'Content-Type': 'application/json',
            };

            const response = await ceilingConfigApiRequest({
                method: 'get',
                url: '/vr/config/aidesk/aideskcelings.json'
            });

            // 直接返回响应数据
            return response;
        } catch (error) {
            console.error('获取吊顶素材配置失败:', error);
            return null;
        }
    }


    static async styleSuit(figureElementList: I_FigureElement[],
        serialKgId: number, seriesName: string, seedSchemeId: string, enterpriseId: string, roomName: string, roomArea: number, traceId: string): Promise<[I_MaterialMatchingItem[], String]> {
        let layoutList = [];
        for (let listIndex = 0; listIndex < figureElementList.length; listIndex++) {
            let figureElement = figureElementList[listIndex];
            let feShape = null;
            let modelLoc = figureElement.modelLoc;
            if (figureElement.modelLoc == "淋浴房") {
                if (figureElement.params.depth < 300) {
                    feShape = "一字形";
                }
            } else if (figureElement.modelLoc == "矩形淋浴房") {
                modelLoc = "淋浴房";
                feShape = "矩形";
            } else if (figureElement.modelLoc == "一字形淋浴房") {
                modelLoc = "淋浴房";
                feShape = "一字形";
            }
            layoutList.push(
                {
                    length: parseFloat(figureElement.rect.w.toFixed(2)),
                    width: parseFloat(figureElement.rect.h.toFixed(2)),
                    height: parseFloat(figureElement.params.height.toFixed(2)),
                    minLength: null as number,
                    index: listIndex,
                    layoutRule: null as number,
                    type: modelLoc,
                    shape: feShape,
                    angularDirection: null as number,
                }
            )
        }

        let postReqBody = {
            seedSchemeId: seedSchemeId,
            traceId: traceId,
            area: roomArea,
            isDecoration: false,
            styleId: serialKgId,
            appId: 0,
            style: seriesName,
            onlySingle: false,
            enterpriseId: enterpriseId,
            room: roomName,
            layouts: layoutList,
            limitRule: true
        }

        const styleSuitApiUrl = `/dp-ai-web/styleSuit`;
        let respTraceId: string = null;

        try {
            const res : any = await BasicRequest.magiccubeDpAiWebRequest({
                method: 'post',
                url: styleSuitApiUrl,
                data: {
                    ...postReqBody,
                },
                timeout: 60000,
            });

            respTraceId = res.data?.traceId;

            if (res.success === undefined) {
                console.error("Backend API Error: " + styleSuitApiUrl + "\n"
                    + (res.code ? res.code : "") + " " + (res.msg ? res.msg : ""));
                return [[], respTraceId];
            }

            if (res.success == false) {
                return [[], respTraceId];
            }

            const matchedMaterials: I_MaterialMatchingItem[] = [];
            for (let objKey in res.data.furnitures) {
                let index: number = parseInt(objKey);
                let furnitureArray = res.data.furnitures[objKey];
                if (furnitureArray === null || furnitureArray.length == 0) {
                    continue;
                }
                let fe = figureElementList[index];
                if (fe == null) {
                    console.error("Error: invalid index value for allFigureElements.")
                    continue;
                }
                for (let r: number = 0; r < furnitureArray.length; r++) {
                    let firstFurniture = furnitureArray[r];
                    let material: I_MaterialMatchingItem = firstFurniture as I_MaterialMatchingItem;
                    material.modelFlag = firstFurniture.modelflag;
                    material.modelLoc = firstFurniture.modellocation;
                    material.organizationId = firstFurniture.organizeId;
                    material.seriesKgId = firstFurniture.styleid;
                    material.figureElement = fe;
                    material.index = index;
                    matchedMaterials.push(material);
                }
            }
            return [matchedMaterials, respTraceId];
        } catch (error) {
            console.log(error);
            return [[], respTraceId];
        }
    }

    static async getMaterialInfo(materialIds: string[]): Promise<I_MaterialInfo[]> {
        let postReqBody = {
            ids: materialIds
        }

        let materialInfos: I_MaterialInfo[] = [];

        try {
            const res : any = await BasicRequest.magiccubeDpAiWebRequest({
                method: 'post',
                url: `/dp-ai-web/getMaterialInfo`,
                data: {
                    ...postReqBody,
                },
                timeout: 60000,
            });

            if (res.success == false) {
                return [];
            }

            const materialInfos = res.data.result as I_MaterialInfo[];
            return materialInfos;
        } catch (error) {
            console.log(error);
            return [];
        }
    }

    static async finishedProductGlobalSearch(searchInfo: I_GlobalSearchInfo, categoryId: string): Promise<any> {
        const res:any = await BasicRequest.openApiRequest({
            method: 'post',
            url: `/api/sdapi/designmaterial/getDesignMaterialListByCategoryIdWithAuthority`,
            data: {
                pageIndex: searchInfo.index,
                pageSize: searchInfo.size,
                materialName: searchInfo.name,
                categoryId: categoryId
            },
            headers: {
                'Content-Type': 'application/json'
            },
            withCredentials: true,
            timeout: 60000,
        });
        return res;
    }

    static async customizedProductGlobalSearch(searchInfo: I_GlobalSearchInfo, categoryId: string): Promise<any> {
        const res : any= await BasicRequest.openApiRequest({
            method: 'post',
            url: `/api/sdapi/designmaterial/getDesignmaterialListByCategoryIdWithRelationByEs`,
            data: {
                pageIndex: searchInfo.index,
                pageSize: searchInfo.size,
                materialName: searchInfo.name,
                categoryId: categoryId
            },
            headers: {
                'Content-Type': 'application/json'
            },
            withCredentials: true,
            timeout: 60000,
        });
        return res;
    }

    static async platformGlobalSearch(searchInfo: I_GlobalSearchInfo, categoryId: string): Promise<any> {
        const res : any= await BasicRequest.magiccubeRequest({
            method: 'post',
            url: `sd-material/api/cloudMaterial/v1/search`,
            data: {
                current: searchInfo.index,
                size: searchInfo.size,
                materialName: searchInfo.name,
                categoryId: categoryId
            },
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 60000,
        });
        return res;
    }

    static async searchMaterial(searchInfo: I_MaterialSearchInfo): Promise<I_MaterialSearchResult> {
        try {
            const res : any = await BasicRequest.magiccubeDpAiWebRequest({
                method: 'post',
                url: `dp-ai-web/searchCloud3dMaterial`,
                headers: {
                    'Content-Type': 'application/json'
                },
                data: { ...searchInfo },
                timeout: 60000,
            });
            console.log('searchMaterial res', res);
            return res;
        } catch (error) {
            console.log(error);
            return null;
        }
    }

    static _cacheMaterials(materialInfos: I_DesignMaterialInfo[]) {
        materialInfos.forEach(material => {
            this._materialCache.set(material.MaterialId, material);
        });
    }

    static _isAllMaterialsCached(materialIds: string[]): boolean {
        return materialIds.every(materialId => this._materialCache.has(materialId));
    }

    static _getMaterialsFromCache(materialIds: string[]): I_DesignMaterialInfo[] {
        return materialIds.map(materialId => this._materialCache.get(materialId));
    }

    static async getDesignMaterialInfoByIds(materialIds: string[]): Promise<I_DesignMaterialInfo[]> {
        let postReqBody = {
            materialIds: materialIds.join(",")
        }

        try {
            const res : any = await BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/sdapi/designmaterial/getDesignMaterialByIdsWithOutPlaceheights`,
                data: {
                    ...postReqBody,
                },
                timeout: 60000,
            });

            if (res.success == false) {
                return [];
            }
            const materialInfos = res.result?.result as I_DesignMaterialInfo[] || [];
            return materialInfos;
        } catch (error) {
            console.log(error);
            return [];
        }
    }
    static async uploadLog(traceId: string, type: string, level: string, context: string, logs: string[]): Promise<Boolean> {
        let postReqBody = {
            type: type,
            level: level,
            traceId: traceId,
            context: context,
            logs: logs
        }

        try {
            const res : any = await BasicRequest.magiccubeDpAiWebRequest({
                method: 'post',
                url: `/dp-ai-web/uploadLog`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            });
            return res.data;
        } catch (error) {
            console.error(error);
            return false;
        }
    }

    static async submitLayoutIssueReport(issueId: string, issueList: any[], schemeId: string, houseArea: number, houseJson: string): Promise<any> {
        let postReqBody = {
            issueId: issueId,
            issueList: issueList,
            schemeId: schemeId,
            area: houseArea,
            schemeJson: houseJson,
            solved: 0
        }

        try {
            const res : any = await BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/LayoutTemplateError/saveErrorMsg`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            });
            return res;
        } catch (error) {
            console.error(error);
            return false;
        }
    }

    static async markSolvedLayoutIssueReport(issueId: string, remark: string): Promise<any> {
        let postReqBody = {
            id: issueId,
            solved: 1,
            isDelete: 0,
            remark: remark
        }

        try {
            const res : any = await BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/LayoutTemplateError/edit`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            });
            return res;
        } catch (error) {
            console.error(error);
            return null;
        }
    }

    static async deleteLayoutIssueReport(id: string): Promise<any> {
        let postReqBody = {
            id: id,
            isDelete: 1
        }

        try {
            const res : any = await BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/LayoutTemplateError/edit`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            });
            return res;
        } catch (error) {
            console.error(error);
            return false;
        }
    }

    static async fetchLayoutIssueReportList(issueStatus: number): Promise<any> {
        let postReqBody = {
            solved: issueStatus,
            pageIndex: 1,
            pageSize: 100
        }

        try {
            const res : any = await BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/LayoutTemplateError/listByPage`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            });
            return res?.result?.result;
        } catch (error) {
            console.error(error);
            return null;
        }
    }

    static uploadRulesJson(rulesJson: string, versionTip: string, onSuccess: () => void, onFail: () => void): void {
        let postReqBody = {
            cfgContent: rulesJson,
            desc: versionTip,
            enable: 0
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/save`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response: any) => {
                if (!response || !response.success) {
                    console.error("Fail to upload rules json to server.");
                    onFail();
                } else {
                    onSuccess();
                }
            }).catch((e: any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static downloadLayoutRule(ruleVersionId: number, onSuccess: (json: any) => void, onFail: () => void): void {
        let postReqBody = {
            id: ruleVersionId
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/get`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response: any) => {
                if (response && response.success && response.result && response.result.configUrl) {
                    fetch(response.result.configUrl)
                        .then((configResponse: any) => {
                            if (configResponse) {
                                configResponse.json().then((json: any) => {
                                    onSuccess(json);
                                }).catch((e: any) => {
                                    console.error(e);
                                    onFail();
                                });
                            }
                        })
                        .catch((e: any) => {
                            console.error(e);
                            onFail();
                        });
                } else {
                    console.error("Fail to get rules json from server.");
                    onFail();
                }
            })
                .catch((e: any) => {
                    console.error(e);
                    onFail();
                });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static peekRulesJson(onSuccess: (json: any[]) => void, onFail: () => void): void {
        let postReqBody = {
            isDelete: 0,
            orderBy: "create_date desc",
            "pageIndex": 1,
            "pageSize": 100
        };

        try {
           BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/listByPage`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response: any) => {
                if (response && response.success && response.result.result) {
                    onSuccess(response.result.result);
                } else {
                    console.error("Fail to get rules json from server.");
                    onFail();
                }
            })
                .catch((e: any) => {
                    console.error(e);
                    onFail();
                });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static getCurrentVersionLayoutRule(onSuccess: (json: any[]) => void, onFail: () => void): void {
        let postReqBody = {
            isDelete: 0,
            enable: 1,
            orderBy: "create_date desc",
            "pageIndex": 1,
            "pageSize": 100
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/listByPage`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response: any) => {
                if (response && response.success && response.result.result) {
                    onSuccess(response.result.result);
                } else {
                    console.error("Fail to get rules json from server.");
                    onFail();
                }
            })
                .catch((e: any) => {
                    console.error(e);
                    onFail();
                });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static applyRuleVersion(ruleVersion: number, onSuccess: () => void, onFail: () => void): void {
        let postReqBody = {
            id: ruleVersion
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/enableConfig`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response: any) => {
                if (!response || !response.success) {
                    console.error("Fail to apply rule version.");
                    onFail();
                } else {
                    onSuccess();
                }
            }).catch((e: any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static deleteRuleVersion(ruleVersion: number, onSuccess: () => void, onFail: () => void): void {
        let postReqBody = {
            id: ruleVersion,
            enable: 0,
            isDelete: 1
        };

        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/layoutGlobalConfig/edit`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response: any) => {
                if (!response || !response.success) {
                    console.error("Fail to apply rule version.");
                    onFail();
                } else {
                    onSuccess();
                }
            }).catch((e: any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    // 获取套系对应的所有素材
    static getSeriesAllMaterial(seriesId: string, onSuccess: (json: any[]) => void, onFail: () => void): void {
        let postReqBody = {
            seriesId: seriesId
        };
        try {
            BasicRequest.openApiRequest({
                method: 'post',
                url: `/api/njvr/KgMaterial/listSeriesMaterials`,
                data: {
                    ...postReqBody,
                },
                timeout: 30000,
            }).then((response: any) => {
                if (response && response.success && response.result) {
                    onSuccess(response.result);
                } else {
                    console.error("Fail to get series material from server.");
                    onFail();
                }
            }).catch((e: any) => {
                console.error(e);
                onFail();
            });
        } catch (error) {
            console.error(error);
            onFail();
        }
    }

    static async getSeriesMaterialByModelLoc(ruleId:string,modelLoc:string)
    {
        let postReqBody = {
            ruleId : ruleId,
            modelloc: modelLoc,
            pageIndex: 1,
            pageSize: 500
        };
        let res : any= await BasicRequest.openApiRequest({
            method: 'post',
            url: `/api/njvr/KgMaterial/pageKgMagerial`,
            data: {
                ...postReqBody,
            },
            timeout: 30000,
        }).catch((e:any):any=>{
            return null;
        });

        if(res?.success && res?.result?.result)
        {
            return res.result.result as I_KgMaterialItem[];
        }else{
            return [];
        }
    }

    static matchKgMaterailItemsOnFigureEle(figureElement:I_FigureElement,items:I_KgMaterialItem[])
    {
        items = [...items];

        let score = (item:I_KgMaterialItem)=>{
            return Math.abs(item.length - ((figureElement as any).length||figureElement.params.length))
        }
        items.sort((a,b)=>score(a)-score(b));

        if(items[0])
        {
            let item = items[0];
            figureElement._matched_material = {
                ...items[0],
                modelId : item.materialId,
                targetPosition: Vec3toMeta(figureElement.rect.rect_center_3d),
                targetSize: { length: item.length, width: item.width, height: item.height },
                targetRotation: { x: 0, y: 0, z: figureElement.rect.rotation_z },
                originalLength : item.length,
                originalWidth : item.width,
                originalHeight : item.height,
                candidate:[]
            } as any;

            (figureElement as any).matched_rect = figureElement.rect.clone();
            (figureElement as any).matched_rect.length = item.length;
            (figureElement as any).matched_rect.depth = item.width;
            (figureElement as any).matched_rect.rect_center = figureElement.rect.rect_center;
        }
    }
}
